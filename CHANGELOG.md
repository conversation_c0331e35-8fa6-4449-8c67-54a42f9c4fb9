# Changelog

## [4.10.0] - 2025-01-10
### Added
- **Advanced Search & Replace System**: Complete database search and replace functionality with security features
  - Comprehensive search and replace across all WordPress database tables with filtering capabilities
  - Table selection interface with "Select All", "Core Tables Only", and "Deselect All" buttons
  - Advanced search options: case insensitive search, dry run preview, and whole words matching
  - Real-time table selection UI with smooth animations and visual feedback
  - Detailed results reporting with affected posts, modification statistics, and change previews
  - Enhanced security with input sanitization, nonce verification, and user capability checks
  - Professional styling with checkboxes, progress indicators, and organized result display

- **Lorem Ipsum Detector Tool**: Professional content scanning system for placeholder text detection
  - Complete WordPress site scanning for Lorem Ipsum placeholder text across all content types
  - Advanced detection algorithms with configurable sensitivity levels and pattern matching
  - Comprehensive results display with affected posts, locations, and action buttons
  - Direct edit/view links for quick content remediation
  - Professional icon integration matching overall design system
  - Real-time scanning progress with detailed status updates

- **Development Mode System**: Enhanced debugging and development workflow tools
  - Admin bar colorization system for development environment identification
  - Memory bank documentation system for project knowledge management
  - Enhanced developer experience with visual environment indicators

### Improved
- **Security & Access Control UI**: Redesigned warning system with enhanced visual appeal
  - Replaced bright orange background with elegant dark surface styling
  - Added distinctive 4px dashed orange border for visual emphasis while maintaining readability
  - Enhanced code element styling with proper contrast and bordered backgrounds
  - Improved text readability with optimized color schemes for dark backgrounds
  - Maintained security emphasis while reducing visual overwhelm

- **Search & Replace Interface**: Complete UI/UX overhaul with modern design patterns
  - Fixed missing checkboxes for search options with proper visual styling
  - Enhanced table selection with visual feedback and smooth state transitions
  - Added comprehensive form validation and error handling
  - Implemented consistent styling across all interactive elements
  - Professional checkbox design with custom styling and hover effects

- **Icon System Consistency**: Complete visual icon integration across all tabs
  - Added professional search/document icon for Lorem Ipsum Detector tab
  - Consistent icon sizing, coloring, and hover/active states across all tabs
  - Enhanced visual hierarchy and navigation clarity

### Fixed
- **Checkbox Rendering Issues**: Resolved missing checkboxes in search and replace form
  - Fixed CSS specificity conflicts affecting checkbox visibility
  - Implemented targeted CSS rules to ensure proper checkbox display
  - Resolved duplicate checkbox issues in table selection interface
  - Added fallback styling for maximum compatibility across different contexts

- **Table Selection Conflicts**: Eliminated duplicate checkboxes in database table selection
  - Fixed CSS targeting to prevent interference between different checkbox systems
  - Ensured table selection checkboxes work independently from search options
  - Maintained proper visual distinction between different checkbox types

### Technical
- **Database Search Architecture**: Implemented enterprise-grade search and replace system
  - Built comprehensive table filtering with WordPress core table detection
  - Added recursive search capabilities for serialized data structures
  - Implemented memory-efficient batch processing for large databases
  - Enhanced error handling and recovery mechanisms for database operations

- **Memory Bank System**: Established comprehensive project documentation framework
  - Created structured knowledge management system for development workflows
  - Implemented decision tracking and pattern documentation
  - Added comprehensive troubleshooting guides and feature documentation

- **CSS Architecture**: Enhanced styling system with improved organization
  - Added scoped CSS targeting to prevent conflicts between different systems
  - Implemented consistent design tokens across all new components
  - Enhanced responsive design patterns for optimal cross-device compatibility

## [4.9.3] - 2025-01-17
### Added
- **Visual Notes Indicator**: Enhanced Notes admin bar icon with purple background when notes exist
  - Added server-side detection system to check if user has any notes content across all storage layers
  - Implemented dynamic CSS class application based on notes presence (`slmm-notes-has-content`)
  - Added purple background (#7C3AED) with white icon and text when notes exist
  - Included real-time JavaScript updates that change icon appearance immediately as user types
  - Enhanced tooltip text to show "Notes available - Click to open" when notes exist
  - Added comprehensive content validation that strips HTML and whitespace to detect meaningful content
  - Implemented hover effect with lighter purple background (#9F7AEA) for better user interaction

### Improved
- **Notes User Experience**: Enhanced visual feedback and discoverability
  - Icon now provides instant visual indication of notes status without opening popup
  - Real-time color updates provide immediate feedback while editing notes
  - Consistent styling across all admin contexts (WordPress admin, Bricks Builder)
  - Added rounded corners (4px border-radius) for polished appearance
  - Enhanced accessibility with clear visual states and improved tooltips

### Technical
- Added `user_has_notes()` method with comprehensive backup source checking
- Implemented `updateAdminBarIcon()` JavaScript function for real-time updates
- Enhanced CSS specificity with proper admin bar targeting
- Added content validation logic that checks primary storage, version-independent backup, structured backup, and global backup
- Optimized performance with efficient content checking algorithms

## [4.9.2] - 2025-01-17
### Fixed
- **Notes Multi-Tab Conflict Resolution**: Resolved critical issues with notes overwriting each other across multiple tabs
  - Implemented comprehensive conflict detection system with timestamp-based validation
  - Added automatic conflict resolution by appending conflicting versions with clear separation markers
  - Prevented concurrent save operations to eliminate race conditions
  - Added periodic sync checking every 30 seconds to detect changes from other tabs
  - Enhanced server-side validation to detect and resolve conflicts before data loss occurs

- **Bricks Builder Notes Integration**: Removed confusing save-like button from Bricks Builder toolbar
  - Removed `slmm-notes-bricks-button` that resembled a save button and caused user confusion
  - Eliminated duplicate notes interfaces in Bricks Builder context
  - Notes now only accessible via WordPress admin bar icon across all contexts
  - Simplified Bricks integration to focus solely on existing functionality without UI conflicts

- **Notes Persistence System**: Enhanced data storage to prevent notes from disappearing
  - Implemented multi-layer backup system: primary user_meta, structured options backup, version-independent backup, and global backup
  - Added automatic recovery system that restores notes from multiple backup sources if primary storage fails
  - Enhanced metadata tracking with timestamps, save counts, and conflict resolution history
  - Added comprehensive error logging and debugging for troubleshooting storage issues
  - Improved data validation and integrity checking to prevent corruption

### Improved
- **Multi-Tab User Experience**: Enhanced handling of notes across multiple browser tabs
  - Added visual indicators for sync conflicts and automatic resolution status
  - Implemented smart conflict resolution that preserves all content with clear conflict markers
  - Added status indicators: "Modified", "Saving...", "✓ Saved", "⚠️ Conflict resolved", "🔄 Synced with server"
  - Enhanced auto-save functionality with conflict detection integration
  - Added client-side timestamp tracking for better conflict detection

- **Notes Reliability**: Strengthened notes system architecture for enterprise-level reliability
  - Added save operation queuing to prevent data corruption during rapid changes
  - Implemented graceful degradation when storage systems fail
  - Enhanced backup validation and automatic restoration capabilities
  - Added comprehensive error handling with fallback mechanisms
  - Improved memory management and cleanup for better performance

### Technical
- Enhanced JavaScript conflict resolution with timestamp validation
- Implemented server-side conflict detection with automatic content merging
- Added comprehensive backup layer system for maximum data preservation
- Improved AJAX error handling and retry mechanisms
- Added debugging capabilities for troubleshooting notes issues
- Optimized database operations for better performance and reliability

## [4.9.1] - 2025-01-17
### Added
- **Comprehensive Bricks Builder Notes Integration**: Complete notes functionality integrated directly into Bricks Builder toolbar
  - Added dedicated Notes button to Bricks Builder toolbar (positioned before the Save button)
  - Full notes popup with formatting toolbar, keyboard shortcuts, and auto-save functionality
  - Seamless integration using Bricks Builder's native UI styling and tooltip system
  - Works independently of WordPress admin bar (suitable for installations where admin bar is hidden)
  - Automatic detection and initialization when Bricks Builder context is detected (`?bricks=run`)
  - Complete feature parity with admin area notes: formatting, shortcuts, persistent storage, and auto-save
  - Smart toolbar injection that waits for Bricks Builder UI to be ready before adding notes button

### Changed
- **Notes Feature Default**: Notes feature is now enabled by default for new installations
  - Changed default setting from disabled to enabled for better user experience
  - New users will have notes functionality available immediately without needing to enable it manually
  - Existing installations maintain their current setting (no change for existing users)
  - Setting can still be disabled in SLMM Settings → Features → User Experience if desired

### Technical
- Enhanced Bricks Builder integration class with comprehensive notes support
- Added automatic notes asset loading (CSS/JS) when Bricks Builder context is detected
- Implemented dynamic toolbar injection with DOM monitoring for Bricks Builder readiness
- Created complete notes popup HTML generation and event handler binding in JavaScript
- Added AJAX integration for save/load operations using existing WordPress endpoints
- Maintained security with existing permission checks and nonce verification
- Preserved all existing functionality while extending scope to visual builder environments
- Updated default value logic in both settings callback and notes initialization
- Smart context detection prevents conflicts between admin bar and Bricks toolbar implementations

## [4.9.0] - 2025-01-17
### Added
- **Enhanced Notes Persistence System**: Implemented triple-redundant data storage architecture
  - Primary storage in WordPress `wp_usermeta` table for user-specific notes
  - Dual backup systems using `wp_options` table with structured and version-independent keys
  - Automatic recovery system that restores notes from backups if primary storage fails
  - Update-safe design ensures notes survive plugin updates and version changes
  - Smart fallback mechanism with seamless auto-restoration to primary storage

### Improved
- **Notes User Interface**: Major UX improvements for better usability and accessibility
  - Replaced problematic tooltips with compact 2-line shortcuts reference panel
  - Added always-visible shortcut guide positioned to the right of formatting buttons
  - Enhanced mobile responsiveness with adaptive layout (shortcuts move below buttons on small screens)
  - Eliminated tooltip positioning conflicts with WordPress admin bar and header elements
  - Improved visual hierarchy with purple accent styling for better readability

- **Data Storage Architecture**: Enterprise-grade reliability for user content
  - Implemented version-independent storage keys to prevent data loss during updates
  - Added comprehensive backup validation and integrity checking
  - Enhanced error handling with graceful degradation and recovery mechanisms
  - Optimized database operations for better performance and reliability

### Fixed
- **Tooltip Display Issues**: Resolved double tooltip problems and positioning conflicts
  - Eliminated browser default tooltips that interfered with custom styling
  - Fixed tooltip positioning below WordPress admin header bar
  - Removed slow animation delays (reduced from 0.3s to instant display)
  - Replaced `title` attributes with custom `data-tooltip` system

- **Notes Data Loss**: Prevented notes disappearing during plugin updates
  - Fixed primary storage failures that occurred during version upgrades
  - Implemented automatic backup creation on every save operation
  - Added recovery system that restores from multiple backup sources
  - Enhanced database cleanup to only remove data on complete uninstall

### Removed
- **Shared Notes Functionality**: Removed experimental shared notes feature for stability
  - Simplified codebase by removing shared/private toggle complexity
  - Reverted to reliable per-user private notes system
  - Eliminated potential data conflicts and permission issues
  - Streamlined settings interface by removing shared notes configuration

### Technical
- Enhanced database schema with redundant storage mechanisms
- Implemented automatic data migration and recovery algorithms
- Added comprehensive error logging and debugging capabilities
- Optimized CSS architecture for better mobile responsiveness
- Improved JavaScript event handling and DOM manipulation

## [4.8.1] - 2025-01-17
### Added
- **Project Notes Feature**: New admin bar notes functionality for project management
  - Added resizable notes popup accessible from WordPress admin bar across all admin pages
  - Implemented dark theme design with purple color scheme matching plugin aesthetics
  - Added basic text formatting toolbar with bold, italic, underline, lists, and link support
  - Implemented auto-save functionality that saves notes when closing popup or pressing ESC
  - Added per-user note storage with secure AJAX endpoints for save/load operations
  - Included keyboard shortcuts (Ctrl/Cmd+B/I/U/K) for quick formatting while editing
  - Added setting toggle in SLMM Settings under "Features" → "User Experience" section
  - Responsive design with mobile optimization and proper z-index handling
  - **Compact Shortcuts Reference**: Replaced large tooltips with neat shortcut guide
    - Added compact 2-line shortcuts reference to the right of formatting buttons
    - Shows all available keyboard shortcuts: Bold (Ctrl/Cmd+B), Italic (Ctrl/Cmd+I), Underline (Ctrl/Cmd+U), Link (Ctrl/Cmd+K)
    - Clearly indicates click-only functions: Strikethrough, Lists, Unlink
    - Responsive design: moves below buttons on mobile with smaller text
    - Eliminates tooltip positioning issues and provides always-visible reference
  - **Enhanced Notes Persistence**: Implemented robust data storage to prevent note loss during plugin updates
    - Primary storage in WordPress `wp_usermeta` table (user-specific)
    - Automatic backup system using `wp_options` table for additional safety
    - Version-independent storage keys to survive plugin updates
    - Auto-recovery system that restores notes from backups if primary storage fails
    - Notes are only deleted on complete plugin uninstall, not during updates

### Technical
- Created new `includes/utils/notes.php` file with complete notes functionality
- Added dark theme CSS in `assets/css/slmm-notes.css` with comprehensive styling
- Implemented JavaScript in `assets/js/slmm-notes.js` with resizable popup and auto-save
- Integrated notes feature into plugin settings with proper enable/disable functionality
- Added secure nonce verification and user permission checks for all AJAX operations


## [4.8.0] - 2025-01-17
### Added
- **Comprehensive Dark Theme Implementation**: Complete dark UI transformation across all admin pages
  - Implemented dark theme for SLMM Settings page (`/wp-admin/admin.php?page=slmm-gpt-settings`) with scoped CSS variables
  - Added dark theme for GPT Prompts page (`/wp-admin/admin.php?page=slmm-gpt-prompts`) with purple primary color scheme
  - Implemented dark theme for WordPress Structure Analyzer (`/wp-admin/admin.php?page=wp-structure-analyzer`) with comprehensive page styling
  - Added dark theme for Content Freshness page (`/wp-admin/admin.php?page=content-freshness`) with proper color indicators
  - Added alternating row colors in content freshness table for improved readability

- **Enhanced Admin User Interface**: Improved user experience with modern dark theme design
  - Added scoped CSS variables system to prevent global CSS conflicts and frontend styling issues
  - Implemented consistent purple color scheme (#7C3AED) across all admin interfaces
  - Added responsive design improvements and mobile optimization
  - Enhanced button styling, form controls, and interactive elements with dark theme

- **Visibility Control & Authorization System**: Advanced plugin access management
  - Added plugin visibility settings to restrict admin menu access to specific users
  - Implemented authorized admin username configuration with dynamic management
  - Added emergency URL generation for plugin access recovery
  - Enhanced security features with user-based access control

### Improved
- **Settings Page User Experience**: Major UI/UX enhancements for better usability
  - Removed redundant "Save Settings" button from Export/Import tab (has its own specific buttons)
  - Enhanced tab navigation with improved visual indicators and animations
  - Improved form validation and user feedback systems
  - Added better organization of feature groups and settings sections

- **CSS Architecture & Maintainability**: Enhanced stylesheet organization and performance
  - Implemented scoped CSS variables (`.slmm-settings-wrap`, `.slmm-prompts-dark-theme`, etc.) to prevent global conflicts
  - Fixed critical CSS variable leak that was affecting frontend elements (e.g., `exclude-classes-input`)
  - Enhanced CSS specificity and organization for better maintainability
  - Added consistent styling patterns across all admin pages

- **Bricks Builder Integration**: Enhanced compatibility and debugging capabilities
  - Improved integration with Bricks Builder for better AI content generation
  - Enhanced debugging and error handling for builder-specific functionality
  - Added better compatibility with visual page builders

### Fixed
- **Global CSS Conflicts**: Resolved critical styling issues affecting entire websites
  - Fixed CSS variables spreading from admin pages to frontend (e.g., dark styling on frontend forms)
  - Implemented proper CSS scoping to isolate admin theme styles from website frontend
  - Resolved styling conflicts with WordPress default styles and other plugins
  - Fixed specificity issues causing unintended style inheritance

- **Content Freshness Page**: Enhanced functionality and visual improvements
  - Fixed button styling and color scheme consistency
  - Improved table readability with alternating row colors
  - Enhanced filter buttons and action controls styling
  - Fixed responsive design issues on mobile devices

- **Settings Interface**: Multiple UI improvements and bug fixes
  - Fixed tab switching behavior and improved navigation smoothness
  - Enhanced form validation and error handling
  - Improved accessibility and keyboard navigation
  - Fixed various styling inconsistencies across different browsers

### Technical
- Implemented comprehensive CSS variable scoping system to prevent global conflicts
- Enhanced JavaScript functionality for improved tab management and UI interactions
- Added robust error handling and debugging capabilities across admin interfaces
- Improved code organization and maintainability for future development
- Enhanced responsive design patterns for better mobile and tablet support

## [4.7.3] - 2025-01-27
### Fixed
- **Keyboard Shortcuts Data Localization**: Fixed critical issue where shortcuts failed on fresh page loads
  - Fixed conditional `wp_localize_script()` preventing `slmmGptPromptData` from being available on page initialization
  - Resolved "shortcuts work if dropdown opened but not on fresh page" issue 
  - Ensured GPT prompt data is always localized, even with empty prompts array
  - Added comprehensive error checking and debugging to prevent future data availability issues

- **Shortcut Execution Reliability**: Restored robust keyboard shortcut functionality
  - Restored simple, working `executePromptDirectly()` function from previous working commit
  - Added data availability validation before shortcut setup and execution
  - Enhanced debugging with console logging for troubleshooting shortcut initialization
  - Implemented graceful error messages when data is unavailable

### Improved
- **Documentation and Debugging**: Enhanced troubleshooting capabilities and prevention guidelines
  - Updated `golden-rules-for-shortcuts.md` with critical data localization warnings
  - Added new debugging commands for quick issue diagnosis in browser console
  - Documented the exact symptoms and solutions for data availability problems
  - Enhanced implementation checklist to prevent conditional localization mistakes

### Technical
- Removed conditional data localization wrapper that caused shortcuts to fail
- Added defensive programming patterns with proper error handling
- Enhanced console logging for better debugging of shortcut initialization
- Updated documentation with code examples showing correct vs. incorrect patterns

## [4.7.2] - 2025-01-26
### Fixed
- **Duplicate ID Conflicts**: Resolved DOM validation errors preventing proper functionality
  - Fixed duplicate `#slmm-execute-gpt-prompt` and `#slmm-gpt-prompt-dropdown` IDs on pages with multiple instances
  - Implemented unique ID generation system using static counters to ensure each instance has distinct identifiers
  - Eliminated browser console warnings about non-unique element IDs
  - Enhanced DOM compatibility across different WordPress installations and themes

- **Keyboard Shortcuts Restoration**: Restored fully working keyboard shortcut functionality
  - Fixed keyboard shortcuts (Ctrl+Cmd**** on Mac, Ctrl+Alt**** on Windows) that stopped working after button ID changes
  - Restored original data-driven approach using `slmmGptPromptData.prompts` instead of DOM parsing
  - Maintained backward compatibility with existing `executePromptDirectly()` function
  - Ensured shortcuts work independently of dropdown ID changes

### Improved
- **Multi-Instance Support**: Enhanced support for multiple GPT prompt interfaces on single pages
  - Added intelligent instance detection system with unique IDs for each button/dropdown pair
  - Implemented smart textarea detection that works with both regular WordPress editor and ACF fields
  - Enhanced button functionality to automatically find the correct associated text field
  - Added `data-instance` attributes for reliable button-to-dropdown matching

- **JavaScript Architecture**: Separated and optimized button vs. keyboard shortcut systems
  - Implemented class-based selectors (`.slmm-execute-gpt-prompt`) for better multi-instance support
  - Added event delegation using `$(document).on()` to handle dynamically generated buttons
  - Enhanced content insertion logic with fallback detection for various textarea types
  - Improved error handling and debugging capabilities with detailed console logging

### Added
- **Comprehensive Documentation**: Created detailed technical documentation for future development
  - Added `assets/docs/golden-rules-for-shortcuts.md` with complete architecture specifications
  - Documented the two-system approach (buttons vs. shortcuts) with implementation guidelines
  - Included troubleshooting guides, testing protocols, and emergency recovery procedures
  - Provided code examples and best practices for maintaining keyboard shortcut functionality

### Technical
- Enhanced PHP ID generation with static counter to prevent conflicts
- Improved JavaScript event binding for multi-instance scenarios
- Added robust element detection functions for various editor types
- Implemented backward compatibility layers for existing installations
- Optimized performance by using data-driven shortcuts instead of DOM queries

## [4.7.1] - 2025-01-26
### Fixed
- **GPT Prompt Execution**: Resolved critical issues preventing GPT prompt button functionality
  - Fixed "Execute GPT Prompt" button not working due to script conflicts and missing event handlers
  - Resolved mismatch between button IDs and JavaScript selectors preventing click events
  - Fixed missing data localization preventing prompt data from reaching frontend scripts
  - Corrected AJAX parameter mismatch between frontend requests and backend expectations
  - Enhanced script loading order and dependencies to prevent initialization conflicts

- **Tooltip Display Issues**: Fixed tooltip not showing on some WordPress installations  
  - Resolved CSS specificity conflicts causing tooltips to be hidden by other styles
  - Fixed z-index issues where WordPress admin bar and other elements covered tooltips
  - Enhanced tooltip positioning and styling with improved cross-browser compatibility
  - Added robust CSS with !important declarations to ensure consistent display

### Improved
- **User Experience**: Replaced intrusive popup alerts with elegant notification system
  - Removed all alert() popups that required clicking "OK" to continue
  - Implemented subtle slide-in notifications that auto-fade after 3 seconds
  - Added color-coded notifications (success: green, error: red, warning: orange, info: blue)
  - Enhanced visual feedback with smooth animations and professional styling
  - Added processing state indicators showing "Generating content..." during API calls

- **Script Consolidation**: Streamlined JavaScript handling for better reliability
  - Unified GPT prompt execution logic across multiple script files
  - Added backward compatibility for different button and dropdown IDs
  - Enhanced error handling with detailed console logging for debugging
  - Improved text selection logic for both TinyMCE and textarea editors

### Technical
- Fixed script enqueuing and localization in both plugin main file and settings class
- Enhanced JavaScript error handling and validation for prompt execution
- Improved CSS specificity and positioning for tooltip system
- Added notification system with proper DOM manipulation and cleanup
- Consolidated duplicate functionality across multiple JavaScript files

## [4.7.0] - 2025-01-25
### Added
- **Complete OpenRouter Integration**: Full implementation of OpenRouter API as an alternative AI provider
  - Added OpenRouter API integration class with comprehensive model fetching and management
  - Integrated provider selection in both general settings and GPT prompts settings
  - Added conditional provider options based on API key configuration
  - Implemented OpenRouter-specific model handling with proper fallbacks
  
- **Enhanced Provider Management**: Comprehensive provider switching and model selection
  - Added dynamic provider dropdown with conditional OpenRouter availability
  - Implemented model fetching and caching per provider
  - Added refresh functionality for both OpenAI and OpenRouter models
  - Enhanced model validation and selection preservation

- **Fallback Prevention System**: Transparent AI provider usage with user notifications
  - Added notification system to prevent silent fallbacks between providers
  - Implemented clear provider/model selection indicators in UI
  - Added debugging and logging for provider selection tracking
  - Enhanced user feedback for provider switching and model changes

### Improved
- **Model Selector Interface**: Enhanced model selection across all plugin areas
  - Improved model dropdown rendering with provider-specific models
  - Added search functionality with real-time filtering
  - Enhanced model refresh capabilities with visual feedback
  - Added proper loading states and error handling

- **Provider Configuration**: Streamlined provider setup and management
  - Added conditional UI elements based on API key availability
  - Improved settings validation for provider-specific configurations
  - Enhanced error messaging for missing API keys
  - Added helpful configuration notes for users

- **User Experience**: Better transparency and control over AI provider usage
  - Clear indication of which provider and model are being used
  - Prevention of silent fallbacks that could confuse users
  - Enhanced debugging capabilities for troubleshooting
  - Improved notifications and user feedback systems

### Fixed
- **Silent Fallback Issues**: Resolved cases where incorrect providers were used
  - Fixed OpenRouter models defaulting to OpenAI unexpectedly
  - Resolved provider mismatch in GPT prompts execution
  - Fixed model validation and provider consistency checks
  - Enhanced provider selection persistence across settings

- **Model Selection Bugs**: Fixed various model selection and persistence issues
  - Resolved model dropdown not updating when switching providers
  - Fixed model selection not being saved correctly
  - Improved model validation and error handling
  - Enhanced model cache management and refresh functionality

### Technical
- Added comprehensive OpenRouter integration class with API wrapper
- Implemented provider-aware AJAX handlers for model management
- Enhanced debugging and logging for provider/model selection tracking
- Added conditional UI rendering based on provider configuration
- Improved model caching and refresh mechanisms for both providers

## [4.6.11.2] - 2025-01-25
### Added
- **OpenRouter Integration**: Added complete OpenRouter API integration for expanded AI model access
  - Added full OpenRouter API integration class with model fetching and 1-hour caching
  - Added OpenRouter API key configuration in general settings
  - Implemented provider switching between OpenAI and OpenRouter in both general settings and prompt settings
  - Added real-time model search and filtering with highlighting
  - Added refresh and cache management functionality for OpenRouter models
  - Included fallback models for offline functionality
  - Added comprehensive UI styling for model selector with provider indicators

### Improved
- **AI Model Selection**: Enhanced model selection interface across the plugin
  - Added provider-based model management with search functionality
  - Improved model dropdown with search/filtering capabilities
  - Added model count indicators and refresh controls
  - Enhanced user experience with loading states and smooth transitions

### Fixed
- **Model Search Functionality**: Fixed search function not working in model selector settings
  - Resolved duplicate event binding issues with search inputs
  - Improved model data initialization and storage
  - Added robust error handling and debugging for search functionality
  - Removed conflicting inline JavaScript that interfered with search events

### Improved
- **Model Search Logic**: Enhanced search functionality to match content-helper AND logic behavior
  - Now properly filters models using AND logic (e.g., "Gemini free" shows only free Gemini models)
  - Added regex escaping for special characters in search terms
  - Improved search term cleaning and processing
  - Enhanced case-insensitive matching for better user experience
- **Model Selection Interface**: Added automatic saving and clear selection display like content-helper
  - Implemented auto-save functionality when model selection changes (no save button needed)
  - Added visual indicators showing current selection even when filtered
  - Preserved selected models in dropdown even when hidden by search filters
  - Added "Saving..." and "✓ Saved" indicators for user feedback
  - Enhanced order preservation during filtering to maintain consistent user experience

### Technical
- Added OpenRouter integration class to plugin file includes
- Implemented AJAX handlers for provider model switching and cache management
- Added CSS and JavaScript assets for enhanced model selector functionality
- Enhanced model validation and fallback systems
- Improved JavaScript initialization order and debugging capabilities

## [********] - 2025-05-24
### Fixed
- **Update Checker Functionality**: Fixed "Check for Updates" button not working due to action name conflicts with other plugins
  - Changed action name from generic `check_for_updates` to unique `slmm_seo_check_for_updates` to prevent conflicts
  - Updated action registration and nonce verification to use the new unique action name
  - Fixed plugin update checker not being triggered when button was clicked
  - Resolved conflicts with other plugins (like GEOCENTRIC_DPUpdateChecker) that used the same action name
- **Plugin Update System**: Replaced update checker implementation with clean, optimized version
  - Updated action URL generation to use the new unique action `action=slmm_seo_check_for_updates`
  - Fixed nonce verification to match new action name for security
  - Improved update checker class structure and error handling
- **Plugin Constants**: Updated plugin.php to use proper version extraction from plugin header
  - Fixed version constant definitions to be consistent across the plugin
  - Improved plugin initialization and constant management

### Technical
- Added unique action names to prevent WordPress admin-post action conflicts
- Enhanced update checker class with proper WordPress integration
- Improved plugin slug and cache key management for update system
- Updated remote URL handling for secure update checks

## [*********] - 2024-07-23
### Changed
- Testing update functionality.

## [4.6.10] - 2024-06-21
### Improved
- Enhanced the GPT prompt dropdown UI in the Classic Editor:
  - Added a clear, accessible tooltip next to the dropdown explaining how to use keyboard shortcuts to trigger prompts, and that text must be highlighted for prompt execution.
  - Tooltip now clarifies that clicking the Execute button is optional and shortcuts are faster.
  - Improved visual alignment: tooltip icon is now to the left of the dropdown and uses a dark color for clarity.
  - Set dropdown min-height to 40px to match button height for consistent UI.
- General UI/UX polish for prompt selection and execution workflow.

## [4.6.9] - 2024-12-19
### Added
- **OpenAI Model Selection**: Added dynamic model dropdown options for "Model for Title" and "Model for Description" in settings
  - Added live API integration to fetch available OpenAI models from the API
  - Implemented model caching system (1-hour cache) for improved performance
  - Added "Refresh Models" button to manually update model list
  - Support for latest OpenAI models including GPT-4o, o1-series, and o3-mini
- **Enhanced Settings Migration**: Added automatic migration system for old model settings to new centralized format
- **Model Validation**: Added proper validation for selected models against available OpenAI models

### Improved
- **Settings Centralization**: Consolidated all OpenAI model settings into the main SLMM SEO settings page
- **API Integration**: Enhanced OpenAI integration to use centralized model selection from settings
- **Model Fallbacks**: Improved fallback model system when API calls fail
- **User Experience**: Better model name formatting and display in dropdown selections

### Fixed
- Fixed deprecated radio button model selection and replaced with modern dropdown interface
- Fixed model synchronization between settings and AI integration components
- Fixed legacy snippet files to redirect to centralized settings
- Improved error handling for OpenAI API model fetching

### Technical
- Added transient caching for OpenAI models with automatic refresh capability
- Implemented proper model validation in settings sanitization
- Enhanced backwards compatibility with automatic settings migration
- Improved code organization with centralized model management

## [4.6.8] - 2024-12-19
### Improved
- **Keyboard Shortcuts**: Rewritten keyboard shortcuts functionality to use standard Cmd+S (Mac) and Ctrl+S (Windows) instead of complex key combinations
- **Enhanced Button Targeting**: Improved WordPress editor button detection and targeting for more reliable save operations
- **SEO Meta Box Options**: Enhanced SEO Overview meta box configuration options and post type targeting

### Fixed
- Fixed keyboard shortcuts not working reliably across different WordPress installations
- Fixed button targeting issues in classic editor and Gutenberg
- Simplified shortcut implementation for better compatibility and user experience

## [4.6.7] - 2024-07-03
### Added
- SEO Overview Meta Box: Added a comprehensive SEO overview meta box to the post/page editor, providing real-time status for key SEO metrics (word count, schema, duplicates, links, hidden divs, featured image, and more). Can be toggled from Screen Options and repositioned in the editor.
- Option in settings to enable/disable the SEO Overview meta box.
- Broken Link Checker: Added a robust broken link detection tool with toolbar integration, dialog interface, and real-time link validation. Highlights broken links in the editor and provides detailed reporting.

### Improved
- Duplicate Content Detection: Improved duplicate content detection UI and navigation. Fixed jump links and enhanced indicator logic for better usability.
- SEO Preview: Improved styling and status indicators for SEO preview and related UI components.

### Fixed
- Fixed issues with relative URLs in link scanning and broken link detection.
- Fixed exception rules for link scanning.
- Fixed ignore relative URLs option logic and removed redundant settings.
- General bug fixes and UI polish for all new features.

## [4.6.6] - 2024-07-02
### Added
- Added a duplicate content detection function for real time content scanning

### Fixed
- Fixed critical issue where the Protected Words functionality was affecting URLs in content
- Implemented DOM-based text processing to ensure Protected Words only modify visible text content and not HTML attributes
- Preserved href, src, and other attribute values when applying Sentence Case formatting
- Improved handling of formatted text (bold, italic, etc.) to maintain proper text case while protecting words

## [*******] - 2024-04-01
### Bugfix
- valid header issue

## [*******] - 2024-04-01

### Changed
- Modified save shortcut to use Control+Command+S on Mac and Control+Alt+S on Windows to prevent browser conflicts
- Improved notification system with smoother transitions and better positioning
- Reduced console logging for cleaner developer experience

### Fixed
- Fixed keyboard shortcut conflicts with browser's native Command+S
- Fixed notification system z-index issues with Oxygen Builder
- Fixed script loading order to ensure proper initialization

## [4.6.5] - 2024-04-01

### Added
- Shortcuts to trigger GPT Prompts in Classic Editor Control+Command****

### Changed
- Improved notification system UI and behavior


## [4.6.4] - 2024-04-01

### Added
- New "Prefill From Clipboard" button to SEO description fields
  - Added button next to existing Auto-Populate buttons
  - Implemented clipboard API integration for reading clipboard text
  - Populates all three SEO description fields (Meta, Facebook, Twitter) with clipboard content
  - Improves workflow efficiency for content editors

## [4.6.3] - 2024-03-19

### Removed
- Removed redundant SLMM SEO Settings meta box from post editor to avoid confusion with existing SEO plugins
- Removed associated meta box functions and database entries
- Added uninstall cleanup for removed meta box data

### Changed
- Improved keyboard shortcuts functionality to work more reliably with TinyMCE editor
- Enhanced save shortcut to work from anywhere on the page, including within the editor
- Fixed issue where save shortcut required clicking outside TinyMCE editor

### Technical
- Optimized keyboard shortcut initialization to not depend on TinyMCE loading
- Added proper cleanup of event listeners and resources
- Improved error handling for save operations

## [*******] - 2024-03-21

### Fixed
- Fixed Anchor Text popup sizing issue in Classic Editor
  - Set minimum width to 600px for better content visibility
  - Maintained maximum height at 80vh for scrollability

## [4.6.2] - 2024-03-14

### Improved
- Enhanced Find & Replace dialog UI with better match counter visibility
- Streamlined status messages and improved swap mode feedback

## [4.6.1] - 2024-03-20

### Added
- Search & Replace: Added favorites system for commonly used word replacements
- Search & Replace: Added ability to save and manage favorite search/replace pairs
- Search & Replace: Added "Apply All Favorites" functionality for batch replacements

### Fixed
- Search & Replace: Fixed keyboard shortcut (Shift+Cmd+F / Shift+Ctrl+F) not working on initial page load
- Search & Replace: Improved dialog initialization sequence for better reliability

### Changed
- Search & Replace: Reorganized dialog UI to include favorites section
- Search & Replace: Enhanced keyboard shortcut handling and event propagation

### [4.6.0] - 2024-03-21

### Added
- Enhanced Find & Replace functionality in Classic Editor
  - Added keyboard shortcut (Cmd+Shift+F for Mac, Ctrl+Shift+F for Windows)
  - Added "Multiple terms with one" mode for replacing multiple search terms with a single replacement
  - Added "Swap mode" for one-to-one term replacement
  - Added whole word matching option
  - Added case-sensitive search option

### Improved
- Modernized Find & Replace UI
  - Added centered modal dialog with dark overlay
  - Added close button and Escape key support
  - Improved visual feedback for matches
  - Added status indicator for number of matches
  - Better handling of multiple search terms


### ******* (2024-03-31)

##### Improvements
- Enhanced TinyMCE integration for keyboard shortcuts
  - Improved editor initialization and cleanup
  - Added better resource management
  - Enhanced performance and reduced console warnings
  - Added proper cleanup on WordPress editor events

##### Bug Fixes
- Fixed TinyMCE integration issues
  - Resolved editor instance tracking problems
  - Fixed shortcut cleanup on page unload
  - Improved handling of editor save events
  - Fixed memory leaks in editor initialization

### 4.5.1 (2024-03-31)

##### Features
- Integrated keyboard shortcuts with WordPress settings
  - Added enable/disable option in general settings
  - Improved keyboard shortcut handling for save functionality
  - Enhanced tooltip display for keyboard shortcuts

##### Improvements
- Streamlined keyboard shortcuts implementation
  - Removed redundant JavaScript files
  - Improved code organization and efficiency
  - Enhanced compatibility with WordPress core

##### Bug Fixes
- Fixed keyboard shortcuts persistence issue
  - Resolved issue where shortcuts remained active when disabled
  - Improved handling of disabled state
  - Fixed tooltip display when feature is disabled

### 4.4.10 (2024-03-19)

##### Improvements
- Standardized OpenAI API key handling across the plugin
  - Unified API key storage in `chatgpt_generator_options['openai_api_key']`
  - Improved API key validation and error handling
  - Enhanced security for API key storage and retrieval

##### Bug Fixes
- Fixed GPT prompt execution issues
  - Improved error handling in AJAX requests
  - Added better validation for API responses
  - Enhanced model selection options to include GPT-4 Turbo
  - Fixed API key retrieval in prompt settings

#### 4.4.9 (2024-02-03)

### Added
- New "Empty Trash" button functionality in admin bar and list views
- New "Send to Trash" button unctionality in admin bar and list views
- Option to enable/disable Empty Trash button via settings
- Rate limiting for trash operations
- Logging system for deletion attempts

### Changed
- Improved redirection after trash operations to list view
- Enhanced trash management with proper cleanup of attachments and metadata
- Updated settings page with clearer descriptions

### Fixed
- Fixed issue where trash wasn't being properly emptied
- Fixed redirection after trashing items to always go to list view
- Fixed visibility of Empty Trash button in admin areas
- Fixed trash cleanup to properly handle attachments and metadata

### Security
- Added nonce verification for trash operations
- Improved permission checks for trash management
- Added rate limiting to prevent abuse
- Enhanced logging for audit trail


#### 4.4.8 (2025-02-22)

##### Features
- Added dynamic Protected Words management
  - Implemented sidebar meta box for adding protected words
  - Added screen options support for Protected Words box
  - Extended protected words functionality across all post types

##### Improvements
- Enhanced Protected Words replacement mechanism
  - Improved word boundary detection
  - Added real-time word list updates
  - Implemented robust word protection logic

##### Bug Fixes
- Fixed protected words replacement in sentence case
  - Corrected regex pattern to prevent partial word replacements
  - Ensured consistent word protection across different contexts

#### 4.4.7 (2025-02-21)

##### Features
- Added new SEO tools functionality
  - Implemented clean and title case conversion
  - Added sentence case transformation
  - Integrated hidden div creation utility
  - Added anchor text generation feature
  - Implemented GMB image insertion tool
- Added keyword checker functionality
  - Integrated keyword analysis tools
  - Added event binding for keyword checking

#### 4.4.6 (2025-02-20)

##### Improvements
- Streamlined SEO text helper JavaScript
  - Removed redundant JavaScript file `slmm-seo-text-helper.js`

##### Bug Fixes
- Improved hidden div detection in pre-flight checklist
  - Enhanced indicator display for zero and multiple hidden divs

#### 4.4.5 (2025-02-20)

##### Bug Fixes
- Fixed class preservation in Clean and Title function
  - Added support for space-separated class lists in exclusions
  - Fixed handling of multiple excluded classes
  - Improved preservation of exact class combinations
  - Maintained class order in preserved attributes

##### Improvements
- Enhanced class exclusion mechanism
  - Added support for exact class string matches
  - Improved handling of compound class names
  - Better preservation of class hierarchies

#### 4.4.4 (2025-02-20)

##### Bug Fixes
- Improved Sentence Case functionality
  - Enhanced word boundary detection for protected words
  - Fixed issues with partial word transformations
  - Implemented robust regex pattern matching
  - Preserved exact capitalization of standalone words

##### Improvements
- Refined text processing algorithm for sentence case conversion
- Added more precise word matching techniques
- Improved handling of multi-word protected phrases

#### 4.4.3 (2025-02-18)

##### Improvements
- Enhanced button and input field styling
  - Added consistent 40px height for all admin buttons
  - Improved vertical alignment of buttons and inputs
  - Created centralized CSS for SLMM plugin buttons
- Expanded SEO pre-fight checklist visibility
  - Checklist now appears for all public post types
  - Removed hardcoded post type limitations

##### Bug Fixes
- Removed non-functional "Search and Replace" input box
  - Deleted input field from text helper interface
  - Simplified editor toolbar functionality

#### 4.4.2 (2025-02-14)

##### Bug Fixes
- Fixed schema tag handling in WP Sheet Editor
  - Added proper sanitization for schema script tags
  - Implemented wp_kses filter to preserve legitimate schema markup
  - Added specific handling for WP Sheet Editor schema saving
  - Enhanced security while maintaining schema functionality

#### 4.4.1 (2025-02-13)

##### Improvements
- Improved version management system
  - Centralized version control in main plugin file
  - Removed duplicate version numbers from component files
  - All components now inherit version from main plugin
  - Simplified version updates across the plugin

##### Bug Fixes
- Fixed protected keywords visibility issue
  - Removed protected words script from front-end
  - Protected words functionality now only loads in admin area
  - Improved code organization and efficiency

#### ******* (2024-11-25)

##### Bug Fixes
- Fixed sentence case functionality for headings
  - Modified heading processing to properly apply sentence case
  - Only first word is now capitalized in headings
  - Improved text transformation consistency

#### 4.3.5 (2024-11-25)

##### Features
- Added a new "Copy URL" button to the editor toolbar
  - One-click copying of post/page permalinks
  - Visual feedback with "Copied!" message
  - Error handling for clipboard operations
  - Seamless integration with existing editor UI
- Enhanced Sentence Case Functionality
  - Added protected words list feature
  - Preserves capitalization of specified terms
  - Improved accuracy of case conversion
  - Better handling of special terms and proper nouns

##### Bug Fixes
- Fixed issue with GPT4 Turbo support
  - Resolved API integration problems
  - Improved response handling
  - Enhanced error messaging

#### 4.3.4 (2024-11-25)

##### Features
- Initial release of enhanced SEO tools
- Added GPT4 Turbo support
  - Integrated latest GPT-4 API
  - Optimized for SEO content generation
  - Enhanced performance and reliability

#### ******* (2024-12-01)

##### Bug Fixes
- Improved sentence case functionality
  - Fixed handling of anchor text in sentences
  - Enhanced sentence flow detection across HTML elements
  - Maintained proper capitalization based on sentence position
  - Improved handling of sentence-ending punctuation

#### ******* (2024-12-01)

##### Bug Fixes
- Fixed issue with sentence case functionality
  - Fixed handling of anchor text in sentences


