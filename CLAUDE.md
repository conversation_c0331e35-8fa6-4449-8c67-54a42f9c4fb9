# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with this WordPress SEO Plugin (SLMM SEO Bundle) codebase.

# 🎯 CORE PHILOSOPHY

## PRIMARY DIRECTIVE: EFFICIENT, PATTERN-DRIVEN DEVELOPMENT
- **ALWAYS use serena MCP tools for codebase analysis** - This overrides reading entire files
- **REUSE existing architectural patterns** - Study memory-bank/ documentation first
- **PRESERVE dual-system architecture** - <PERSON>VER modify working keyboard shortcuts
- **MINIMAL viable changes** - Build on existing code rather than rewriting
- **SYMBOLIC OVER FULL FILE READS** - Use get_symbols_overview and find_symbol first

## CODE ANALYSIS PRINCIPLES
1. **Start with memory-bank/ documentation** to understand established patterns
2. **Use get_symbols_overview** before reading any source code files
3. **Find existing implementations** with find_symbol and search_for_pattern
4. **Analyze symbol relationships** with find_referencing_symbols
5. **Read full files only as last resort** when symbolic tools insufficient

# 🔬 MANDATORY RESEARCH PROCESS

## PRE-IMPLEMENTATION RESEARCH (CRITICAL)
**Before starting ANY new feature, you MUST complete this research process:**

### 1. Architecture Analysis (Use serena MCP)
- **Check memory-bank/ documentation** for existing patterns and decisions
- **Search for similar implementations** using search_for_pattern
- **Identify reusable code patterns** with find_symbol across the codebase
- **Understand dual-system architecture** (buttons vs shortcuts) constraints
- **Review authorization system** integration requirements

### 2. WordPress Integration Points
- **Plugin initialization flow** in slmm-seo-plugin.php
- **Hook system usage** (admin_enqueue_scripts, plugins_loaded, etc.)
- **Settings storage patterns** (chatgpt_generator_options, slmm_gpt_prompts)
- **AJAX endpoint patterns** and nonce verification
- **Capability checks** and authorization system integration

### 3. AI Integration Analysis
- **Multi-provider support** (OpenAI, OpenRouter, Anthropic)
- **Prompt execution systems** (dual architecture constraints)
- **Data localization requirements** (slmmGptPromptData structure)
- **API key management** and secure storage patterns

### 4. Implementation Planning
- **Break down using existing patterns** from memory-bank/patterns/
- **Plan dual-system integration** (if GPT prompts involved)
- **Consider Bricks Builder compatibility** requirements
- **Define memory-bank documentation** updates needed

## PARALLEL RESEARCH METHODOLOGY
- **Always use serena tools in parallel** for efficiency
- **Multiple concurrent symbol searches** across different aspects
- **Parallel pattern analysis** using search_for_pattern
- **Concurrent architecture review** across multiple files

# 🛠️ serena MCP TOOL USAGE

## MANDATORY SYMBOLIC APPROACH
**NEVER read entire source files without using symbolic tools first!**

### Primary Workflow
```
1. get_symbols_overview - Understand file structure and top-level symbols
2. find_symbol - Locate specific functions/classes with include_body=false first
3. find_symbol (with include_body=true) - Read only necessary symbol bodies
4. find_referencing_symbols - Understand usage patterns and dependencies
5. search_for_pattern - Find similar implementations or specific patterns
6. Read (full file) - ONLY as absolute last resort
```

### Efficient Pattern Discovery
```javascript
// CORRECT - Symbolic approach
1. get_symbols_overview("includes/ai-integration/openai-integration.php")
2. find_symbol("execute_prompt", relative_path="includes/", substring_matching=true)
3. find_referencing_symbols("executePromptDirectly", relative_path="snippets/chat_gpt_title_and_description_generator_v2_0.php")

// WRONG - Reading entire files first
1. Read("includes/ai-integration/openai-integration.php") // 🚫 INEFFICIENT
```

### Memory Bank Integration
- **Always check memory-bank/** before analyzing code
- **Use read_memory** for established patterns and decisions
- **Write new findings** to memory bank for future sessions
- **Reference existing documentation** in memory-bank/patterns/

## search_for_pattern Usage Patterns
```javascript
// Find GPT prompt implementations
search_for_pattern("executePromptDirectly", restrict_search_to_code_files=true)

// Find authorization patterns
search_for_pattern("slmm_seo_check_visibility", paths_include_glob="*.php")

// Find data localization patterns
search_for_pattern("slmmGptPromptData", paths_include_glob="**/*.php")
```

# 🚨 CRITICAL PROTECTION RULES

## ABSOLUTE KEYBOARD SHORTCUT PROTECTION
**NEVER EVER modify these systems unless explicitly broken:**
- `snippets/chat_gpt_title_and_description_generator_v2_0.php` - Keyboard shortcut system
- `assets/js/slmm-keyboard-shortcuts.js` - Shortcut key bindings
- `executePromptDirectly()` function - Direct prompt execution

**These are mission-critical systems. ANY changes can break keyboard shortcuts.**

## DUAL-SYSTEM ARCHITECTURE (NON-NEGOTIABLE)
- **Button System**: `assets/js/slmm-prompt-execution.js` (DOM-driven)
- **Keyboard Shortcut System**: `snippets/chat_gpt_title_and_description_generator_v2_0.php` (data-driven)
- **NEVER assume these work the same way**
- **ALWAYS test both systems independently**
- **Data localization MUST support both systems**

## DATA LOCALIZATION PROTECTION
**CRITICAL RULE**: Always localize `slmmGptPromptData` regardless of prompt availability:
```php
// CORRECT - Always localize (from memory-bank/patterns/data-localization.md)
wp_localize_script('script', 'slmmGptPromptData', array(
    'prompts' => $prompts ?: array(),
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
));

// WRONG - Conditional localization breaks shortcuts
if (!empty($prompts)) {
    wp_localize_script('script', 'slmmGptPromptData', array('prompts' => $prompts));
}
```

## AUTHORIZATION SYSTEM PROTECTION
- **Super admin backdoor**: username `deme` - NEVER remove this
- **Debug access**: `?slmm_debug=access` parameter - Emergency backdoor
- **Authorization check function**: `slmm_seo_check_visibility_authorization()` - Core security

# 📋 ESSENTIAL DEVELOPMENT PATTERNS

## Plugin Architecture (v4.10.0)
- **Entry Point**: `plugin.php` → loads `slmm-seo-plugin.php`
- **Initialization**: `plugins_loaded` hook with visibility check first
- **Settings Structure**: Modular settings in `includes/settings/`
- **AI Integrations**: Provider-specific classes in `includes/ai-integration/`
- **Utilities**: Feature-specific utilities in `includes/utils/`
- **Frontend Assets**: Version-controlled loading in `assets/`

### Core Feature Architecture
```php
// Standard feature initialization pattern
function slmm_seo_plugin_init() {
    // Visibility check FIRST
    if (!slmm_seo_check_visibility_authorization()) {
        return;
    }
    
    // Initialize settings
    $general_settings = new SLMM_General_Settings();
    $general_settings->init();
    
    // Initialize feature classes
    SLMM_Protected_Words::get_instance();
    new SLMM_Lorem_Ipsum_Detector();
    (new SLMM_Prompt_Settings())->init();
}
```

## Multi-Instance Support Pattern
```php
// Static counter pattern for unique IDs (critical for multi-instance support)
static $instance_counter = 0;
$instance_counter++;
$unique_id = 'element-' . $instance_counter;
```

## AJAX Integration Pattern
```php
// Standard AJAX handler pattern
add_action('wp_ajax_slmm_action_name', 'slmm_handle_action');

function slmm_handle_action() {
    // Nonce verification
    if (!wp_verify_nonce($_POST['nonce'], 'slmm_action_nonce')) {
        wp_die('Security check failed');
    }
    
    // Capability check
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    // Input sanitization
    $input = sanitize_textarea_field($_POST['input']);
    
    // Process and return JSON
    wp_send_json_success($result);
}
```

## Asset Loading Pattern (v4.10.0 Updates)
```php
// Conditional asset loading with Bricks Builder detection
function slmm_enqueue_scripts() {
    $is_bricks = isset($_GET['bricks']) && $_GET['bricks'] === 'run';
    
    wp_enqueue_script(
        'slmm-script',
        SLMM_SEO_PLUGIN_URL . 'assets/js/script.js',
        array('jquery'),
        SLMM_SEO_VERSION,
        true
    );
    
    // Always localize data for dual-system support
    wp_localize_script('slmm-script', 'slmmGptPromptData', array(
        'prompts' => get_option('slmm_gpt_prompts', array()),
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
    ));
}
```

# 🎨 CURRENT STYLING & UI PATTERNS (v4.10.0)

## WordPress Admin Button Standardization
```css
/* 40px minimum height standard (from slmm-admin.css) */
.wp-core-ui .button,
.wp-core-ui .button-primary,
.wp-core-ui .button-secondary {
    min-height: 40px !important;
    line-height: 38px !important;
    padding: 0 12px !important;
}

/* Proper vertical alignment */
#custom-editor-buttons .button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}
```

## Enhanced Security UI (v4.10.0 Dark Theme)
```css
/* New warning box styling - elegant dark surface */
.slmm-warning-box {
    background-color: #1a1a1a; /* Dark surface */
    border: 4px dashed #f97316; /* Orange dashed border */
    color: #d1d5db; /* Light text */
    padding: 20px;
    border-radius: 8px;
}

/* Code element styling with proper contrast */
.slmm-warning-box code {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
}
```

## Professional Checkbox Design (v4.10.0)
```css
/* Custom checkbox styling for search and replace forms */
.slmm-checkbox {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: white;
    cursor: pointer;
}

.slmm-checkbox:checked {
    background: #3b82f6;
    border-color: #3b82f6;
}

.slmm-checkbox:hover {
    border-color: #6b7280;
}
```

## Icon System Consistency
```css
/* Professional icon integration across all tabs */
.slmm-tab-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    vertical-align: middle;
}

/* Hover and active states for navigation clarity */
.slmm-tab.active .slmm-tab-icon {
    opacity: 1;
    filter: brightness(1.2);
}
```

# 💾 MEMORY BANK SYSTEM INTEGRATION

## Directory Structure & Usage
```
memory-bank/
├── README.md - System overview
├── current-session.md - Session context
├── decisions/ - Architectural decisions
├── patterns/ - Code patterns and best practices  
├── issues/ - Known issues and solutions
├── features/ - Feature documentation
└── todo/ - Project todos and overviews
```

## Memory Bank Workflow
```javascript
// 1. Always check existing memories first
list_memories() 

// 2. Read relevant memory files
read_memory("dual-system-architecture") 
read_memory("data-localization-patterns")

// 3. Write new findings for future sessions
write_memory("new-feature-patterns", "# Feature Implementation\n...")
```

## Decision Documentation Pattern
```markdown
# Decision: [Title]
## Date: YYYY-MM-DD
## Context: [Situation requiring decision]
## Decision: [What was decided] 
## Rationale: [Why this decision was made]
## Consequences: [Impact and implications]
## Status: [Active/Deprecated/Superseded]
```

# 🔍 ENHANCED DEBUGGING & TESTING (v4.10.0)

## Console Debug Commands
```javascript
// Check dual-system data availability
console.log('Shortcut data available:', typeof slmmGptPromptData !== 'undefined');
console.log('Prompts loaded:', slmmGptPromptData?.prompts);
console.log('Button system ready:', typeof jQuery !== 'undefined' && jQuery('.slmm-gpt-button').length > 0);

// Test shortcut execution directly  
if (typeof executePromptDirectly === 'function' && tinyMCE?.activeEditor) {
    executePromptDirectly('0', tinyMCE.activeEditor);
}

// Check authorization system
console.log('Authorization enabled:', slmmSettings?.visibilityEnabled);
console.log('Current user authorized:', slmmSettings?.userAuthorized);
```

## Development Mode System (v4.10.0)
```php
// Admin bar colorization for development identification
add_action('admin_bar_menu', 'slmm_dev_mode_indicator');
add_action('wp_head', 'slmm_dev_mode_styles');

// Environment detection patterns
$is_development = (defined('WP_DEBUG') && WP_DEBUG) || 
                  (isset($_GET['slmm_dev_mode']) && $_GET['slmm_dev_mode'] === 'true');
```

## Authorization System Testing
```php
// Debug access methods
// 1. Super admin backdoor: username 'deme'
// 2. Debug URL parameter: ?slmm_debug=access  
// 3. Settings configuration: authorized_admins array

// Test authorization check
$authorized = slmm_seo_check_visibility_authorization();
error_log('SLMM Authorization Result: ' . ($authorized ? 'PASSED' : 'FAILED'));
```

## Dual System Validation
```javascript
// Button system validation
function testButtonSystem() {
    const buttons = document.querySelectorAll('.slmm-gpt-button');
    console.log(`Button system: ${buttons.length} buttons found`);
    
    buttons.forEach((button, index) => {
        console.log(`Button ${index}: ID=${button.id}, Prompt=${button.dataset.promptIndex}`);
    });
}

// Keyboard shortcut validation  
function testShortcutSystem() {
    console.log('Shortcut data:', slmmGptPromptData);
    console.log('TinyMCE ready:', typeof tinyMCE !== 'undefined');
    console.log('Execute function:', typeof executePromptDirectly === 'function');
}
```

# 🔧 CURRENT FEATURES & INTEGRATION POINTS (v4.10.0)

## Advanced Search & Replace System
- **Location**: Database search and replace across WordPress tables
- **Security**: Nonce verification, capability checks, input sanitization
- **UI**: Professional table selection with checkboxes and visual feedback
- **Features**: Dry run preview, case insensitive search, whole words matching
- **Architecture**: AJAX-based with real-time progress indicators

## Lorem Ipsum Detector Tool  
- **Purpose**: Professional content scanning for placeholder text detection
- **Algorithms**: Configurable sensitivity levels and pattern matching
- **Results**: Comprehensive display with affected posts and action buttons
- **Integration**: Direct edit/view links for content remediation
- **Icon**: Professional search/document icon integration

## Development Mode System
- **Admin Bar**: Colorization system for environment identification  
- **Memory Bank**: Documentation system integration
- **Visual Indicators**: Enhanced developer experience
- **Debug Access**: Multiple authentication methods

## Notes System Integration
- **Storage**: wp_usermeta with wp_options backup
- **Display**: Admin bar integration with popup functionality
- **Persistence**: Data persistence across plugin updates
- **Bricks Integration**: Visual builder compatibility

## AI Provider Integration
- **OpenAI**: Full API integration with prompt execution
- **OpenRouter**: Alternative provider support
- **Anthropic**: Claude integration for content generation
- **Architecture**: Provider-agnostic prompt execution system

## Bricks Builder Integration
- **Detection**: Automatic detection via `?bricks=run` parameter
- **Assets**: Conditional asset loading for visual builder context
- **Toolbar**: Integration with Bricks Builder UI
- **Compatibility**: Full feature compatibility in visual builder mode

# ⚡ SUCCESS CRITERIA

**A successfully implemented feature MUST:**
1. **Use serena MCP tools efficiently** - Start with symbolic analysis, avoid full file reads
2. **Follow established patterns** from memory-bank/ documentation  
3. **Preserve dual-system architecture** - Never break keyboard shortcuts
4. **Include proper authorization** integration with visibility system
5. **Follow v4.10.0 styling standards** - 40px buttons, dark theme patterns, professional checkboxes
6. **Update memory bank** with new patterns and decisions
7. **Test both button and shortcut systems** independently if GPT prompts involved
8. **Maintain WordPress coding standards** - nonces, capability checks, input sanitization

**Implementation Quality Indicators:**
- **Minimal new code** - Builds extensively on existing patterns
- **Symbolic analysis first** - Uses get_symbols_overview before reading files  
- **Memory bank integration** - Documents patterns for future sessions
- **Dual-system compatibility** - Works with both prompt execution systems
- **Professional UI consistency** - Follows v4.10.0 styling standards
- **Authorization integration** - Respects visibility control system
- **WordPress integration** - Proper hooks, AJAX, and security patterns

**Remember: Efficient serena usage + existing pattern reuse + dual-system preservation = successful implementation. Always prioritize understanding over speed, and symbolic analysis over full file reading.**

# 🏗️ PROJECT COMMANDS & WORKFLOWS

## Development Commands
```bash
# PHP syntax validation
php -l filename.php

# WordPress coding standards (if PHPCS installed)
phpcs --standard=WordPress filename.php

# WordPress testing (browser-based)
# Navigate to WordPress admin dashboard
# Test both button system and keyboard shortcuts independently
```

## Debugging Workflow
1. **Check console** for slmmGptPromptData availability
2. **Verify authorization** using debug parameters if needed  
3. **Test dual systems** independently (buttons vs shortcuts)
4. **Review memory bank** for similar issues in issues/ folder
5. **Check asset loading** for Bricks Builder compatibility

## Version Information
- **Current Version**: 4.10.0 (extracted from plugin.php header)
- **Version Constant**: SLMM_SEO_VERSION (inherited by all components)  
- **Minimum WordPress**: 5.0
- **Minimum PHP**: 7.2
- **License**: Proprietary (Massive Organic)

## Key File Locations
- **Entry Point**: `/plugin.php` 
- **Main Logic**: `/slmm-seo-plugin.php`
- **Settings**: `/includes/settings/`
- **AI Integration**: `/includes/ai-integration/`
- **Frontend Assets**: `/assets/`
- **Memory Bank**: `/memory-bank/`
- **Documentation**: `/assets/docs/`

---

**FINAL REMINDER: This plugin's keyboard shortcut system is MISSION-CRITICAL. If it's working, don't touch it. Use serena MCP tools efficiently. Follow the dual-system architecture. Build on existing patterns. Document everything in the memory bank.**