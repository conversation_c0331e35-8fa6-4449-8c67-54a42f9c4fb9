[{"location_code": "2004", "location_name": "Afghanistan", "location_code_parent": "", "country_iso_code": "AF", "location_type": "Country"}, {"location_code": "2008", "location_name": "Albania", "location_code_parent": "", "country_iso_code": "AL", "location_type": "Country"}, {"location_code": "2010", "location_name": "Antarctica", "location_code_parent": "", "country_iso_code": "AQ", "location_type": "Country"}, {"location_code": "2012", "location_name": "Algeria", "location_code_parent": "", "country_iso_code": "DZ", "location_type": "Country"}, {"location_code": "2016", "location_name": "American Samoa", "location_code_parent": "", "country_iso_code": "AS", "location_type": "Country"}, {"location_code": "2020", "location_name": "Andorra", "location_code_parent": "", "country_iso_code": "AD", "location_type": "Country"}, {"location_code": "2024", "location_name": "Angola", "location_code_parent": "", "country_iso_code": "AO", "location_type": "Country"}, {"location_code": "2028", "location_name": "Antigua and Barbuda", "location_code_parent": "", "country_iso_code": "AG", "location_type": "Country"}, {"location_code": "2031", "location_name": "Azerbaijan", "location_code_parent": "", "country_iso_code": "AZ", "location_type": "Country"}, {"location_code": "2032", "location_name": "Argentina", "location_code_parent": "", "country_iso_code": "AR", "location_type": "Country"}, {"location_code": "2036", "location_name": "Australia", "location_code_parent": "", "country_iso_code": "AU", "location_type": "Country"}, {"location_code": "2040", "location_name": "Austria", "location_code_parent": "", "country_iso_code": "AT", "location_type": "Country"}, {"location_code": "2044", "location_name": "The Bahamas", "location_code_parent": "", "country_iso_code": "BS", "location_type": "Country"}, {"location_code": "2048", "location_name": "Bahrain", "location_code_parent": "", "country_iso_code": "BH", "location_type": "Country"}, {"location_code": "2050", "location_name": "Bangladesh", "location_code_parent": "", "country_iso_code": "BD", "location_type": "Country"}, {"location_code": "2051", "location_name": "Armenia", "location_code_parent": "", "country_iso_code": "AM", "location_type": "Country"}, {"location_code": "2052", "location_name": "Barbados", "location_code_parent": "", "country_iso_code": "BB", "location_type": "Country"}, {"location_code": "2056", "location_name": "Belgium", "location_code_parent": "", "country_iso_code": "BE", "location_type": "Country"}, {"location_code": "2060", "location_name": "Bermuda", "location_code_parent": "", "country_iso_code": "BM", "location_type": "Region"}, {"location_code": "2064", "location_name": "Bhutan", "location_code_parent": "", "country_iso_code": "BT", "location_type": "Country"}, {"location_code": "2068", "location_name": "Bolivia", "location_code_parent": "", "country_iso_code": "BO", "location_type": "Country"}, {"location_code": "2070", "location_name": "Bosnia and Herzegovina", "location_code_parent": "", "country_iso_code": "BA", "location_type": "Country"}, {"location_code": "2072", "location_name": "Botswana", "location_code_parent": "", "country_iso_code": "BW", "location_type": "Country"}, {"location_code": "2074", "location_name": "Bouvet Island", "location_code_parent": "", "country_iso_code": "BV", "location_type": "Region"}, {"location_code": "2076", "location_name": "Brazil", "location_code_parent": "", "country_iso_code": "BR", "location_type": "Country"}, {"location_code": "2084", "location_name": "Belize", "location_code_parent": "", "country_iso_code": "BZ", "location_type": "Country"}, {"location_code": "2086", "location_name": "British Indian Ocean Territory", "location_code_parent": "", "country_iso_code": "IO", "location_type": "Region"}, {"location_code": "2090", "location_name": "Solomon Islands", "location_code_parent": "", "country_iso_code": "SB", "location_type": "Country"}, {"location_code": "2092", "location_name": "British Virgin Islands", "location_code_parent": "", "country_iso_code": "VG", "location_type": "Region"}, {"location_code": "2096", "location_name": "Brunei", "location_code_parent": "", "country_iso_code": "BN", "location_type": "Country"}, {"location_code": "2100", "location_name": "Bulgaria", "location_code_parent": "", "country_iso_code": "BG", "location_type": "Country"}, {"location_code": "2104", "location_name": "Myanmar (Burma)", "location_code_parent": "", "country_iso_code": "MM", "location_type": "Country"}, {"location_code": "2108", "location_name": "Burundi", "location_code_parent": "", "country_iso_code": "BI", "location_type": "Country"}, {"location_code": "2116", "location_name": "Cambodia", "location_code_parent": "", "country_iso_code": "KH", "location_type": "Country"}, {"location_code": "2120", "location_name": "Cameroon", "location_code_parent": "", "country_iso_code": "CM", "location_type": "Country"}, {"location_code": "2124", "location_name": "Canada", "location_code_parent": "", "country_iso_code": "CA", "location_type": "Country"}, {"location_code": "2132", "location_name": "Cabo Verde", "location_code_parent": "", "country_iso_code": "CV", "location_type": "Country"}, {"location_code": "2136", "location_name": "Cayman Islands", "location_code_parent": "", "country_iso_code": "KY", "location_type": "Region"}, {"location_code": "2140", "location_name": "Central African Republic", "location_code_parent": "", "country_iso_code": "CF", "location_type": "Country"}, {"location_code": "2144", "location_name": "Sri Lanka", "location_code_parent": "", "country_iso_code": "LK", "location_type": "Country"}, {"location_code": "2148", "location_name": "Chad", "location_code_parent": "", "country_iso_code": "TD", "location_type": "Country"}, {"location_code": "2152", "location_name": "Chile", "location_code_parent": "", "country_iso_code": "CL", "location_type": "Country"}, {"location_code": "2156", "location_name": "China", "location_code_parent": "", "country_iso_code": "CN", "location_type": "Country"}, {"location_code": "2158", "location_name": "Taiwan", "location_code_parent": "", "country_iso_code": "TW", "location_type": "Region"}, {"location_code": "2162", "location_name": "Christmas Island", "location_code_parent": "", "country_iso_code": "CX", "location_type": "Country"}, {"location_code": "2166", "location_name": "Cocos (Keeling) Islands", "location_code_parent": "", "country_iso_code": "CC", "location_type": "Country"}, {"location_code": "2170", "location_name": "Colombia", "location_code_parent": "", "country_iso_code": "CO", "location_type": "Country"}, {"location_code": "2174", "location_name": "Comoros", "location_code_parent": "", "country_iso_code": "KM", "location_type": "Country"}, {"location_code": "2175", "location_name": "Mayotte", "location_code_parent": "", "country_iso_code": "YT", "location_type": "Region"}, {"location_code": "2178", "location_name": "Republic of the Congo", "location_code_parent": "", "country_iso_code": "CG", "location_type": "Country"}, {"location_code": "2180", "location_name": "Democratic Republic of the Congo", "location_code_parent": "", "country_iso_code": "CD", "location_type": "Country"}, {"location_code": "2184", "location_name": "Cook Islands", "location_code_parent": "", "country_iso_code": "CK", "location_type": "Country"}, {"location_code": "2188", "location_name": "Costa Rica", "location_code_parent": "", "country_iso_code": "CR", "location_type": "Country"}, {"location_code": "2191", "location_name": "Croatia", "location_code_parent": "", "country_iso_code": "HR", "location_type": "Country"}, {"location_code": "2196", "location_name": "Cyprus", "location_code_parent": "", "country_iso_code": "CY", "location_type": "Country"}, {"location_code": "2203", "location_name": "Czechia", "location_code_parent": "", "country_iso_code": "CZ", "location_type": "Country"}, {"location_code": "2204", "location_name": "Benin", "location_code_parent": "", "country_iso_code": "BJ", "location_type": "Country"}, {"location_code": "2208", "location_name": "Denmark", "location_code_parent": "", "country_iso_code": "DK", "location_type": "Country"}, {"location_code": "2212", "location_name": "Dominica", "location_code_parent": "", "country_iso_code": "DM", "location_type": "Country"}, {"location_code": "2214", "location_name": "Dominican Republic", "location_code_parent": "", "country_iso_code": "DO", "location_type": "Country"}, {"location_code": "2218", "location_name": "Ecuador", "location_code_parent": "", "country_iso_code": "EC", "location_type": "Country"}, {"location_code": "2222", "location_name": "El Salvador", "location_code_parent": "", "country_iso_code": "SV", "location_type": "Country"}, {"location_code": "2226", "location_name": "Equatorial Guinea", "location_code_parent": "", "country_iso_code": "GQ", "location_type": "Country"}, {"location_code": "2231", "location_name": "Ethiopia", "location_code_parent": "", "country_iso_code": "ET", "location_type": "Country"}, {"location_code": "2232", "location_name": "Eritrea", "location_code_parent": "", "country_iso_code": "ER", "location_type": "Country"}, {"location_code": "2233", "location_name": "Estonia", "location_code_parent": "", "country_iso_code": "EE", "location_type": "Country"}, {"location_code": "2234", "location_name": "Faroe Islands", "location_code_parent": "", "country_iso_code": "FO", "location_type": "Region"}, {"location_code": "2238", "location_name": "Falkland Islands (Islas Malvinas)", "location_code_parent": "", "country_iso_code": "FK", "location_type": "Region"}, {"location_code": "2239", "location_name": "South Georgia and the South Sandwich Islands", "location_code_parent": "", "country_iso_code": "GS", "location_type": "Country"}, {"location_code": "2242", "location_name": "Fiji", "location_code_parent": "", "country_iso_code": "FJ", "location_type": "Country"}, {"location_code": "2246", "location_name": "Finland", "location_code_parent": "", "country_iso_code": "FI", "location_type": "Country"}, {"location_code": "2250", "location_name": "France", "location_code_parent": "", "country_iso_code": "FR", "location_type": "Country"}, {"location_code": "2254", "location_name": "French Guiana", "location_code_parent": "", "country_iso_code": "GF", "location_type": "Region"}, {"location_code": "2258", "location_name": "French Polynesia", "location_code_parent": "", "country_iso_code": "PF", "location_type": "Country"}, {"location_code": "2260", "location_name": "French Southern and Antarctic Lands", "location_code_parent": "", "country_iso_code": "TF", "location_type": "Country"}, {"location_code": "2262", "location_name": "Djibouti", "location_code_parent": "", "country_iso_code": "DJ", "location_type": "Country"}, {"location_code": "2266", "location_name": "Gabon", "location_code_parent": "", "country_iso_code": "GA", "location_type": "Country"}, {"location_code": "2268", "location_name": "Georgia", "location_code_parent": "", "country_iso_code": "GE", "location_type": "Country"}, {"location_code": "2270", "location_name": "The Gambia", "location_code_parent": "", "country_iso_code": "GM", "location_type": "Country"}, {"location_code": "2275", "location_name": "Palestine", "location_code_parent": "", "country_iso_code": "PS", "location_type": "Region"}, {"location_code": "2276", "location_name": "Germany", "location_code_parent": "", "country_iso_code": "DE", "location_type": "Country"}, {"location_code": "2288", "location_name": "Ghana", "location_code_parent": "", "country_iso_code": "GH", "location_type": "Country"}, {"location_code": "2292", "location_name": "Gibraltar", "location_code_parent": "", "country_iso_code": "GI", "location_type": "Region"}, {"location_code": "2296", "location_name": "Kiribati", "location_code_parent": "", "country_iso_code": "KI", "location_type": "Country"}, {"location_code": "2300", "location_name": "Greece", "location_code_parent": "", "country_iso_code": "GR", "location_type": "Country"}, {"location_code": "2304", "location_name": "Greenland", "location_code_parent": "", "country_iso_code": "GL", "location_type": "Region"}, {"location_code": "2308", "location_name": "Grenada", "location_code_parent": "", "country_iso_code": "GD", "location_type": "Country"}, {"location_code": "2312", "location_name": "Guadeloupe", "location_code_parent": "", "country_iso_code": "GP", "location_type": "Region"}, {"location_code": "2316", "location_name": "Guam", "location_code_parent": "", "country_iso_code": "GU", "location_type": "Country"}, {"location_code": "2320", "location_name": "Guatemala", "location_code_parent": "", "country_iso_code": "GT", "location_type": "Country"}, {"location_code": "2324", "location_name": "Guinea", "location_code_parent": "", "country_iso_code": "GN", "location_type": "Country"}, {"location_code": "2328", "location_name": "Guyana", "location_code_parent": "", "country_iso_code": "GY", "location_type": "Country"}, {"location_code": "2332", "location_name": "Haiti", "location_code_parent": "", "country_iso_code": "HT", "location_type": "Country"}, {"location_code": "2334", "location_name": "Heard Island and McDonald Islands", "location_code_parent": "", "country_iso_code": "HM", "location_type": "Country"}, {"location_code": "2336", "location_name": "Vatican City", "location_code_parent": "", "country_iso_code": "VA", "location_type": "Country"}, {"location_code": "2340", "location_name": "Honduras", "location_code_parent": "", "country_iso_code": "HN", "location_type": "Country"}, {"location_code": "2344", "location_name": "Hong Kong", "location_code_parent": "", "country_iso_code": "HK", "location_type": "Region"}, {"location_code": "2348", "location_name": "Hungary", "location_code_parent": "", "country_iso_code": "HU", "location_type": "Country"}, {"location_code": "2352", "location_name": "Iceland", "location_code_parent": "", "country_iso_code": "IS", "location_type": "Country"}, {"location_code": "2356", "location_name": "India", "location_code_parent": "", "country_iso_code": "IN", "location_type": "Country"}, {"location_code": "2360", "location_name": "Indonesia", "location_code_parent": "", "country_iso_code": "ID", "location_type": "Country"}, {"location_code": "2368", "location_name": "Iraq", "location_code_parent": "", "country_iso_code": "IQ", "location_type": "Country"}, {"location_code": "2372", "location_name": "Ireland", "location_code_parent": "", "country_iso_code": "IE", "location_type": "Country"}, {"location_code": "2376", "location_name": "Israel", "location_code_parent": "", "country_iso_code": "IL", "location_type": "Country"}, {"location_code": "2380", "location_name": "Italy", "location_code_parent": "", "country_iso_code": "IT", "location_type": "Country"}, {"location_code": "2384", "location_name": "Cote d'Ivoire", "location_code_parent": "", "country_iso_code": "CI", "location_type": "Country"}, {"location_code": "2388", "location_name": "Jamaica", "location_code_parent": "", "country_iso_code": "JM", "location_type": "Country"}, {"location_code": "2392", "location_name": "Japan", "location_code_parent": "", "country_iso_code": "JP", "location_type": "Country"}, {"location_code": "2398", "location_name": "Kazakhstan", "location_code_parent": "", "country_iso_code": "KZ", "location_type": "Country"}, {"location_code": "2400", "location_name": "Jordan", "location_code_parent": "", "country_iso_code": "JO", "location_type": "Country"}, {"location_code": "2404", "location_name": "Kenya", "location_code_parent": "", "country_iso_code": "KE", "location_type": "Country"}, {"location_code": "2410", "location_name": "South Korea", "location_code_parent": "", "country_iso_code": "KR", "location_type": "Country"}, {"location_code": "2414", "location_name": "Kuwait", "location_code_parent": "", "country_iso_code": "KW", "location_type": "Country"}, {"location_code": "2417", "location_name": "Kyrgyzstan", "location_code_parent": "", "country_iso_code": "KG", "location_type": "Country"}, {"location_code": "2418", "location_name": "Laos", "location_code_parent": "", "country_iso_code": "LA", "location_type": "Country"}, {"location_code": "2422", "location_name": "Lebanon", "location_code_parent": "", "country_iso_code": "LB", "location_type": "Country"}, {"location_code": "2426", "location_name": "Lesotho", "location_code_parent": "", "country_iso_code": "LS", "location_type": "Country"}, {"location_code": "2428", "location_name": "Latvia", "location_code_parent": "", "country_iso_code": "LV", "location_type": "Country"}, {"location_code": "2430", "location_name": "Liberia", "location_code_parent": "", "country_iso_code": "LR", "location_type": "Country"}, {"location_code": "2434", "location_name": "Libya", "location_code_parent": "", "country_iso_code": "LY", "location_type": "Country"}, {"location_code": "2438", "location_name": "Liechtenstein", "location_code_parent": "", "country_iso_code": "LI", "location_type": "Country"}, {"location_code": "2440", "location_name": "Lithuania", "location_code_parent": "", "country_iso_code": "LT", "location_type": "Country"}, {"location_code": "2442", "location_name": "Luxembourg", "location_code_parent": "", "country_iso_code": "LU", "location_type": "Country"}, {"location_code": "2446", "location_name": "Macao", "location_code_parent": "", "country_iso_code": "MO", "location_type": "Region"}, {"location_code": "2450", "location_name": "Madagascar", "location_code_parent": "", "country_iso_code": "MG", "location_type": "Country"}, {"location_code": "2454", "location_name": "Malawi", "location_code_parent": "", "country_iso_code": "MW", "location_type": "Country"}, {"location_code": "2458", "location_name": "Malaysia", "location_code_parent": "", "country_iso_code": "MY", "location_type": "Country"}, {"location_code": "2462", "location_name": "Maldives", "location_code_parent": "", "country_iso_code": "MV", "location_type": "Country"}, {"location_code": "2466", "location_name": "Mali", "location_code_parent": "", "country_iso_code": "ML", "location_type": "Country"}, {"location_code": "2470", "location_name": "Malta", "location_code_parent": "", "country_iso_code": "MT", "location_type": "Country"}, {"location_code": "2474", "location_name": "Martinique", "location_code_parent": "", "country_iso_code": "MQ", "location_type": "Region"}, {"location_code": "2478", "location_name": "Mauritania", "location_code_parent": "", "country_iso_code": "MR", "location_type": "Country"}, {"location_code": "2480", "location_name": "Mauritius", "location_code_parent": "", "country_iso_code": "MU", "location_type": "Country"}, {"location_code": "2484", "location_name": "Mexico", "location_code_parent": "", "country_iso_code": "MX", "location_type": "Country"}, {"location_code": "2492", "location_name": "Monaco", "location_code_parent": "", "country_iso_code": "MC", "location_type": "Country"}, {"location_code": "2496", "location_name": "Mongolia", "location_code_parent": "", "country_iso_code": "MN", "location_type": "Country"}, {"location_code": "2498", "location_name": "Moldova", "location_code_parent": "", "country_iso_code": "MD", "location_type": "Country"}, {"location_code": "2499", "location_name": "Montenegro", "location_code_parent": "", "country_iso_code": "ME", "location_type": "Country"}, {"location_code": "2500", "location_name": "Montserrat", "location_code_parent": "", "country_iso_code": "MS", "location_type": "Region"}, {"location_code": "2504", "location_name": "Morocco", "location_code_parent": "", "country_iso_code": "MA", "location_type": "Country"}, {"location_code": "2508", "location_name": "Mozambique", "location_code_parent": "", "country_iso_code": "MZ", "location_type": "Country"}, {"location_code": "2512", "location_name": "Oman", "location_code_parent": "", "country_iso_code": "OM", "location_type": "Country"}, {"location_code": "2516", "location_name": "Namibia", "location_code_parent": "", "country_iso_code": "NA", "location_type": "Country"}, {"location_code": "2520", "location_name": "Nauru", "location_code_parent": "", "country_iso_code": "NR", "location_type": "Country"}, {"location_code": "2524", "location_name": "Nepal", "location_code_parent": "", "country_iso_code": "NP", "location_type": "Country"}, {"location_code": "2528", "location_name": "Netherlands", "location_code_parent": "", "country_iso_code": "NL", "location_type": "Country"}, {"location_code": "2531", "location_name": "Curacao", "location_code_parent": "", "country_iso_code": "CW", "location_type": "Country"}, {"location_code": "2533", "location_name": "Aruba", "location_code_parent": "", "country_iso_code": "AW", "location_type": "Region"}, {"location_code": "2534", "location_name": "Sint Maarten", "location_code_parent": "", "country_iso_code": "SX", "location_type": "Country"}, {"location_code": "2535", "location_name": "Caribbean Netherlands", "location_code_parent": "", "country_iso_code": "BQ", "location_type": "Country"}, {"location_code": "2540", "location_name": "New Caledonia", "location_code_parent": "", "country_iso_code": "NC", "location_type": "Country"}, {"location_code": "2548", "location_name": "Vanuatu", "location_code_parent": "", "country_iso_code": "VU", "location_type": "Country"}, {"location_code": "2554", "location_name": "New Zealand", "location_code_parent": "", "country_iso_code": "NZ", "location_type": "Country"}, {"location_code": "2558", "location_name": "Nicaragua", "location_code_parent": "", "country_iso_code": "NI", "location_type": "Country"}, {"location_code": "2562", "location_name": "Niger", "location_code_parent": "", "country_iso_code": "NE", "location_type": "Country"}, {"location_code": "2566", "location_name": "Nigeria", "location_code_parent": "", "country_iso_code": "NG", "location_type": "Country"}, {"location_code": "2570", "location_name": "Niue", "location_code_parent": "", "country_iso_code": "NU", "location_type": "Country"}, {"location_code": "2574", "location_name": "Norfolk Island", "location_code_parent": "", "country_iso_code": "NF", "location_type": "Country"}, {"location_code": "2578", "location_name": "Norway", "location_code_parent": "", "country_iso_code": "NO", "location_type": "Country"}, {"location_code": "2580", "location_name": "Northern Mariana Islands", "location_code_parent": "", "country_iso_code": "MP", "location_type": "Country"}, {"location_code": "2581", "location_name": "United States Minor Outlying Islands", "location_code_parent": "", "country_iso_code": "UM", "location_type": "Country"}, {"location_code": "2583", "location_name": "Micronesia", "location_code_parent": "", "country_iso_code": "FM", "location_type": "Country"}, {"location_code": "2584", "location_name": "Marshall Islands", "location_code_parent": "", "country_iso_code": "MH", "location_type": "Country"}, {"location_code": "2585", "location_name": "<PERSON><PERSON>", "location_code_parent": "", "country_iso_code": "PW", "location_type": "Country"}, {"location_code": "2586", "location_name": "Pakistan", "location_code_parent": "", "country_iso_code": "PK", "location_type": "Country"}, {"location_code": "2591", "location_name": "Panama", "location_code_parent": "", "country_iso_code": "PA", "location_type": "Country"}, {"location_code": "2598", "location_name": "Papua New Guinea", "location_code_parent": "", "country_iso_code": "PG", "location_type": "Country"}, {"location_code": "2600", "location_name": "Paraguay", "location_code_parent": "", "country_iso_code": "PY", "location_type": "Country"}, {"location_code": "2604", "location_name": "Peru", "location_code_parent": "", "country_iso_code": "PE", "location_type": "Country"}, {"location_code": "2608", "location_name": "Philippines", "location_code_parent": "", "country_iso_code": "PH", "location_type": "Country"}, {"location_code": "2612", "location_name": "Pitcairn Islands", "location_code_parent": "", "country_iso_code": "PN", "location_type": "Country"}, {"location_code": "2616", "location_name": "Poland", "location_code_parent": "", "country_iso_code": "PL", "location_type": "Country"}, {"location_code": "2620", "location_name": "Portugal", "location_code_parent": "", "country_iso_code": "PT", "location_type": "Country"}, {"location_code": "2624", "location_name": "Guinea-Bissau", "location_code_parent": "", "country_iso_code": "GW", "location_type": "Country"}, {"location_code": "2626", "location_name": "Timor-Leste", "location_code_parent": "", "country_iso_code": "TL", "location_type": "Country"}, {"location_code": "2630", "location_name": "Puerto Rico", "location_code_parent": "", "country_iso_code": "PR", "location_type": "Region"}, {"location_code": "2634", "location_name": "Qatar", "location_code_parent": "", "country_iso_code": "QA", "location_type": "Country"}, {"location_code": "2638", "location_name": "Reunion", "location_code_parent": "", "country_iso_code": "RE", "location_type": "Region"}, {"location_code": "2642", "location_name": "Romania", "location_code_parent": "", "country_iso_code": "RO", "location_type": "Country"}, {"location_code": "2646", "location_name": "Rwanda", "location_code_parent": "", "country_iso_code": "RW", "location_type": "Country"}, {"location_code": "2654", "location_name": "Saint Helena, Ascension and Tristan <PERSON>ha", "location_code_parent": "", "country_iso_code": "SH", "location_type": "Country"}, {"location_code": "2659", "location_name": "Saint Kitts and Nevis", "location_code_parent": "", "country_iso_code": "KN", "location_type": "Country"}, {"location_code": "2660", "location_name": "<PERSON><PERSON><PERSON>", "location_code_parent": "", "country_iso_code": "AI", "location_type": "Region"}, {"location_code": "2662", "location_name": "Saint Lucia", "location_code_parent": "", "country_iso_code": "LC", "location_type": "Country"}, {"location_code": "2663", "location_name": "Saint <PERSON>", "location_code_parent": "", "country_iso_code": "MF", "location_type": "Country"}, {"location_code": "2666", "location_name": "Saint Pierre and Miquelon", "location_code_parent": "", "country_iso_code": "PM", "location_type": "Country"}, {"location_code": "2670", "location_name": "Saint Vincent and the Grenadines", "location_code_parent": "", "country_iso_code": "VC", "location_type": "Country"}, {"location_code": "2674", "location_name": "San Marino", "location_code_parent": "", "country_iso_code": "SM", "location_type": "Country"}, {"location_code": "2678", "location_name": "Sao Tome and Principe", "location_code_parent": "", "country_iso_code": "ST", "location_type": "Country"}, {"location_code": "2682", "location_name": "Saudi Arabia", "location_code_parent": "", "country_iso_code": "SA", "location_type": "Country"}, {"location_code": "2686", "location_name": "Senegal", "location_code_parent": "", "country_iso_code": "SN", "location_type": "Country"}, {"location_code": "2688", "location_name": "Serbia", "location_code_parent": "", "country_iso_code": "RS", "location_type": "Country"}, {"location_code": "2690", "location_name": "Seychelles", "location_code_parent": "", "country_iso_code": "SC", "location_type": "Country"}, {"location_code": "2694", "location_name": "Sierra Leone", "location_code_parent": "", "country_iso_code": "SL", "location_type": "Country"}, {"location_code": "2702", "location_name": "Singapore", "location_code_parent": "", "country_iso_code": "SG", "location_type": "Country"}, {"location_code": "2703", "location_name": "Slovakia", "location_code_parent": "", "country_iso_code": "SK", "location_type": "Country"}, {"location_code": "2704", "location_name": "Vietnam", "location_code_parent": "", "country_iso_code": "VN", "location_type": "Country"}, {"location_code": "2705", "location_name": "Slovenia", "location_code_parent": "", "country_iso_code": "SI", "location_type": "Country"}, {"location_code": "2706", "location_name": "Somalia", "location_code_parent": "", "country_iso_code": "SO", "location_type": "Country"}, {"location_code": "2710", "location_name": "South Africa", "location_code_parent": "", "country_iso_code": "ZA", "location_type": "Country"}, {"location_code": "2716", "location_name": "Zimbabwe", "location_code_parent": "", "country_iso_code": "ZW", "location_type": "Country"}, {"location_code": "2724", "location_name": "Spain", "location_code_parent": "", "country_iso_code": "ES", "location_type": "Country"}, {"location_code": "2732", "location_name": "Western Sahara", "location_code_parent": "", "country_iso_code": "EH", "location_type": "Region"}, {"location_code": "2740", "location_name": "Suriname", "location_code_parent": "", "country_iso_code": "SR", "location_type": "Country"}, {"location_code": "2744", "location_name": "Svalbard and <PERSON>", "location_code_parent": "", "country_iso_code": "SJ", "location_type": "Region"}, {"location_code": "2748", "location_name": "<PERSON><PERSON><PERSON><PERSON>", "location_code_parent": "", "country_iso_code": "SZ", "location_type": "Country"}, {"location_code": "2752", "location_name": "Sweden", "location_code_parent": "", "country_iso_code": "SE", "location_type": "Country"}, {"location_code": "2756", "location_name": "Switzerland", "location_code_parent": "", "country_iso_code": "CH", "location_type": "Country"}, {"location_code": "2762", "location_name": "Tajikistan", "location_code_parent": "", "country_iso_code": "TJ", "location_type": "Country"}, {"location_code": "2764", "location_name": "Thailand", "location_code_parent": "", "country_iso_code": "TH", "location_type": "Country"}, {"location_code": "2768", "location_name": "Togo", "location_code_parent": "", "country_iso_code": "TG", "location_type": "Country"}, {"location_code": "2772", "location_name": "Tokelau", "location_code_parent": "", "country_iso_code": "TK", "location_type": "Country"}, {"location_code": "2776", "location_name": "Tonga", "location_code_parent": "", "country_iso_code": "TO", "location_type": "Country"}, {"location_code": "2780", "location_name": "Trinidad and Tobago", "location_code_parent": "", "country_iso_code": "TT", "location_type": "Country"}, {"location_code": "2784", "location_name": "United Arab Emirates", "location_code_parent": "", "country_iso_code": "AE", "location_type": "Country"}, {"location_code": "2788", "location_name": "Tunisia", "location_code_parent": "", "country_iso_code": "TN", "location_type": "Country"}, {"location_code": "2792", "location_name": "Turkiye", "location_code_parent": "", "country_iso_code": "TR", "location_type": "Country"}, {"location_code": "2795", "location_name": "Turkmenistan", "location_code_parent": "", "country_iso_code": "TM", "location_type": "Country"}, {"location_code": "2796", "location_name": "Turks and Caicos Islands", "location_code_parent": "", "country_iso_code": "TC", "location_type": "Region"}, {"location_code": "2798", "location_name": "Tuvalu", "location_code_parent": "", "country_iso_code": "TV", "location_type": "Country"}, {"location_code": "2800", "location_name": "Uganda", "location_code_parent": "", "country_iso_code": "UG", "location_type": "Country"}, {"location_code": "2804", "location_name": "Ukraine", "location_code_parent": "", "country_iso_code": "UA", "location_type": "Country"}, {"location_code": "2807", "location_name": "North Macedonia", "location_code_parent": "", "country_iso_code": "MK", "location_type": "Country"}, {"location_code": "2818", "location_name": "Egypt", "location_code_parent": "", "country_iso_code": "EG", "location_type": "Country"}, {"location_code": "2826", "location_name": "United Kingdom", "location_code_parent": "", "country_iso_code": "GB", "location_type": "Country"}, {"location_code": "2831", "location_name": "Guernsey", "location_code_parent": "", "country_iso_code": "GG", "location_type": "Country"}, {"location_code": "2832", "location_name": "Jersey", "location_code_parent": "", "country_iso_code": "JE", "location_type": "Country"}, {"location_code": "2833", "location_name": "Isle of Man", "location_code_parent": "", "country_iso_code": "IM", "location_type": "Country"}, {"location_code": "2834", "location_name": "Tanzania", "location_code_parent": "", "country_iso_code": "TZ", "location_type": "Country"}, {"location_code": "2840", "location_name": "United States", "location_code_parent": "", "country_iso_code": "US", "location_type": "Country"}, {"location_code": "2850", "location_name": "U.S. Virgin Islands", "location_code_parent": "", "country_iso_code": "VI", "location_type": "Region"}, {"location_code": "2854", "location_name": "Burkina Faso", "location_code_parent": "", "country_iso_code": "BF", "location_type": "Country"}, {"location_code": "2858", "location_name": "Uruguay", "location_code_parent": "", "country_iso_code": "UY", "location_type": "Country"}, {"location_code": "2860", "location_name": "Uzbekistan", "location_code_parent": "", "country_iso_code": "UZ", "location_type": "Country"}, {"location_code": "2862", "location_name": "Venezuela", "location_code_parent": "", "country_iso_code": "VE", "location_type": "Country"}, {"location_code": "2876", "location_name": "Wallis and Futuna", "location_code_parent": "", "country_iso_code": "WF", "location_type": "Country"}, {"location_code": "2882", "location_name": "Samoa", "location_code_parent": "", "country_iso_code": "WS", "location_type": "Country"}, {"location_code": "2887", "location_name": "Yemen", "location_code_parent": "", "country_iso_code": "YE", "location_type": "Country"}, {"location_code": "2894", "location_name": "Zambia", "location_code_parent": "", "country_iso_code": "ZM", "location_type": "Country"}, {"location_code": "2900", "location_name": "Kosovo", "location_code_parent": "", "country_iso_code": "XK", "location_type": "Region"}]