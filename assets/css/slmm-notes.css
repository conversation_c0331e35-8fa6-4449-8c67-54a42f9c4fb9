/**
 * SLMM Notes Feature - Dark Theme Styles
 * 
 * Dark theme styling for the notes popup feature
 * 
 * @package SLMM_SEO_Bundle
 * @since 4.8.1
 */

/* Admin Bar Styling */
#wp-admin-bar-slmm-notes .ab-item {
    background: transparent !important;
    cursor: pointer;
}

#wp-admin-bar-slmm-notes:hover .ab-item {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #72aee6 !important;
}

#wp-admin-bar-slmm-notes .ab-icon {
    font-size: 16px !important;
    margin-right: 6px;
    top: 3px;
}

#wp-admin-bar-slmm-notes .ab-label {
    font-weight: 500;
}

/* Purple background with white text when notes exist */
#wp-admin-bar-slmm-notes.slmm-notes-has-content .ab-item {
    background: #7C3AED !important;
    color: #ffffff !important;
    border-radius: 4px !important;
}

#wp-admin-bar-slmm-notes.slmm-notes-has-content .ab-icon:before {
    color: #ffffff !important;
}

#wp-admin-bar-slmm-notes.slmm-notes-has-content .ab-label {
    color: #ffffff !important;
}

#wp-admin-bar-slmm-notes.slmm-notes-has-content:hover .ab-item {
    background: #9F7AEA !important;
    color: #ffffff !important;
}

#wp-admin-bar-slmm-notes.slmm-notes-has-content:hover .ab-icon:before {
    color: #ffffff !important;
}

#wp-admin-bar-slmm-notes.slmm-notes-has-content:hover .ab-label {
    color: #ffffff !important;
}

h3.slmm-notes-title {
    margin: 0 !important;
}

/* Overlay */
.slmm-notes-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999998;
    backdrop-filter: blur(2px);
}

/* Main Popup Container */
.slmm-notes-popup {
    position: fixed;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 800px;
    max-width: 90vw;
    height: 500px;
    max-height: 90vh;
    background: #1e1e1e;
    border: 1px solid #444;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    z-index: 999999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    color: #e0e0e0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0 !important;
}

/* Header */
.slmm-notes-header {
    background: linear-gradient(135deg, #7C3AED 0%, #5B21B6 100%);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #444;
    flex-shrink: 0;
}

.slmm-notes-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.slmm-notes-title .dashicons {
    font-size: 20px;
}

.slmm-notes-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.slmm-notes-status {
    font-size: 14px;
    color: #cbd5e1;
    font-weight: 500;
    min-width: 80px;
    text-align: right;
}

.slmm-notes-close {
    background: transparent;
    border: none;
    color: #ffffff;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.slmm-notes-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.slmm-notes-close .dashicons {
    font-size: 18px;
}

/* Content Area */
.slmm-notes-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #2a2a2a;
    overflow: hidden;
}

/* Toolbar */
.slmm-notes-toolbar {
    padding: 12px 16px;
    background: #333333;
    border-bottom: 1px solid #444;
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
}

.slmm-notes-format-btn {
    background: #404040;
    border: 1px solid #555;
    color: #e0e0e0;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.slmm-notes-format-btn:hover {
    background: #7C3AED;
    border-color: #7C3AED;
    color: #ffffff;
}

.slmm-notes-format-btn.active {
    background: #7C3AED;
    border-color: #7C3AED;
    color: #ffffff;
}

/* Compact Shortcuts Info */
.slmm-notes-shortcuts-info {
    margin-left: 16px;
    padding-left: 16px;
    border-left: 1px solid #555;
    flex-shrink: 0;
}

.slmm-shortcuts-line {
    font-size: 11px;
    color: #aaa;
    line-height: 1.3;
    margin: 1px 0;
}

.slmm-shortcuts-line strong {
    color: #7C3AED;
    font-weight: 600;
}

.slmm-notes-divider {
    width: 1px;
    height: 24px;
    background: #555;
    margin: 0 8px;
}

/* Editor Container */
.slmm-notes-editor-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: calc(100% - 120px); /* Account for header and toolbar */
}

.slmm-notes-editor {
    min-height: 100%;
    background: transparent;
    border: none;
    outline: none;
    font-size: 14px;
    line-height: 1.6;
    color: #e0e0e0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.slmm-notes-editor:focus {
    outline: none;
    border: none;
}

.slmm-notes-editor[contenteditable="true"] {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

.slmm-notes-editor:empty:before {
    content: attr(placeholder);
    color: #888;
    font-style: italic;
}

.slmm-notes-editor h1,
.slmm-notes-editor h2,
.slmm-notes-editor h3,
.slmm-notes-editor h4,
.slmm-notes-editor h5,
.slmm-notes-editor h6 {
    color: #ffffff;
    margin: 1em 0 0.5em 0;
}

.slmm-notes-editor h1:first-child,
.slmm-notes-editor h2:first-child,
.slmm-notes-editor h3:first-child,
.slmm-notes-editor h4:first-child,
.slmm-notes-editor h5:first-child,
.slmm-notes-editor h6:first-child {
    margin-top: 0;
}

.slmm-notes-editor p {
    margin: 0 0 1em 0;
}

.slmm-notes-editor ul,
.slmm-notes-editor ol {
    margin: 1em 0;
    padding-left: 2em;
}

.slmm-notes-editor li {
    margin: 0.5em 0;
}

.slmm-notes-editor a {
    color: #72aee6;
    text-decoration: underline;
}

.slmm-notes-editor a:hover {
    color: #96c9f4;
}

.slmm-notes-editor strong {
    color: #ffffff;
    font-weight: 600;
}

.slmm-notes-editor s {
    color: #888;
    text-decoration: line-through;
}

.slmm-notes-editor blockquote {
    border-left: 4px solid #7C3AED;
    padding-left: 16px;
    margin: 1em 0;
    color: #cbd5e1;
    font-style: italic;
}

/* Standard Lists */
.slmm-notes-editor ul {
    list-style-type: disc;
    margin: 1em 0;
    padding-left: 2em;
}

.slmm-notes-editor ol {
    list-style-type: decimal;
    margin: 1em 0;
    padding-left: 2em;
}

.slmm-notes-editor li {
    margin: 0.25em 0;
    color: #ffffff;
}

/* Ensure checkboxes work in contenteditable */
.slmm-notes-editor input[type="checkbox"] {
    pointer-events: auto;
}

/* Footer */
.slmm-notes-footer {
    background: #333333;
    border-top: 1px solid #444;
    padding: 8px 16px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-shrink: 0;
}

/* Removed resize handle */

/* Removed resizable functionality */

/* Status Messages */
.slmm-notes-status.saving {
    color: #fbbf24;
}

.slmm-notes-status.saved {
    color: #10b981;
}

.slmm-notes-status.error {
    color: #ef4444;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) !important;
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) !important;
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) !important;
    }
    to {
        opacity: 0;
        transform: translate(-50%, -60%) !important;
    }
}

.slmm-notes-popup.show {
    animation: slideIn 0.3s ease-out;
}

.slmm-notes-popup.hide {
    animation: slideOut 0.3s ease-in;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .slmm-notes-popup {
        width: 95vw;
        height: 80vh;
        max-width: none;
    }
    
    .slmm-notes-toolbar {
        flex-wrap: wrap;
        gap: 2px;
    }
    
    .slmm-notes-format-btn {
        padding: 4px 8px;
        font-size: 12px;
        min-width: 28px;
        height: 28px;
    }
    
    .slmm-notes-title {
        font-size: 16px;
    }
    
    .slmm-notes-header {
        padding: 12px 16px;
    }
    
    .slmm-notes-editor-container {
        padding: 16px;
    }
    
    .slmm-notes-shortcuts-info {
        margin-left: 8px;
        padding-left: 8px;
        flex-basis: 100%;
        margin-top: 8px;
        border-left: none;
        border-top: 1px solid #555;
        padding-top: 6px;
    }
    
    .slmm-shortcuts-line {
        font-size: 10px;
    }
}

/* Scrollbar Styling for Dark Theme */
.slmm-notes-editor-container::-webkit-scrollbar {
    width: 8px;
}

.slmm-notes-editor-container::-webkit-scrollbar-track {
    background: #1e1e1e;
}

.slmm-notes-editor-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.slmm-notes-editor-container::-webkit-scrollbar-thumb:hover {
    background: #666;
} 