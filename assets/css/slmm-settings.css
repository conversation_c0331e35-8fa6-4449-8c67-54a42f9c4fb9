/* SLMM Settings Dark Theme - Scoped Variables */
.slmm-settings-wrap {
    --slmm-primary: #7C3AED;
    --slmm-primary-hover: #8B5CF6;
    --slmm-primary-light: rgba(124, 58, 237, 0.08);
    --slmm-primary-subtle: rgba(124, 58, 237, 0.04);
    --slmm-dark-bg: #0A0A0F;
    --slmm-dark-surface: #141419;
    --slmm-dark-surface-hover: #1A1A1F;
    --slmm-dark-border: #1F1F24;
    --slmm-dark-border-subtle: #16161B;
    --slmm-text-primary: #F1F5F9;
    --slmm-text-secondary: #94A3B8;
    --slmm-text-muted: #64748B;
    --slmm-text-dim: #475569;
    --slmm-success: #10B981;
    --slmm-warning: #F59E0B;
    --slmm-error: #EF4444;
    --slmm-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    --slmm-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.4);
}

/* Main Container */
.slmm-settings-wrap {
    background: var(--slmm-dark-bg);
    color: var(--slmm-text-primary);
    min-height: 100vh;
    margin: 0 -20px -10px;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.slmm-settings-wrap * {
    box-sizing: border-box;
}

/* Header */
.slmm-settings-header {
    background: var(--slmm-dark-surface);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--slmm-dark-border-subtle);
}

.slmm-settings-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--slmm-text-primary);
}

.slmm-logo {
    font-size: 1.25rem;
    color: var(--slmm-text-dim);
}

.slmm-settings-subtitle {
    margin: 0.25rem 0 0;
    font-size: 1.875rem;
    font-weight: 400;
}

/* Container */
.slmm-settings-container {
    padding: 2rem;
    max-width: 1920px;
    margin: 0 auto;
}

/* Tabs Wrapper */
.slmm-tabs-wrapper {
    background: var(--slmm-dark-surface);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--slmm-dark-border-subtle);
}

/* Tabs Navigation */
.slmm-tabs-nav {
    display: flex;
    background: var(--slmm-dark-bg);
    border-bottom: 1px solid var(--slmm-dark-border);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.slmm-tabs-nav::-webkit-scrollbar {
    display: none;
}

.slmm-tab-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.25rem;
    background: none;
    border: none;
    color: var(--slmm-text-primary);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    white-space: nowrap;
    min-width: fit-content;
}

.slmm-tab-button:hover {
    color: var(--slmm-text-secondary);
    background: var(--slmm-dark-surface);
}

.slmm-tab-button.active {
    color: var(--slmm-text-primary);
    background: var(--slmm-dark-surface);
}

.slmm-tab-button.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--slmm-primary);
}

.slmm-tab-icon {
    width: 16px;
    height: 16px;
    opacity: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;
}

.slmm-tab-icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.slmm-tab-button.active .slmm-tab-icon {
    opacity: 1;
}

/* Tab Icons - SVG as background images */
.slmm-tab-button[data-tab="api-keys"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="%23475569"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.351 1.091a4.528 4.528 0 0 1 3.44 3.16c.215.724.247 1.49.093 2.23a4.583 4.583 0 0 1-4.437 3.6c-.438 0-.874-.063-1.293-.19l-.8.938-.379.175H7v1.5l-.5.5H5v1.5l-.5.5h-3l-.5-.5v-2.307l.146-.353L6.12 6.87a4.464 4.464 0 0 1-.2-1.405 4.528 4.528 0 0 1 5.431-4.375zm1.318 7.2a3.568 3.568 0 0 0 1.239-2.005l.004.005A3.543 3.543 0 0 0 9.72 2.08a3.576 3.576 0 0 0-2.8 3.4c-.01.456.07.908.239 1.33l-.11.543L2 12.404v1.6h2v-1.5l.5-.5H6v-1.5l.5-.5h1.245l.876-1.016.561-.14a3.47 3.47 0 0 0 1.269.238 3.568 3.568 0 0 0 2.218-.795zm-.838-2.732a1 1 0 1 0-1.662-1.11 1 1 0 0 0 1.662 1.11z"/></svg>');
}

.slmm-tab-button[data-tab="prompts"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2h-4.586l-2.707 2.707a1 1 0 0 1-1.414 0L8.586 19H4a2 2 0 0 1-2-2V6zm18 0H4v11h5a1 1 0 0 1 .707.293L12 19.586l2.293-2.293A1 1 0 0 1 15 17h5V6zM6 9.5a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1zm0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1z" fill="%23475569"/></svg>');
}

.slmm-tab-button[data-tab="business-info"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg fill="%23475569" width="16" height="16" viewBox="0 0 24 24" version="1.2" baseProfile="tiny" xmlns="http://www.w3.org/2000/svg"><path d="M21.17,15.4l-5.91-9.85C14.48,4.25,13.3,3.51,12,3.51S9.52,4.25,8.74,5.54L2.83,15.4c-0.44,0.73-0.66,1.49-0.66,2.21 c0,0.57,0.14,1.13,0.42,1.62C3.23,20.35,4.47,21,6,21h12c1.53,0,2.77-0.65,3.41-1.77c0.28-0.49,0.42-1.02,0.42-1.58 C21.84,16.91,21.62,16.14,21.17,15.4z M12,8.45c0.85,0,1.55,0.7,1.55,1.55c0,0.85-0.69,1.55-1.55,1.55c-0.85,0-1.55-0.7-1.55-1.55 C10.45,9.14,11.14,8.45,12,8.45z M13.69,16.91c-0.03,0.04-0.8,0.92-2.07,0.92l-0.15,0c-0.51-0.03-0.93-0.25-1.18-0.63 c-0.31-0.47-0.36-1.11-0.12-1.82l0.41-1.22c0.23-0.68,0.01-0.79-0.11-0.85l-0.14-0.02c-0.25,0-0.6,0.15-0.71,0.21 c-0.1,0.05-0.23,0.03-0.31-0.07c-0.07-0.1-0.07-0.23,0.01-0.32c0.03-0.04,0.87-0.99,2.22-0.91c0.51,0.03,0.93,0.25,1.18,0.63 c0.32,0.47,0.36,1.11,0.12,1.83l-0.41,1.22c-0.23,0.68-0.01,0.79,0.11,0.85l0.14,0.02c0.25,0,0.6-0.15,0.71-0.2 c0.11-0.06,0.23-0.03,0.31,0.07C13.77,16.69,13.77,16.82,13.69,16.91z"/></svg>');
}

.slmm-tab-button[data-tab="models"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><g><path d="M45.6,18.7L41,14.9V7.5a1,1,0,0,0-.6-.9L30.5,2.1h-.4l-.6.2L24,5.9,18.5,2.2,17.9,2h-.4L7.6,6.6a1,1,0,0,0-.6.9v7.4L2.4,18.7a.8.8,0,0,0-.4.8v9H2a.8.8,0,0,0,.4.8L7,33.1v7.4a1,1,0,0,0,.6.9l9.9,4.5h.4l.6-.2L24,42.1l5.5,3.7.6.2h.4l9.9-4.5a1,1,0,0,0,.6-.9V33.1l4.6-3.8a.8.8,0,0,0,.4-.7V19.4h0A.8.8,0,0,0,45.6,18.7Zm-5.1,6.8H42v1.6l-3.5,2.8-.4.3-.4-.2a1.4,1.4,0,0,0-2,.7,1.5,1.5,0,0,0,.6,2l.7.3h0v5.4l-6.6,3.1-4.2-2.8-.7-.5V25.5H27a1.5,1.5,0,0,0,0-3H25.5V9.7l.7-.5,4.2-2.8L37,9.5v5.4h0l-.7.3a1.5,1.5,0,0,0-.6,2,1.4,1.4,0,0,0,1.3.9l.7-.2.4-.2.4.3L42,20.9v1.6H40.5a1.5,1.5,0,0,0,0,3ZM21,25.5h1.5V38.3l-.7.5-4.2,2.8L11,38.5V33.1h0l.7-.3a1.5,1.5,0,0,0,.6-2,1.4,1.4,0,0,0-2-.7l-.4.2-.4-.3L6,27.1V25.5H7.5a1.5,1.5,0,0,0,0-3H6V20.9l3.5-2.8.4-.3.4.2.7.2a1.4,1.4,0,0,0,1.3-.9,1.5,1.5,0,0,0-.6-2L11,15h0V9.5l6.6-3.1,4.2,2.8.7.5V22.5H21a1.5,1.5,0,0,0,0,3Z" fill="%23475569"/><path d="M13.9,9.9a1.8,1.8,0,0,0,0,2.2l2.6,2.5v2.8l-4,4v5.2l4,4v2.8l-2.6,2.5a1.8,1.8,0,0,0,0,2.2,1.5,1.5,0,0,0,1.1.4,1.5,1.5,0,0,0,1.1-.4l3.4-3.5V29.4l-4-4V22.6l4-4V13.4L16.1,9.9A1.8,1.8,0,0,0,13.9,9.9Z" fill="%23475569"/><path d="M31.5,14.6l2.6-2.5a1.8,1.8,0,0,0,0-2.2,1.8,1.8,0,0,0-2.2,0l-3.4,3.5v5.2l4,4v2.8l-4,4v5.2l3.4,3.5a1.7,1.7,0,0,0,2.2,0,1.8,1.8,0,0,0,0-2.2l-2.6-2.5V30.6l4-4V21.4l-4-4Z" fill="%23475569"/></g></svg>');
}

.slmm-tab-button[data-tab="protected-words"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="%23475569"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.246 14.713a27.792 27.792 0 0 1-1.505-.953c-.501-.34-.983-.707-1.444-1.1-.458-.395-.888-.82-1.288-1.274-.4-.455-.753-.95-1.05-1.478a7.8 7.8 0 0 1-.7-1.69A7.041 7.041 0 0 1 2 6.3V3.1l.5-.5c.333 0 .656-.011.97-.036.296-.023.591-.066.882-.128.284-.062.562-.148.832-.256.284-.118.557-.261.816-.427a4.83 4.83 0 0 1 1.184-.565 4.8 4.8 0 0 1 2-.142 4.018 4.018 0 0 1 1.237.383c.199.097.392.204.58.322.26.167.535.31.821.428.27.109.547.194.831.256.291.062.587.106.884.129.311.024.634.035.967.035l.5.5v3.2a7.043 7.043 0 0 1-.256 1.919 7.804 7.804 0 0 1-.7 1.69 8.751 8.751 0 0 1-1.05 1.478c-.4.452-.829.877-1.286 1.27a15.94 15.94 0 0 1-1.448 1.1 28.71 28.71 0 0 1-1.51.956h-.508zM3 3.59V6.3c-.004.555.07 1.11.22 1.645a6.7 6.7 0 0 0 .61 1.473c.263.467.575.905.93 1.308.37.417.766.81 1.188 1.174.432.368.883.712 1.352 1.03.4.267.8.523 1.2.769.4-.242.8-.498 1.2-.768.47-.319.923-.663 1.355-1.031.421-.364.817-.756 1.186-1.172a7.8 7.8 0 0 0 .93-1.308c.261-.465.466-.96.61-1.473.15-.537.223-1.09.22-1.647V3.59c-.159 0-.313-.012-.465-.023l-.079-.006a7.95 7.95 0 0 1-1.018-.147 6.112 6.112 0 0 1-1.976-.814 5.166 5.166 0 0 0-.482-.27 3.123 3.123 0 0 0-.943-.29 3.686 3.686 0 0 0-1.558.106c-.332.108-.649.26-.94.452-.312.2-.64.372-.983.513a6.4 6.4 0 0 1-1 .307c-.335.07-.675.12-1.017.146-.174.01-.355.02-.54.026zm6.065 4.3a1.5 1.5 0 1 0-1.13 0L7.5 10.5h2l-.435-2.61z"/></svg>');
}

.slmm-tab-button[data-tab="features"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="%23475569"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.246 14.713a27.792 27.792 0 0 1-1.505-.953c-.501-.34-.983-.707-1.444-1.1-.458-.395-.888-.82-1.288-1.274-.4-.455-.753-.95-1.05-1.478a7.8 7.8 0 0 1-.7-1.69A7.041 7.041 0 0 1 2 6.3V3.1l.5-.5c.333 0 .656-.011.97-.036.296-.023.591-.066.882-.128.284-.062.562-.148.832-.256.284-.118.557-.261.816-.427a4.83 4.83 0 0 1 1.184-.565 4.8 4.8 0 0 1 2-.142 4.018 4.018 0 0 1 1.237.383c.199.097.392.204.58.322.26.167.535.31.821.428.27.109.547.194.831.256.291.062.587.106.884.129.311.024.634.035.967.035l.5.5v3.2a7.043 7.043 0 0 1-.256 1.919 7.804 7.804 0 0 1-.7 1.69 8.751 8.751 0 0 1-1.05 1.478c-.4.452-.829.877-1.286 1.27a15.94 15.94 0 0 1-1.448 1.1 28.71 28.71 0 0 1-1.51.956h-.508zM3 3.59V6.3c-.004.555.07 1.11.22 1.645a6.7 6.7 0 0 0 .61 1.473c.263.467.575.905.93 1.308.37.417.766.81 1.188 1.174.432.368.883.712 1.352 1.03.4.267.8.523 1.2.769.4-.242.8-.498 1.2-.768.47-.319.923-.663 1.355-1.031.421-.364.817-.756 1.186-1.172a7.8 7.8 0 0 0 .93-1.308c.261-.465.466-.96.61-1.473.15-.537.223-1.09.22-1.647V3.59c-.159 0-.313-.012-.465-.023l-.079-.006a7.95 7.95 0 0 1-1.018-.147 6.112 6.112 0 0 1-1.976-.814 5.166 5.166 0 0 0-.482-.27 3.123 3.123 0 0 0-.943-.29 3.686 3.686 0 0 0-1.558.106c-.332.108-.649.26-.94.452-.312.2-.64.372-.983.513a6.4 6.4 0 0 1-1 .307c-.335.07-.675.12-1.017.146-.174.01-.355.02-.54.026zm6.065 4.3a1.5 1.5 0 1 0-1.13 0L7.5 10.5h2l-.435-2.61z"/></svg>');
}

.slmm-tab-button[data-tab="lorem-detector"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" fill="%23475569"/><path d="M3 3h18c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H3c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2zm0 2v14h18V5H3z" fill="%23475569" opacity="0.3"/><path d="M7 9h4v2H7zm0 4h6v2H7zm0-8h10v2H7z" fill="%23475569" opacity="0.5"/></svg>');
}

.slmm-tab-button[data-tab="export-import"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg fill="%23475569" width="16" height="16" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M19.7903934,18.6127185 L19.7072026,18.7069258 L16.7071326,21.7069258 C16.6801187,21.7339397 16.6515664,21.7594153 16.6216183,21.7832098 L16.500353,21.8659223 L16.500353,21.8659223 L16.427064,21.9043128 L16.427064,21.9043128 L16.3400271,21.9405322 L16.3400271,21.9405322 L16.2335653,21.9723902 L16.2335653,21.9723902 L16.116647,21.9930913 L16.033029,21.9992768 L16.033029,21.9992768 L15.9409671,21.9980859 L15.8251966,21.9845213 L15.8251966,21.9845213 L15.6878494,21.9500809 L15.6878494,21.9500809 L15.5767675,21.9061457 L15.5767675,21.9061457 L15.4792778,21.8538236 L15.4792778,21.8538236 L15.3832241,21.7870331 L15.2928749,21.7069258 L12.2927974,18.7069258 C11.902263,18.3164015 11.902263,17.6832365 12.2927974,17.2927122 C12.6532907,16.9322283 13.2205364,16.9044987 13.6128377,17.2095236 L13.7070475,17.2927122 L14.9998966,18.584819 L14.9999741,8.99981902 C14.9999741,8.48698318 15.3860143,8.06431186 15.883353,8.00654675 L16.0000259,7.99981902 C16.5523106,7.99981902 17.0000259,8.44753427 17.0000259,8.99981902 L16.9998966,18.584819 L18.2929525,17.2927122 C18.6534458,16.9322283 19.2206915,16.9044987 19.6129929,17.2095236 L19.7072026,17.2927122 C20.0376548,17.6231559 20.0884936,18.1273245 19.859719,18.511222 L19.7903934,18.6127185 L19.7903934,18.6127185 Z M4.29279737,5.29255711 L7.29286736,2.29255711 L7.40481484,2.1959774 L7.51569719,2.12453966 L7.51569719,2.12453966 L7.62891562,2.07076785 L7.62891562,2.07076785 L7.73413453,2.03538486 L7.73413453,2.03538486 L7.82519664,2.01496161 L7.82519664,2.01496161 L7.94096709,2.00139699 L8.05914398,2.00139699 L8.05914398,2.00139699 L8.17466132,2.0149356 L8.17466132,2.0149356 L8.31274961,2.04953478 L8.31274961,2.04953478 L8.36670687,2.06905084 L8.45385903,2.10832658 L8.45385903,2.10832658 L8.52068604,2.14573132 L8.52068604,2.14573132 L8.60170489,2.20078783 L8.60170489,2.20078783 L8.66547577,2.25320781 L8.66547577,2.25320781 L8.70713264,2.29255711 L11.7072026,5.29255711 L11.7903934,5.38676445 C12.0700068,5.74636472 12.0700068,6.25296306 11.7903934,6.61256333 L11.7072026,6.70677067 L11.6129929,6.78995928 C11.2533833,7.06956543 10.7467718,7.06956543 10.3871623,6.78995928 L10.2929525,6.70677067 L8.99989658,5.41466389 L9.00002585,14.9996639 C9.00002585,15.5124997 8.61398566,15.9351711 8.11664698,15.9929362 L8.00002585,15.9996639 L7.88335302,15.9929362 C7.42427116,15.9396145 7.06002351,15.5753669 7.00670188,15.116285 L6.99997415,14.9996639 L6.99989658,5.41466389 L5.7070475,6.70677067 L5.61283773,6.78995928 C5.22053638,7.09498417 4.65329066,7.06725463 4.29279737,6.70677067 C3.93230409,6.34628671 3.90457384,5.77905565 4.20960662,5.38676445 L4.29279737,5.29255711 Z"/></svg>');
}

/* Hover and active states for icons */
.slmm-tab-button:hover[data-tab="api-keys"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="%2394A3B8"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.351 1.091a4.528 4.528 0 0 1 3.44 3.16c.215.724.247 1.49.093 2.23a4.583 4.583 0 0 1-4.437 3.6c-.438 0-.874-.063-1.293-.19l-.8.938-.379.175H7v1.5l-.5.5H5v1.5l-.5.5h-3l-.5-.5v-2.307l.146-.353L6.12 6.87a4.464 4.464 0 0 1-.2-1.405 4.528 4.528 0 0 1 5.431-4.375zm1.318 7.2a3.568 3.568 0 0 0 1.239-2.005l.004.005A3.543 3.543 0 0 0 9.72 2.08a3.576 3.576 0 0 0-2.8 3.4c-.01.456.07.908.239 1.33l-.11.543L2 12.404v1.6h2v-1.5l.5-.5H6v-1.5l.5-.5h1.245l.876-1.016.561-.14a3.47 3.47 0 0 0 1.269.238 3.568 3.568 0 0 0 2.218-.795zm-.838-2.732a1 1 0 1 0-1.662-1.11 1 1 0 0 0 1.662 1.11z"/></svg>');
}

.slmm-tab-button.active[data-tab="api-keys"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="%23F1F5F9"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.351 1.091a4.528 4.528 0 0 1 3.44 3.16c.215.724.247 1.49.093 2.23a4.583 4.583 0 0 1-4.437 3.6c-.438 0-.874-.063-1.293-.19l-.8.938-.379.175H7v1.5l-.5.5H5v1.5l-.5.5h-3l-.5-.5v-2.307l.146-.353L6.12 6.87a4.464 4.464 0 0 1-.2-1.405 4.528 4.528 0 0 1 5.431-4.375zm1.318 7.2a3.568 3.568 0 0 0 1.239-2.005l.004.005A3.543 3.543 0 0 0 9.72 2.08a3.576 3.576 0 0 0-2.8 3.4c-.01.456.07.908.239 1.33l-.11.543L2 12.404v1.6h2v-1.5l.5-.5H6v-1.5l.5-.5h1.245l.876-1.016.561-.14a3.47 3.47 0 0 0 1.269.238 3.568 3.568 0 0 0 2.218-.795zm-.838-2.732a1 1 0 1 0-1.662-1.11 1 1 0 0 0 1.662 1.11z"/></svg>');
}

/* All other tabs - hover states */
.slmm-tab-button:hover .slmm-tab-icon {
    background-image: var(--hover-icon) !important;
}

.slmm-tab-button:hover[data-tab="prompts"] .slmm-tab-icon,
.slmm-tab-button:hover[data-tab="business-info"] .slmm-tab-icon,
.slmm-tab-button:hover[data-tab="models"] .slmm-tab-icon,
.slmm-tab-button:hover[data-tab="protected-words"] .slmm-tab-icon,
.slmm-tab-button:hover[data-tab="features"] .slmm-tab-icon,
.slmm-tab-button:hover[data-tab="lorem-detector"] .slmm-tab-icon,
.slmm-tab-button:hover[data-tab="export-import"] .slmm-tab-icon {
    filter: brightness(1.4);
}

/* All tabs - active states */
.slmm-tab-button.active .slmm-tab-icon {
    filter: brightness(1.8);
}

/* Tab Content */
.slmm-tabs-content {
    position: relative;
}

.slmm-tab-pane {
    display: none;
    padding: 2rem;
    animation: fadeIn 0.3s ease;
}

.slmm-tab-pane.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slmm-tab-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--slmm-dark-border);
}

.slmm-tab-header h2 {
    margin: 0 0 0.5rem;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--slmm-text-primary);
}

.slmm-tab-header p {
    margin: 0;
    color: var(--slmm-text-secondary);
    font-size: 1rem;
}

/* Form Grid */
.slmm-form-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .slmm-form-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}

/* Features tab - 2 column layout for better fit */
#features .slmm-form-grid {
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    #features .slmm-form-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
}

/* Form Fields */
.slmm-form-field {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
    background-color: var(--slmm-dark-border-subtle);
}

.slmm-field-label {
    font-weight: 600;
    color: var(--slmm-text-primary);
    font-size: 0.95rem;
    margin: 0;
}

/* Input Styles */
.slmm-input-wrapper {
    position: relative;
}

.slmm-input {
    width: 100%;
    padding: 0.875rem 1rem;
    padding-right: 3rem;
    background: var(--slmm-dark-border-subtle) !important;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 8px;
    color: #ffffff !important;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.slmm-input:focus {
    outline: none;
    border-color: var(--slmm-primary);
    background: var(--slmm-dark-border-subtle) !important;
    color: #ffffff !important;
}

.slmm-input::placeholder {
    color: var(--slmm-text-muted);
}

.slmm-input-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1rem;
    opacity: 0.6;
    pointer-events: none;
}

/* Textarea Styles */
.slmm-textarea-wrapper {
    position: relative;
}

.slmm-textarea {
    width: 100%;
    padding: 1rem;
    background: #3a3a40;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.95rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.5;
    resize: vertical;
    min-height: 120px;
    transition: all 0.3s ease;
}

.slmm-textarea:focus {
    outline: none;
    border-color: var(--slmm-primary);
    background: #3a3a40;
    color: #ffffff;
}

.slmm-textarea::placeholder {
    color: var(--slmm-text-muted);
    font-family: inherit;
}

.slmm-protected-words {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    min-height: 200px;
}

/* Toggle Switches */
.slmm-toggle-wrapper {
    display: flex;
    align-items: center;
}

.slmm-toggle {
    display: none;
}

.slmm-toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    margin: 0;
}

.slmm-toggle-slider {
    position: relative;
    width: 52px;
    height: 28px;
    background: var(--slmm-dark-border);
    border-radius: 14px;
    transition: all 0.3s ease;
}

.slmm-toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: var(--slmm-text-primary);
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slmm-toggle:checked + .slmm-toggle-label .slmm-toggle-slider {
    background: var(--slmm-primary);
}

.slmm-toggle:checked + .slmm-toggle-label .slmm-toggle-slider::before {
    transform: translateX(24px);
    background: white;
}

.slmm-toggle-text {
    font-weight: 500;
    color: var(--slmm-text-primary);
}

/* Field Descriptions */
.slmm-field-description {
    color: var(--slmm-text-secondary);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.4;
}

.slmm-link {
    color: var(--slmm-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.slmm-link:hover {
    color: var(--slmm-primary-hover);
    text-decoration: underline;
}

/* Buttons */
.slmm-save-button {
    background: var(--slmm-primary) !important;
    border: none !important;
    color: white !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-shadow: none !important;
}

.slmm-save-button:hover {
    background: var(--slmm-primary-hover) !important;
}

.slmm-form-actions {
    margin-top: 2rem;
    margin-bottom: 2rem;
    margin-left: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--slmm-dark-border);
    text-align: left;
}

.slmm-search-replace-actions {
    margin-top: 1.5rem;
    text-align: left;
}

/* Export/Import Section */
.slmm-export-import-form {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .slmm-export-import-form {
        grid-template-columns: 1fr 1fr;
    }
}

.slmm-export-section,
.slmm-import-section {
    background: var(--slmm-dark-surface);
    padding: 1.25rem;
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border-subtle);
}

.slmm-section-title {
    margin: 0 0 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--slmm-text-primary);
}

.slmm-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.slmm-checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--slmm-text-primary);
    font-weight: 500;
    margin: 0;
}

.slmm-checkbox-label input[type="checkbox"] {
    display: none;
}

.slmm-checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.slmm-checkbox-label input[type="checkbox"]:checked + .slmm-checkmark {
    background: var(--slmm-primary);
    border-color: var(--slmm-primary);
}

.slmm-checkbox-label input[type="checkbox"]:checked + .slmm-checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* File Upload */
.slmm-file-upload {
    margin-bottom: 1.5rem;
}

.slmm-file-upload input[type="file"] {
    display: none;
}

.slmm-file-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--slmm-dark-surface);
    border: 2px dashed var(--slmm-dark-border);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--slmm-text-primary);
    font-weight: 500;
    margin: 0;
}

.slmm-file-label:hover {
    border-color: var(--slmm-dark-border);
    background: var(--slmm-dark-surface-hover);
}

.slmm-file-icon {
    font-size: 1.25rem;
    opacity: 0.8;
}

/* Secondary Buttons */
.slmm-export-button,
.slmm-import-button {
    background: var(--slmm-dark-surface) !important;
    border: 1px solid var(--slmm-primary) !important;
    color: var(--slmm-text-primary) !important;
    padding: 0.75rem 1.25rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-shadow: none !important;
}

.slmm-export-button:hover,
.slmm-import-button:hover {
    background: var(--slmm-dark-surface-hover) !important;
    border-color: var(--slmm-primary) !important;
    color: var(--slmm-primary) !important;
}

/* Model Selector Wrapper */
.slmm-model-selector-wrapper {
    background: var(--slmm-dark-surface);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid var(--slmm-dark-border);
    box-shadow: var(--slmm-shadow);
    margin-top: 0.5rem;
}

.slmm-model-selector-wrapper label strong {
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: block;
    letter-spacing: 0.025em;
}

.slmm-model-selector-wrapper .provider-select {
    background: #3a3a40;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 8px;
    color: var(--slmm-text-primary);
    padding: 0.75rem;
    width: 100%;
    max-width: 200px;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.slmm-model-selector-wrapper .provider-select:focus {
    outline: none;
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

.slmm-model-selector-wrapper input[type="text"] {
    background: #3a3a40;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 8px;
    color: #ffffff;
    padding: 0.75rem;
    width: 100%;
    max-width: 300px;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: var(--slmm-dark-border-subtle)!important;
}

.slmm-model-selector-wrapper input[type="text"]:focus {
    outline: none;
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

.slmm-model-selector-wrapper input[type="text"]::placeholder {
    color: var(--slmm-text-muted);
}

.slmm-model-selector-wrapper select.model-select {
    background: #3a3a40;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 8px;
    color: #ffffff;
    padding: 0.75rem;
    width: 100%;
    max-width: 400px;
    min-height: 120px;
    font-size: 0.875rem;
    line-height: 1.4;
    transition: all 0.3s ease;
}

.slmm-model-selector-wrapper select.model-select:focus {
    outline: none;
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

.slmm-model-selector-wrapper select.model-select option {
    background: #3a3a40;
    color: #ffffff;
    padding: 0.5rem;
}

.slmm-model-selector-wrapper .refresh-models-btn {
    background: var(--slmm-primary);
    border: none;
    border-radius: 6px;
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    margin-top: 0.75rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.slmm-model-selector-wrapper .refresh-models-btn:hover {
    background: var(--slmm-primary-hover);
    transform: translateY(-1px);
}

.slmm-model-selector-wrapper .refresh-models-btn:active {
    transform: translateY(0);
}

.slmm-model-selector-wrapper .model-count {
    font-size: 0.875rem;
    color: var(--slmm-text-secondary);
    margin-left: 0.75rem;
    font-weight: 500;
    height: 37px!important;
    align-self: center;
    align-items: center;
    border-radius: 3px;
    font-size: 12px;
    color: #fff;
    border-radius: 6px!important;
}

.slmm-model-selector-wrapper .description {
    font-size: 0.875rem;
    color: var(--slmm-text-secondary);
    margin-top: 0.75rem;
    line-height: 1.5;
}

.slmm-model-selector-wrapper .clear-model-cache {
    color: var(--slmm-primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.slmm-model-selector-wrapper .clear-model-cache:hover {
    color: var(--slmm-primary-hover);
    text-decoration: underline;
}

/* Model Selector Sections */
.slmm-provider-section,
.slmm-search-section,
.slmm-selection-section,
.slmm-actions-section {
    margin-bottom: 1.25rem;
}

.slmm-provider-section label,
.slmm-search-section label,
.slmm-selection-section label {
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
    letter-spacing: 0.025em;
}

.slmm-provider-note {
    font-size: 0.8125rem;
    color: var(--slmm-text-muted);
    margin-top: 0.5rem;
    margin-bottom: 0;
    font-style: italic;
    line-height: 1.4;
}

.slmm-actions-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--slmm-dark-border);
}

.slmm-actions-section .refresh-models-btn {
    margin: 0;
}

.slmm-actions-section .model-count {
    margin: 0;
    font-size: 0.8125rem;
    color: var(--white);
    background: #3a3a40;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    border: 1px solid var(--slmm-dark-border);
    height: 37px!important;
    align-content: center;
    border-radius: 6px!important;
    font-size: 14px;
    color: #fff;
}

/* Selected Models Section */
.slmm-selected-models {
    background: #2a2a30;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.slmm-selected-models h4 {
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.slmm-selected-models h4::before {
    content: "✓";
    color: var(--slmm-primary);
    font-weight: bold;
}

.slmm-selected-model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--slmm-dark-border);
}

.slmm-selected-model-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.slmm-selected-model-label {
    color: var(--slmm-text-secondary);
    font-size: 0.8125rem;
    font-weight: 500;
}

.slmm-selected-model-value {
    color: var(--slmm-text-primary);
    font-size: 0.8125rem;
    font-weight: 600;
    background: #3a3a40;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--slmm-dark-border);
}

/* Feature Groups Styling */
.slmm-feature-group {
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: var(--slmm-shadow);
}

.slmm-feature-group-title {
    background: var(--slmm-dark-border);
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0;
    padding: 0.875rem 1.25rem;
    border-bottom: 1px solid var(--slmm-dark-border-subtle);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.slmm-feature-group-content {
    padding: 1rem;
    font-size: 0.875rem;
}

.slmm-checkbox-field {
    background: var(--slmm-dark-bg);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.slmm-checkbox-field:hover {
    border-color: var(--slmm-dark-border);
    background: var(--slmm-dark-surface-hover);
}

.slmm-checkbox-field .slmm-field-label {
    color: var(--slmm-text-primary) !important;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.slmm-visibility-label {
    color: var(--slmm-text-primary) !important;
    font-weight: 600 !important;
}

/* Checkbox styling improvements - purple checkboxes for all */
.slmm-checkbox-field input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 4px;
    margin-right: 0.5rem;
    vertical-align: middle;
    transition: all 0.3s ease;
    position: relative;
    background: transparent;
    cursor: pointer;
    margin-top: 0.1rem;
}

.slmm-checkbox-field input[type="checkbox"]:checked {
    background: var(--slmm-primary);
    border-color: var(--slmm-primary);
}

.slmm-checkbox-field input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 11px;
    font-weight: bold;
}

.slmm-checkbox-field label {
    color: var(--slmm-text-primary) !important;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    line-height: 1.3;
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0;
}

.slmm-checkbox-field .description {
    margin-top: 0.25rem;
    margin-left: 0;
    font-size: 0.8rem;
    color: var(--slmm-text-secondary) !important;
    line-height: 1.3;
}

/* Tighter spacing for checkbox groups */
.slmm-feature-group-content .slmm-form-field:last-child {
    margin-bottom: 0;
}

/* Post type checkboxes styling - make all checkboxes purple */
.slmm-feature-group-content input[type="checkbox"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 4px;
    margin-right: 0.5rem;
    vertical-align: middle;
    transition: all 0.3s ease;
    position: relative;
    background: var(--slmm-text-muted);
    cursor: pointer;
}

.slmm-feature-group-content input[type="checkbox"]:checked {
    background: var(--slmm-primary);
    border-color: var(--slmm-primary);
}

.slmm-feature-group-content input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 11px;
    font-weight: bold;
}

.slmm-feature-group-content label {
    color: var(--slmm-text-primary) !important;
    display: inline-flex;
    align-items: center;
    margin-right: 0.75rem;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.95rem;
    font-weight: 500;
}

/* Rate limit input styling */
.slmm-feature-group-content input[type="number"] {
    background: #3a3a40;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 6px;
    color: #ffffff;
    padding: 0.5rem;
    width: 80px;
    font-size: 0.875rem;
}

.slmm-feature-group-content input[type="number"]:focus {
    outline: none;
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

/* Admin usernames section */
.authorized-admin-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.authorized-admin-row input[type="text"] {
    background: #3a3a40;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 6px;
    color: #ffffff;
    padding: 0.5rem;
    flex: 1;
    max-width: 200px;
    font-size: 0.875rem;
}

.authorized-admin-row input[type="text"]:focus {
    outline: none;
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

.authorized-admin-row .remove-admin-button {
    background: var(--slmm-error);
    border: none;
    border-radius: 4px;
    color: white;
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.authorized-admin-row .remove-admin-button:hover {
    background: #dc2626;
}

#add-admin-button {
    background: var(--slmm-primary);
    border: none;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

#add-admin-button:hover {
    background: var(--slmm-primary-hover);
}

/* Emergency URL section styling */
#emergency-url {
    background: #3a3a40 !important;
    border: 2px solid var(--slmm-dark-border) !important;
    color: #ffffff !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    font-family: monospace;
    font-size: 0.8125rem;
    cursor: pointer;
    user-select: all;
    display: inline-block;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
}

#emergency-url:hover {
    border-color: var(--slmm-primary) !important;
    background: var(--slmm-primary-light) !important;
}

/* Warning box text styling fixes - lighter background with dashed orange border */
.slmm-feature-group-content div[style*="background: #fff3cd"] {
    background: var(--slmm-dark-surface) !important;
    border: 4px dashed var(--slmm-warning) !important;
    border-radius: 8px !important;
    padding: 1rem !important;
    margin: 1rem 0 !important;
}

.slmm-feature-group-content div[style*="background: #fff3cd"] strong,
.slmm-feature-group-content div[style*="background: #fff3cd"] p {
    color: var(--slmm-text-primary) !important;
    margin: 0.5rem 0 !important;
    line-height: 1.5 !important;
    font-size: 1rem;
}

.slmm-feature-group-content div[style*="background: #fff3cd"] code {
    background: var(--slmm-dark-bg) !important;
    color: var(--slmm-warning) !important;
    padding: 0.25rem 0.5rem !important;
    border-radius: 4px !important;
    font-family: monospace !important;
    border: 1px solid var(--slmm-dark-border) !important;
}

/* Copy feedback styling */
#copy-feedback {
    color: var(--slmm-success) !important;
    margin-left: 10px !important;
    opacity: 0 !important;
    transition: opacity 0.3s !important;
}

/* Override WordPress admin styles */
.slmm-settings-wrap .form-table,
.slmm-settings-wrap .form-table th,
.slmm-settings-wrap .form-table td {
    background: transparent !important;
    color: var(--slmm-text-primary) !important;
    border: none !important;
}

/* Override WordPress input styles with dark theme */
.slmm-settings-wrap input[type="text"],
.slmm-settings-wrap input[type="password"],
.slmm-settings-wrap input[type="color"],
.slmm-settings-wrap input[type="date"],
.slmm-settings-wrap input[type="datetime"],
.slmm-settings-wrap input[type="datetime-local"],
.slmm-settings-wrap input[type="email"],
.slmm-settings-wrap input[type="month"],
.slmm-settings-wrap input[type="number"],
.slmm-settings-wrap input[type="search"],
.slmm-settings-wrap input[type="tel"],
.slmm-settings-wrap input[type="time"],
.slmm-settings-wrap input[type="url"],
.slmm-settings-wrap input[type="week"],
.slmm-settings-wrap select,
.slmm-settings-wrap textarea {
    background: var(--slmm-dark-border-subtle) !important;
    color: #ffffff !important;
    border: 2px solid var(--slmm-dark-border) !important;
    border-radius: 8px !important;
}

/* Focus states with purple theme color */
.slmm-settings-wrap input[type="text"]:focus,
.slmm-settings-wrap input[type="password"]:focus,
.slmm-settings-wrap input[type="color"]:focus,
.slmm-settings-wrap input[type="date"]:focus,
.slmm-settings-wrap input[type="datetime"]:focus,
.slmm-settings-wrap input[type="datetime-local"]:focus,
.slmm-settings-wrap input[type="email"]:focus,
.slmm-settings-wrap input[type="month"]:focus,
.slmm-settings-wrap input[type="number"]:focus,
.slmm-settings-wrap input[type="search"]:focus,
.slmm-settings-wrap input[type="tel"]:focus,
.slmm-settings-wrap input[type="time"]:focus,
.slmm-settings-wrap input[type="url"]:focus,
.slmm-settings-wrap input[type="week"]:focus,
.slmm-settings-wrap select:focus,
.slmm-settings-wrap textarea:focus {
    outline: none !important;
    border-color: var(--slmm-primary) !important;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
}

.slmm-settings-wrap .notice {
    background: var(--slmm-dark-surface) !important;
    color: var(--slmm-text-primary) !important;
    border-left-color: var(--slmm-primary) !important;
    border-radius: 8px !important;
}

.slmm-settings-wrap .notice.notice-success {
    border-left-color: var(--slmm-success) !important;
}

.slmm-settings-wrap .notice.notice-error {
    border-left-color: var(--slmm-error) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slmm-settings-container {
        padding: 1rem;
    }
    
    .slmm-settings-header {
        padding: 1.5rem 1rem 1rem;
    }
    
    .slmm-settings-title {
        font-size: 1.75rem;
    }
    
    .slmm-tab-pane {
        padding: 1.5rem 1rem;
    }
    
    .slmm-tabs-nav {
        flex-wrap: wrap;
    }
    
    .slmm-tab-button {
        flex: 1;
        min-width: 120px;
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
    }
}

/* Animation for form submission */
.slmm-settings-form.saving {
    pointer-events: none;
    opacity: 0.7;
}

.slmm-save-button.saving {
    position: relative;
    color: transparent !important;
}

.slmm-save-button.saving::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Additional features styles */

/* Form validation states */
.slmm-input.valid {
    border-color: var(--slmm-success) !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2) !important;
}

.slmm-input.invalid {
    border-color: var(--slmm-error) !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}

/* Focused form fields */
.slmm-form-field.focused {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
}

/* Checked toggles */
.slmm-toggle-wrapper.checked {
    background: var(--slmm-primary-light);
    border-radius: 8px;
    padding: 0.5rem;
    margin: -0.5rem;
}

/* File upload states */
.slmm-file-label.file-selected {
    border-color: var(--slmm-success);
    background: rgba(16, 185, 129, 0.1);
    color: var(--slmm-success);
}

.slmm-file-label.drag-over {
    border-color: var(--slmm-primary);
    background: var(--slmm-primary-light);
    transform: scale(1.02);
}

/* Unsaved changes indicator */
.slmm-unsaved-indicator {
    color: var(--slmm-warning);
    font-size: 0.875rem;
    margin-left: 1rem;
    animation: pulse 2s ease-in-out infinite;
}

/* Tooltips */
.slmm-tooltip {
    position: absolute;
    background: var(--slmm-dark-surface);
    color: var(--slmm-text-primary);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    box-shadow: var(--slmm-shadow-lg);
    z-index: 10000;
    max-width: 300px;
    border: 1px solid var(--slmm-dark-border);
    display: none;
}

.slmm-tooltip::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid var(--slmm-dark-surface);
}

/* Notifications */
.slmm-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    box-shadow: var(--slmm-shadow-lg);
    z-index: 10001;
    display: none;
    max-width: 400px;
}

.slmm-notification-success {
    background: var(--slmm-success);
}

.slmm-notification-error {
    background: var(--slmm-error);
}

.slmm-notification-warning {
    background: var(--slmm-warning);
}

/* Progress bars */
.slmm-progress-bar {
    height: 4px;
    background: var(--slmm-primary);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
}

.slmm-progress-container {
    background: var(--slmm-dark-border);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.5rem;
}

/* Enhanced button states */
.slmm-save-button:active {
    transform: translateY(-1px) !important;
}

.slmm-export-button:active,
.slmm-import-button:active {
    transform: translateY(0) !important;
}

/* Search input */
.slmm-search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--slmm-dark-bg);
    border: 2px solid var(--slmm-dark-border);
    border-radius: 8px;
    color: var(--slmm-text-primary);
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

.slmm-search-input:focus {
    outline: none;
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2);
}

.slmm-search-input::placeholder {
    color: var(--slmm-text-muted);
}

/* Modal styles */
.slmm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10002;
    display: none;
    align-items: center;
    justify-content: center;
}

.slmm-modal-content {
    background: var(--slmm-dark-surface);
    border-radius: 16px;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--slmm-dark-border);
    box-shadow: var(--slmm-shadow-lg);
}

/* Loading states for individual sections */
.slmm-section-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.slmm-section-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top: 2px solid var(--slmm-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced scrollbar for webkit browsers */
.slmm-tabs-content::-webkit-scrollbar {
    width: 8px;
}

.slmm-tabs-content::-webkit-scrollbar-track {
    background: var(--slmm-dark-bg);
    border-radius: 4px;
}

.slmm-tabs-content::-webkit-scrollbar-thumb {
    background: var(--slmm-dark-border);
    border-radius: 4px;
}

.slmm-tabs-content::-webkit-scrollbar-thumb:hover {
    background: var(--slmm-primary);
}

/* Accessibility improvements */
.slmm-tab-button:focus-visible {
    outline: 2px solid var(--slmm-primary);
    outline-offset: 2px;
}

.slmm-input:focus-visible,
.slmm-textarea:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.4);
}

/* Print styles */
@media print {
    .slmm-settings-wrap {
        background: white !important;
        color: black !important;
    }
    
    .slmm-tabs-nav {
        display: none;
    }
    
    .slmm-tab-pane {
        display: block !important;
        page-break-inside: avoid;
    }
    
    .slmm-form-actions {
        display: none;
    }
}

/* Search & Replace Tab Icon */
.slmm-tab-button[data-tab="search-replace"] .slmm-tab-icon {
    background-image: url('data:image/svg+xml;utf8,<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 17v-2h14v2H5zm0-4v-2h14v2H5zm0-4V7h14v2H5z" fill="%23475569"/><path d="M8 12l-3 3 3 3v-2h6v-2H8v-2zm8-4V6H10v2h6V6l3 3-3 3z" fill="%23475569"/></svg>');
}

.slmm-tab-button:hover[data-tab="search-replace"] .slmm-tab-icon,
.slmm-tab-button.active[data-tab="search-replace"] .slmm-tab-icon {
    filter: brightness(1.4);
}

.slmm-tab-button.active[data-tab="search-replace"] .slmm-tab-icon {
    filter: brightness(1.8);
}

/* Search & Replace Component Styles */
.slmm-search-replace-content {
    display: grid;
    gap: 2rem;
}

.slmm-search-replace-form {
    background: var(--slmm-dark-surface);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border-subtle);
}

.slmm-search-replace-info {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--slmm-primary-subtle);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 6px;
}

.slmm-search-replace-info h3 {
    margin: 0 0 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--slmm-text-primary);
}

.slmm-search-replace-info p {
    margin: 0.5rem 0;
    color: var(--slmm-text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

.slmm-search-replace-info strong {
    color: var(--slmm-warning);
    font-weight: 600;
}

.slmm-search-replace-form-container {
    display: grid;
    gap: 1.5rem;
}

.slmm-form-row {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .slmm-form-row:first-child {
        grid-template-columns: 1fr 1fr;
    }
}

.slmm-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.slmm-form-group label {
    font-weight: 500;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-form-input {
    padding: 0.75rem;
    background: var(--slmm-dark-bg);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 6px;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.slmm-form-input:focus {
    outline: none;
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 3px var(--slmm-primary-light);
}

.slmm-form-input::placeholder {
    color: var(--slmm-text-muted);
}

.slmm-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin: 0;
}

.slmm-checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
    user-select: none;
}

.slmm-checkbox-label input[type="checkbox"] {
    display: none;
}

.slmm-checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--slmm-dark-border);
    border-radius: 4px;
    background: var(--slmm-dark-bg);
    transition: all 0.2s ease;
    position: relative;
}

.slmm-checkbox-label input[type="checkbox"]:checked + .slmm-checkmark {
    background: var(--slmm-primary);
    border-color: var(--slmm-primary);
}

.slmm-checkbox-label input[type="checkbox"]:checked + .slmm-checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Enhanced Search & Replace Form Styling */
.slmm-search-replace-form .slmm-checkbox-group {
    display: flex !important;
    flex-direction: column;
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: var(--slmm-dark-bg);
    border: 1px solid var(--slmm-dark-border-subtle);
    border-radius: 6px;
}

/* Force checkmark visibility ONLY for search options */
.slmm-search-replace-form .slmm-checkbox-group .slmm-checkmark {
    display: block !important;
    visibility: visible !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid var(--slmm-dark-border) !important;
    border-radius: 4px !important;
    background: var(--slmm-dark-bg) !important;
    transition: all 0.2s ease;
    position: relative;
    flex-shrink: 0;
}

/* Ensure table checkboxes don't get affected - keep them hidden */
.slmm-table-checkbox .slmm-checkmark {
    display: none !important;
}

.slmm-search-replace-form .slmm-checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    user-select: none;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.slmm-search-replace-form .slmm-checkbox-label:hover {
    background: var(--slmm-dark-surface);
}


.slmm-search-replace-form .slmm-checkbox-label input[type="checkbox"]:checked + .slmm-checkmark {
    background: var(--slmm-primary) !important;
    border-color: var(--slmm-primary) !important;
}

.slmm-search-replace-form .slmm-checkbox-label input[type="checkbox"]:checked + .slmm-checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Search & Replace Form Row Styling */
.slmm-search-replace-form .slmm-form-row {
    margin-bottom: 1.5rem;
}

.slmm-search-replace-form .slmm-form-row:last-child {
    margin-bottom: 0;
}

/* Search & Replace Options Section */
.slmm-search-replace-form .slmm-form-row:nth-child(2) {
    border-top: 1px solid var(--slmm-dark-border-subtle);
    padding-top: 1rem;
}

.slmm-search-replace-form .slmm-form-row:nth-child(2)::before {
    content: 'Search Options';
    display: block;
    font-weight: 600;
    color: var(--slmm-text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

/* Specific targeting for ONLY search options checkboxes */
.slmm-checkbox-group #case_insensitive + .slmm-checkmark,
.slmm-checkbox-group #dry_run + .slmm-checkmark,
.slmm-checkbox-group #whole_words + .slmm-checkmark {
    display: block !important;
    visibility: visible !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #666 !important;
    border-radius: 4px !important;
    background: #2c2c2c !important;
    position: relative;
    flex-shrink: 0;
}

.slmm-checkbox-group #case_insensitive:checked + .slmm-checkmark,
.slmm-checkbox-group #dry_run:checked + .slmm-checkmark,
.slmm-checkbox-group #whole_words:checked + .slmm-checkmark {
    background: #7a39e8 !important;
    border-color: #7a39e8 !important;
}

.slmm-checkbox-group #case_insensitive:checked + .slmm-checkmark::after,
.slmm-checkbox-group #dry_run:checked + .slmm-checkmark::after,
.slmm-checkbox-group #whole_words:checked + .slmm-checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.slmm-table-selection {
    margin-top: 1rem;
}

.slmm-table-selection .button {
    margin-right: 0.5rem;
    margin-bottom: 1rem;
    background: var(--slmm-dark-surface) !important;
    border: 1px solid var(--slmm-dark-border) !important;
    color: var(--slmm-text-primary) !important;
    text-shadow: none !important;
    font-size: 0.875rem !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

.slmm-table-selection .button:hover {
    background: var(--slmm-dark-surface-hover) !important;
    border-color: var(--slmm-primary) !important;
}

/* Special styling for deselect button */
.slmm-table-selection #deselect-all-tables {
    border-color: var(--slmm-warning) !important;
}

.slmm-table-selection #deselect-all-tables:hover {
    background: rgba(239, 68, 68, 0.1) !important;
    border-color: #ef4444 !important;
    color: #ef4444 !important;
}

.slmm-table-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--slmm-dark-border);
    border-radius: 6px;
    background: var(--slmm-dark-bg);
    padding: 0.5rem;
}

.slmm-table-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.slmm-table-checkbox:hover {
    background: var(--slmm-dark-surface);
}

.slmm-table-checkbox.slmm-core-table {
    background: var(--slmm-primary-subtle);
}

.slmm-table-checkbox.slmm-core-table:hover {
    background: var(--slmm-primary-light);
}

.slmm-table-name {
    flex: 1;
    font-weight: 500;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-table-rows {
    color: var(--slmm-text-muted);
    font-size: 0.75rem;
}

.slmm-search-replace-btn {
    background: var(--slmm-primary) !important;
    border: none !important;
    color: white !important;
    padding: 0.875rem 1.5rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-shadow: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.slmm-search-replace-btn:hover:not(:disabled) {
    background: var(--slmm-primary-hover) !important;
}

.slmm-search-replace-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
}

.slmm-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.slmm-loading {
    text-align: center;
    padding: 2rem;
    color: var(--slmm-text-secondary);
    font-style: italic;
}

.slmm-error {
    background: var(--slmm-error);
    color: white;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    font-weight: 500;
}

/* Search & Replace Results */
.slmm-search-replace-results-container {
    margin-top: 2rem;
}

.slmm-search-replace-results {
    background: var(--slmm-dark-surface);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border-subtle);
}

.slmm-search-replace-results h3 {
    margin: 0 0 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--slmm-text-primary);
}

.slmm-dry-run-notice {
    background: var(--slmm-primary-light);
    border: 1px solid var(--slmm-primary);
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-results-summary {
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--slmm-dark-bg);
    border-radius: 6px;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-no-results {
    text-align: center;
    padding: 2rem;
    color: var(--slmm-text-secondary);
    font-style: italic;
}

.slmm-results-table {
    overflow-x: auto;
}

.slmm-results-table-inner {
    width: 100%;
    border-collapse: collapse;
    background: var(--slmm-dark-bg);
    border-radius: 6px;
    overflow: hidden;
}

.slmm-results-table-inner th,
.slmm-results-table-inner td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--slmm-dark-border);
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-results-table-inner th {
    background: var(--slmm-dark-surface);
    font-weight: 600;
    color: var(--slmm-text-secondary);
}

.slmm-results-table-inner tr:last-child td {
    border-bottom: none;
}

.slmm-results-table-inner tr:nth-child(even) {
    background: var(--slmm-dark-surface-hover);
}

/* Notifications */
.slmm-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 999999;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    box-shadow: var(--slmm-shadow-lg);
    max-width: 400px;
}

.slmm-notification-success {
    background: var(--slmm-success);
    color: white;
}

.slmm-notification-error {
    background: var(--slmm-error);
    color: white;
}

.slmm-notification-warning {
    background: var(--slmm-warning);
    color: white;
}

/* Enhanced Search & Replace Results Styling */
.slmm-search-replace-results {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--slmm-dark-surface);
    border-radius: 12px;
    border: 1px solid var(--slmm-dark-border);
    box-shadow: var(--slmm-shadow);
}

.slmm-search-replace-results h3 {
    color: var(--slmm-text-primary);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.slmm-search-replace-results h4 {
    color: var(--slmm-text-primary);
    margin: 1.5rem 0 0.75rem 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.slmm-dry-run-notice {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.slmm-results-summary {
    margin-bottom: 1.5rem;
}

.slmm-summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.slmm-stat {
    background: var(--slmm-dark-border-subtle);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-stat strong {
    color: var(--slmm-primary);
}

.slmm-results-table-inner {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.5rem;
}

.slmm-results-table-inner th,
.slmm-results-table-inner td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--slmm-dark-border);
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-results-table-inner th {
    background: var(--slmm-dark-border-subtle);
    font-weight: 600;
    color: var(--slmm-primary);
}

.slmm-results-table-inner tr:hover {
    background: var(--slmm-dark-border-subtle);
}

.slmm-affected-posts {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--slmm-dark-border-subtle);
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
}

.slmm-posts-list {
    display: grid;
    gap: 0.75rem;
}

.slmm-post-item {
    background: var(--slmm-dark-surface);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-post-item strong {
    color: var(--slmm-primary);
}

.slmm-post-title {
    color: var(--slmm-text-primary);
    font-weight: 500;
    margin-left: 0.5rem;
}

.slmm-post-meta {
    color: var(--slmm-text-secondary);
    margin-left: 0.5rem;
    font-size: 0.8rem;
}

.slmm-option-preview {
    color: var(--slmm-text-secondary);
    font-family: monospace;
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.slmm-column-stats {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--slmm-dark-border-subtle);
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
}

.slmm-column-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.slmm-column-stat {
    background: var(--slmm-dark-surface);
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid var(--slmm-dark-border);
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-column-stat strong {
    color: var(--slmm-primary);
}

.slmm-change-details {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--slmm-dark-border-subtle);
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
}

.slmm-toggle-details {
    background: var(--slmm-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.slmm-toggle-details:hover {
    background: var(--slmm-primary-hover);
    transform: translateY(-1px);
}

.slmm-change-item {
    background: var(--slmm-dark-surface);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
    margin-bottom: 1rem;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-change-item strong {
    color: var(--slmm-primary);
}

.slmm-change-title {
    color: var(--slmm-text-primary);
    font-weight: 500;
}

.slmm-change-preview {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: var(--slmm-dark-border-subtle);
    border-radius: 6px;
    border: 1px solid var(--slmm-dark-border);
    font-family: monospace;
    font-size: 0.8rem;
    color: var(--slmm-text-secondary);
    line-height: 1.4;
}

.slmm-change-preview strong {
    color: var(--slmm-primary);
}

.slmm-no-results {
    text-align: center;
    padding: 2rem;
    color: var(--slmm-text-secondary);
    font-size: 0.9rem;
    font-style: italic;
}

/* Enhanced Affected Posts Table */
.slmm-posts-table-wrapper {
    margin-top: 1rem;
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
    overflow: hidden;
}

.slmm-posts-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--slmm-dark-surface);
}

.slmm-posts-table th,
.slmm-posts-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--slmm-dark-border);
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-posts-table th {
    background: var(--slmm-dark-border-subtle);
    font-weight: 600;
    color: var(--slmm-primary);
}

.slmm-posts-table tbody tr:hover {
    background: var(--slmm-dark-border-subtle);
}

.slmm-posts-table tbody tr.hover {
    background: var(--slmm-dark-border-subtle);
}

.slmm-posts-table .slmm-post-title {
    font-weight: 500;
    color: var(--slmm-text-primary);
}

.slmm-posts-table .slmm-post-type {
    color: var(--slmm-text-secondary);
    text-transform: capitalize;
}

.slmm-posts-table .slmm-post-actions {
    white-space: nowrap;
}

/* Status badges for posts table */
.slmm-status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.slmm-status-badge.slmm-status-publish {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.slmm-status-badge.slmm-status-draft {
    background: rgba(251, 191, 36, 0.1);
    color: #f59e0b;
}

.slmm-status-badge.slmm-status-private {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.slmm-status-badge.slmm-status-pending {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.slmm-status-badge.slmm-status-future {
    background: rgba(168, 85, 247, 0.1);
    color: #a855f7;
}

/* Action links (reusing Lorem Ipsum Detector styles) */
.slmm-action-link {
    display: inline-block;
    padding: 4px 8px;
    margin: 0 2px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.slmm-edit-link {
    background: var(--slmm-primary);
    color: #ffffff;
}

.slmm-edit-link:hover {
    background: var(--slmm-primary-hover);
    color: #ffffff;
}

.slmm-view-link {
    background: var(--slmm-success);
    color: #ffffff;
}

.slmm-view-link:hover {
    background: rgba(16, 185, 129, 0.8);
    color: #ffffff;
}

/* Options list styling */
.slmm-options-list {
    margin-top: 1rem;
}

.slmm-option-item {
    background: var(--slmm-dark-surface);
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid var(--slmm-dark-border);
    margin-bottom: 0.5rem;
    color: var(--slmm-text-primary);
    font-size: 0.875rem;
}

.slmm-option-item:last-child {
    margin-bottom: 0;
}

.slmm-option-item strong {
    color: var(--slmm-primary);
}

/* Search and Replace Table Selection Styles */
#table-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--slmm-dark-border);
    border-radius: 8px;
    background: var(--slmm-dark-surface);
}

.slmm-table-checkbox {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--slmm-dark-border-subtle);
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0;
    gap: 0.75rem;
    min-height: 60px;
}

.slmm-table-checkbox:last-child {
    border-bottom: none;
}

.slmm-table-checkbox:hover {
    background: var(--slmm-dark-surface-hover);
}

.slmm-table-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    flex-shrink: 0;
    appearance: none;
    background: var(--slmm-dark-bg);
    border: 2px solid var(--slmm-dark-border);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.slmm-table-checkbox input[type="checkbox"]:checked {
    background: var(--slmm-primary);
    border-color: var(--slmm-primary);
}

.slmm-table-checkbox input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.slmm-table-checkbox input[type="checkbox"]:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.3);
}

.slmm-table-name {
    color: var(--slmm-text-primary);
    font-weight: 500;
    flex-grow: 1;
    font-size: 0.95rem;
}

.slmm-table-rows {
    color: var(--slmm-text-muted);
    font-size: 0.85rem;
    flex-shrink: 0;
}

/* Selected table styling with purple background */
.slmm-table-checkbox.selected {
    background: rgba(124, 58, 237, 0.15);
    border-left: 4px solid var(--slmm-primary);
    padding-left: calc(1.25rem - 4px);
}

.slmm-table-checkbox.selected:hover {
    background: rgba(124, 58, 237, 0.2);
}

.slmm-table-checkbox.selected .slmm-table-name {
    color: var(--slmm-primary);
    font-weight: 600;
}

.slmm-table-checkbox.selected .slmm-table-rows {
    color: var(--slmm-primary-hover);
}

/* Core table indicators */
.slmm-table-checkbox.slmm-core-table .slmm-table-name::after {
    content: ' (Core)';
    color: var(--slmm-warning);
    font-size: 0.8rem;
    font-weight: 500;
}

.slmm-table-checkbox.slmm-core-table.selected .slmm-table-name::after {
    color: #fbbf24;
}

/* Smooth animations for table reordering */
.slmm-table-checkbox {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Checkmark animation */
.slmm-checkmark {
    display: none;
}

/* Loading state for table list */
#table-list .slmm-loading {
    padding: 2rem;
    text-align: center;
    color: var(--slmm-text-secondary);
    font-style: italic;
}

/* Error state for table list */
#table-list .slmm-error {
    padding: 1.5rem;
    text-align: center;
    color: var(--slmm-error);
    background: rgba(239, 68, 68, 0.1);
    border-radius: 6px;
    margin: 1rem;
}

/* Responsive adjustments for table selection */
@media (max-width: 768px) {
    .slmm-table-checkbox {
        padding: 0.875rem 1rem;
        min-height: 50px;
        gap: 0.5rem;
    }
    
    .slmm-table-checkbox input[type="checkbox"] {
        width: 16px;
        height: 16px;
    }
    
    .slmm-table-name {
        font-size: 0.9rem;
    }
    
    .slmm-table-rows {
        font-size: 0.8rem;
    }
} 