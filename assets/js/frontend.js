var queue = [];

(function ($) {
    'use strict';

    const pollInterval = setInterval(() => {

        let ids = queue.map(item => item.id);

        if (ids.length < 1) return;

        $.ajax({
                   url   : xagio_data.wp_post,
                   method: 'POST',
                   data  : {
                       action: 'xagio_get_ai_frontend_output',
                       ids   : ids.join(','),
                   },
               })
         .done(function (response) {
             // response should be an array of { id, status, data }

             response.data.forEach(task => {
                 const {
                           id,
                           input,
                           output
                       } = task;

                 // find the queued object
                 const qItem = queue.find(q => q.id === id);
                 if (!qItem) return;

                 // use its retarget_id to locate the element again
                 const $el = $('.' + qItem.retarget_id);

                 // if the parent is our wrapper, unwrap it
                 const $parent = $el.parent();
                 if ($parent.is('span.xag_ai_loading')) {
                     $el.unwrap();
                 } else {
                     // otherwise just remove the class from the element itself
                     $el.removeClass('xag_ai_loading');
                 }

                 // swap in new data
                 if (input === 'TEXT_CONTENT') {
                     $el.text(output);
                 } else {
                     $el.attr('src', output + '?_=' + Date.now() + '_' + qItem.retarget_id);
                     $el.removeAttr('srcset');
                 }

                 // remove this entry from the queue
                 queue = queue.filter(q => q.id !== id);

                 if (queue.length == 0) {
                     $('#send-to-ai-btn').removeAttr('disabled');
                     $('#ai-additional-prompt').val('');
                 }
             });

         })
         .fail(function (jqXHR, textStatus) {
             console.error('Polling error:', textStatus);
         });
    }, 2000);

    let prices = null;

    let inspectorEnabled = false;
    let lastHovered = null;
    let selectedElement = null;

    function enableInspector() {
        $('body')
            .on('keyup', '#ai-additional-prompt', function (e) {
                if (e.key === 'Enter') {
                    $('#send-to-ai-btn').trigger('click');
                    e.preventDefault();
                }
            })
            .on('change', '[name="ai-action-type"]', function () {
                if ($(this).val() == 'generate') {
                    $('.inspector-cost .inspector-cost-value').html(prices.IMAGE_GEN[0].price.toFixed(2));
                } else {
                    $('.inspector-cost .inspector-cost-value').html(prices.IMAGE_EDIT[0].price.toFixed(2));
                }
            })
            .on('mouseover.inspector', '*', function (e) {
                if ($(this).closest('#xagio-backups-modal, .dialog-widget, #inspector-toggle, #inspector-info, #wpadminbar, [data-elementor-type="header"], [data-elementor-type="footer"]').length) return;
                e.stopPropagation();
                if (lastHovered && lastHovered !== selectedElement) {
                    $(lastHovered).removeClass('inspector-highlight');
                }
                lastHovered = this;
                if (this !== selectedElement) {
                    $(this).addClass('inspector-highlight');
                }
            })
            .on('mouseout.inspector', '*', function (e) {
                if ($(this).closest('#xagio-backups-modal, .dialog-widget, #inspector-toggle, #inspector-info, #wpadminbar, [data-elementor-type="header"], [data-elementor-type="footer"]').length) return;
                e.stopPropagation();
                if (this !== selectedElement) {
                    $(this).removeClass('inspector-highlight');
                }
            })
            .on('click.inspector', '*', function (e) {
                if ($(this).closest('#xagio-backups-modal, .dialog-widget, #inspector-toggle, #inspector-info, #wpadminbar, [data-elementor-type="header"], [data-elementor-type="footer"]').length) return;
                e.preventDefault();
                e.stopPropagation();

                const isElementor = $('body').hasClass('elementor-page');
                const $el = $(this);

                // Restrict allowed elements on Elementor pages
                if (
                    isElementor &&
                    !$el.is('img') &&
                    !$el.parents().is('[data-element_type="widget"]') &&
                    !($el.is('[data-element_type="container"]') && hasBackgroundImage($el))
                ) {
                    return; // Do not proceed if not allowed element
                }

                if (selectedElement) {
                    $(selectedElement).removeClass('inspector-highlight');
                }

                selectedElement = this;
                $(selectedElement).addClass('inspector-highlight');

                // Determine if it should be treated as image
                let isImage = $el.is('img');
                let content;

                if (!isImage && $el.is('[data-element_type="container"]') && hasBackgroundImage($el)) {
                    isImage = true;
                    content = extractBackgroundImageUrl($el);
                } else {
                    content = isImage ? $el.attr('src') : $el.text().trim();
                }

                // Helper functions
                function hasBackgroundImage($element) {
                    const bg = $element.css('background-image');
                    return bg && bg !== 'none' && /^url\(["']?.+["']?\)$/.test(bg);
                }

                function extractBackgroundImageUrl($element) {
                    const bg = $element.css('background-image');
                    return bg.replace(/^url\(["']?(.+?)["']?\)$/, '$1');
                }

                function trimTo150(str) {
                    if (str.length <= 150) return str;
                    return str.slice(0, 150) + '...';
                }


                $('#inspector-info .inspector-body').html(`
  <b class="inspector-selected-element-type">Selected ${isImage ? 'Image' : 'Text'}</b><br>
  <div class="inspector-selected-element-content">
    ${isImage
      ? `<a href="${content}" target="_blank">${content}</a>`
      : trimTo150(content)}
  </div>
  
  <label class="xag-ai-additional-prompt">
    <div class="inspector-text-and-cost">
        <span>Additional Prompt</span>
        <span class="inspector-cost" title="Cost in xR / xB"><span class="inspector-cost-value">0.0000</span> xR</span>
    </div>
    <textarea id="ai-additional-prompt" class="widefat" rows="4"
      placeholder="Start typing your instructions..."></textarea>
      
    <div class="inspector-actions">
      <a href="#" id="open-backups-modal" title="View & restore Elementor backups"><i class="xagio-icon xagio-icon-refresh"></i> Backups</a>
    <button title="Start processing" id="send-to-ai-btn" class="button button-primary" disabled>
        <svg width="14" height="14" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.396447 7.89645C0.201184 8.09171 0.201184 8.40829 0.396447 8.60355C0.591709 8.79882 0.908291 8.79882 1.10355 8.60355L0.396447 7.89645ZM8.75 0.75C8.75 0.473858 8.52614 0.25 8.25 0.25L3.75 0.25C3.47386 0.25 3.25 0.473858 3.25 0.75C3.25 1.02614 3.47386 1.25 3.75 1.25L7.75 1.25L7.75 5.25C7.75 5.52614 7.97386 5.75 8.25 5.75C8.52614 5.75 8.75 5.52614 8.75 5.25L8.75 0.75ZM0.75 8.25L1.10355 8.60355L8.60355 1.10355L8.25 0.75L7.89645 0.396447L0.396447 7.89645L0.75 8.25Z" fill="white"/>
        </svg>
    </button>
</div>
  </label>
  
  <span id="ai-status"></span>
`);

                if (isImage) {
                    $('.inspector-cost .inspector-cost-value').html(prices.IMAGE_GEN[0].price.toFixed(2));
                } else {
                    $('.inspector-cost .inspector-cost-value').html(prices.TEXT_CONTENT[0].price.toFixed(3));
                }
                $('#inspector-info').show();

                const statusAction = isImage ? 'xagio_ai_check_status_image' : 'xagio_ai_check_status_text';
                const processAction = isImage ? 'xagio_ai_process_image' : 'xagio_ai_process_text';

                // First, get ID (only for images)
                if (isImage) {
                    $.post(xagio_data.wp_post, {
                        action   : 'xagio_ai_get_attachment_id',
                        image_url: content
                    }, function (res) {
                        if (res.status === 'success') {
                            const attachmentId = res.data.id;
                            checkStatusAndBindButton({
                                                         type  : 'image',
                                                         id    : attachmentId,
                                                         action: processAction
                                                     });
                        } else {
                            $('#ai-status').text('❌ Make sure image is in Media Library.');
                        }
                    });
                } else {
                    // for text, hash or base64 the content as ID if needed
                    checkStatusAndBindButton({
                                                 type  : 'text',
                                                 text  : content,
                                                 action: processAction
                                             });
                }

                function checkStatusAndBindButton({
                                                      type,
                                                      id = null,
                                                      text = null,
                                                      action
                                                  }) {
                    const statusAction = type ===
                                         'image' ? 'xagio_ai_check_status_image' : 'xagio_ai_check_status_text';
                    const pageType = $('body').hasClass('elementor-page') ? 'elementor' : 'default';
                    let elementorDataId = null;

                    if (type === 'text' && pageType === 'elementor') {
                        elementorDataId = $(selectedElement).closest('[data-id]').attr('data-id') || null;

                        if (!elementorDataId) {
                            $('#ai-status').text('⚠️ Cannot send: missing data-id on Elementor text element');
                            return;
                        }
                    }

                    const statusPayload = (type === 'image')
                                          ? {
                            attachment_id: id,
                            page_type    : pageType,
                            post_id      : xagio_post_id.value,
                        }
                                          : {
                            post_id  : xagio_post_id.value,
                            content  : text,
                            page_type: pageType,
                            data_id  : elementorDataId
                        };

                    $.post(xagio_data.wp_post, {
                        action: statusAction,
                        ...statusPayload
                    }, function (response) {
                        const btn = $('#send-to-ai-btn');
                        if (response.data === 'queued') {
                            btn.prop('disabled', true);
                            $('#ai-status').text('');
                        } else if (response.data === 'running') {
                            btn.prop('disabled', true);
                            $('#ai-status').text('');
                        } else {
                            btn.prop('disabled', false);
                            $('#ai-status').text('');
                        }

                        $(document).off('click', '#send-to-ai-btn').on('click', '#send-to-ai-btn', function () {

                            // Calculate if user has enough xags to process this
                            let total_xags = parseFloat($('.xrenew .value').text()) +
                                             parseFloat($('.xbanks .value').text());
                            let total_cost = parseFloat($('.inspector-cost-value').text());

                            if (total_cost > total_xags) {
                                alert('You do not have enough XAGs to process this action!');
                                return;
                            }

                            btn.prop('disabled', true);

                            const processPayload = (type === 'image')
                                                   ? {
                                    attachment_id: id,
                                    page_type    : pageType,
                                    post_id  : xagio_post_id.value
                                }
                                                   : {
                                    content  : text,
                                    post_id  : xagio_post_id.value,
                                    data_id  : elementorDataId,
                                    page_type: pageType
                                };

                            // grab the additional prompt
                            const additionalPrompt = $('#ai-additional-prompt').val().trim();
                            if (additionalPrompt) {
                                processPayload.additional_prompt = additionalPrompt;
                            }

                            // for images, include the action-type radio
                            if (type === 'image') {
                                processPayload.action_type = 'generate';
                            }

                            // generate a unique ID each time
                            const retarget_id = 'rt_'
                                                + Date.now().toString(36)    // base36 timestamp
                                                + '_'
                                                + Math.random().toString(36).substr(2, 8);  // 8 chars of randomness
                            $(selectedElement).addClass(retarget_id);

                            if ($(selectedElement).is('img')) {
                                // wrap the img in a span that gets the loading styles
                                $(selectedElement).wrap('<span class="xag_ai_loading"></span>');
                            } else {
                                // for text or other elements, just add the class
                                $(selectedElement).addClass('xag_ai_loading');
                            }


                            $.post(xagio_data.wp_post, {
                                action: action,
                                ...processPayload
                            }, function (res) {
                                if (res.status === 'success') {

                                    // Push to queue
                                    queue.push({
                                                   id         : res.data,
                                                   retarget_id: retarget_id
                                               });

                                } else {
                                    $('#ai-status').text('❌ Failed to send');
                                    btn.prop('disabled', false);
                                }
                            });
                        });
                    });
                }

            });

    }

    function disableInspector() {
        $('body').off('.inspector');
        if (lastHovered) $(lastHovered).removeClass('inspector-highlight');
        if (selectedElement) $(selectedElement).removeClass('inspector-highlight');
        $('#inspector-info').hide();
        lastHovered = null;
        selectedElement = null;
    }

    function refreshXags() {
        $.post(xagio_data.wp_post, 'action=xagio_refreshXags', function (d) {
            if (d.status == 'success') {

                $('.xrenew').find('.value').html(parseFloat(d.data.xags_allowance).toFixed(2));

                if (d.data['xags'] > 0) {
                    $('.xbanks').find('.value').html(parseFloat(d.data.xags).toFixed(2));
                }

            }
        });
    }

    function getCosts() {
        $.post(xagio_data.wp_post, `action=xagio_ai_get_average_prices`, function (d) {

            prices = d.data.average_prices;

        });
    }

    //
    // 4) Toggle & panel wiring (moved into #wpadminbar)
    //
    $(document).ready(function () {
        const pageType = $('body').hasClass('elementor-page') ? 'Elementor' : 'Default';
        if (pageType !== 'Elementor') return;

        refreshXags();
        getCosts();

        const logoUrl = xagio_data.plugins_url + 'assets/img/logo-xagio-smaller.webp';

        // Admin bar item
        const $toggleLink = $(
            '<li id="wp-admin-bar-xagio-inspector" class="menupop">' +
            `<a href="#" id="inspector-toggle" class="ab-item"><img src="${logoUrl}" alt="Xagio Logo" class="inspector-logo" /> Enable Xagio AI Assistant</a>` +
            '</li>'
        );
        $('#wp-admin-bar-root-default').append($toggleLink);

        // Panel + Backups button
        const $panel = $(`
    <div id="inspector-info">
      <div class="inspector-header">
        <span class="inspector-logo-container">
            <img src="${logoUrl}" alt="Xagio Logo" class="inspector-logo" />
            <span class="inspector-title">Xagio AI Assistant</span>
        </span>
        <span id="inspector-xags">
            <div class="xags-container">
                <div class="xags-item xrenew" id="xags-allowance" data-xagio-tooltip data-xagio-tooltip-position="bottom" data-xagio-title="These are your current XAGS (xRenew)">
                    <img src="${xagio_data.plugins_url}assets/img/logos/xRenew.png" alt="xR" class="xags-icon">
                    <span class="value">0</span>
                </div>
                <div class="xags-item xbanks" id="xags" data-xagio-tooltip data-xagio-tooltip-position="bottom" data-xagio-title="These are your current XAGS (xBank)">
                    <img src="${xagio_data.plugins_url}assets/img/logos/xBanks.png" alt="xB" class="xags-icon">
                    <span class="value">0</span>
                </div>
            </div>
        </span>       
      </div>
      <div class="inspector-body"></div>      
    </div>`);

        $('body').append($panel);

        // Toggle (unchanged)
        $('#inspector-toggle').on('click', function (e) {
            e.preventDefault();
            inspectorEnabled = !inspectorEnabled;
            $(this).html(
                inspectorEnabled
                ? `<img src="${logoUrl}" alt="Xagio Logo" class="inspector-logo" /> Disable Xagio AI Assistant`
                : `<img src="${logoUrl}" alt="Xagio Logo" class="inspector-logo" /> Enable Xagio AI Assistant`
            );
            inspectorEnabled ? enableInspector() : disableInspector();
        });

        // ===== Backups modal wiring =====
        $(document).on('click', '#open-backups-modal', function(e){
            e.preventDefault();
            openBackupsModal();
        });

        function openBackupsModal(){
            const $modal = $(`
<div id="xagio-backups-modal" role="dialog" aria-modal="true" aria-label="Elementor Backups">
  <div class="modal-inner">
    <div class="modal-head">
      <strong>Elementor Backups &nbsp; <a href="#" id="delete-all-backups">Delete All</a></strong>
      <button class="close" aria-label="Close">×</button>      
    </div>
    <div class="modal-body"><div class="loading">Loading…</div></div>
  </div>
</div>`);
            $('body').append($modal);
            $modal.fadeIn(120);
            bindModalEvents($modal);
            fetchBackups($modal);
        }

        function bindModalEvents($modal){
            $modal.on('click', '.close', () => closeBackupsModal($modal));
            $modal.on('click', e => { if (e.target.id === 'xagio-backups-modal') closeBackupsModal($modal); });
            $(document).on('keydown.xagioModal', e => { if (e.key === 'Escape') closeBackupsModal($modal); });

            // Restore action
            $modal.on('click', '.restore-backup', function(){
                const index = Number($(this).data('index'));
                if (!Number.isInteger(index)) return;
                if (!confirm('Restore this backup? This will overwrite current Elementor content.')) return;

                $.post(xagio_data.wp_post, {
                    action : 'xagio_restore_elementor_backup',
                    post_id: xagio_post_id.value,
                    index  : index
                }, function(res){
                    if (res.success === true) {
                        // Reload so Elementor/page picks up the restored JSON
                        location.reload();
                    } else {
                        alert(res.message || 'Restore failed.');
                    }
                });
            });

            // Delete action
            $modal.on('click', '.delete-backup', function(){
                const index = Number($(this).data('index'));
                if (!Number.isInteger(index)) return;
                if (!confirm('Delete this backup permanently?')) return;

                const $li = $(this).closest('li');

                $.post(xagio_data.wp_post, {
                    action : 'xagio_delete_elementor_backup',
                    post_id: xagio_post_id.value,
                    index  : index
                }, function(res){
                    if (res.success === true) {
                        $li.remove();
                        const $list = $modal.find('.modal-body .list');
                        if (!$list.find('li').length) {
                            $modal.find('.modal-body').html('<div class="empty">No backups yet.</div>');
                        }
                    } else {
                        alert(res.message || 'Delete failed.');
                    }
                });
            });

            // Delete all backups
            $modal.on('click', '#delete-all-backups', function(e){
                e.preventDefault();
                if (!confirm('Delete ALL backups for this page? This cannot be undone.')) return;
                $.post(xagio_data.wp_post, {
                    action : 'xagio_delete_all_elementor_backups',
                    post_id: xagio_post_id.value
                }, function(res){
                    if (res.success === true) {
                        $modal.find('.modal-body').html('<div class="empty">No backups yet.</div>');
                    } else {
                        alert(res.message || 'Delete-all failed.');
                    }
                });
            });

        }

        function closeBackupsModal($modal){
            $(document).off('keydown.xagioModal');
            $modal.fadeOut(120, () => $modal.remove());
        }

        function fetchBackups($modal){
            $.post(xagio_data.wp_post, {
                action : 'xagio_get_elementor_backups',
                post_id: xagio_post_id.value
            }, function(res){
                const $body = $modal.find('.modal-body');
                if (res.success !== true) {
                    $body.html('<div class="empty">Could not load backups.</div>');
                    return;
                }
                // Accept either a raw array or {backups: []}
                const list = Array.isArray(res.data) ? res.data : (res.data && res.data.backups) ? res.data.backups : [];
                if (!list.length) {
                    $body.html('<div class="empty">No backups yet.</div>');
                    return;
                }

                const items = list.map((b, i) => {
                    const sizeKB = b && b.data ? Math.ceil(b.data.length / 1024) : 0;
                    const by     = (b && (b.by ?? '')).toString();
                    const date   = (b && b.date) ? b.date : '';
                    const type   = (b && b.type) ? b.type : '';

                    return `
<li>
  <div class="meta">
    <div><strong>${escapeHtml(date)}</strong></div>
    <div class="sub">type: ${escapeHtml(type)} · user: ${escapeHtml(by)} · ~${sizeKB} KB</div>
  </div>
  <div class="actions">
    <button class="button button-primary restore-backup" data-index="${i}" type="button">Restore</button>
     <button class="button delete-backup" data-index="${i}" type="button">Delete</button>
  </div>
</li>`;
                }).reverse().join(''); // newest first visually

                $body.html(`<ul class="list">${items}</ul><div class="note" style="margin-top:8px;opacity:.8">Restoring will overwrite current <code>_elementor_data</code>.</div>`);
            });
        }

        function escapeHtml(s){
            return String(s ?? '').replace(/[&<>"']/g, (m)=>({ '&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#039;' }[m]));
        }
    });


})(jQuery);
