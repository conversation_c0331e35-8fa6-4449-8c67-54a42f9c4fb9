jQuery(document).ready(function($) {
    console.log('SLMM GPT Prompt Script loaded successfully.');

    // Add the passive listener for touchstart events globally
    document.addEventListener('touchstart', function() {}, { passive: true });

    // Dark Theme Notification system
    function showNotification(message, type = 'success') {
        const notificationId = 'slmm-prompt-notification';
        
        // Remove any existing notification
        $('#' + notificationId).remove();
        
        const colors = {
            'success': '#7C3AED',
            'error': '#7C3AED',
            'warning': '#7C3AED',
            'info': '#7C3AED'
        };
        
        const borderColors = {
            'success': '#7C3AED',
            'error': '#7C3AED',
            'warning': '#7C3AED',
            'info': '#7C3AED'
        };
        
        const textColors = {
            'success': '#7C3AED',
            'error': '#7C3AED',
            'warning': '#7C3AED',
            'info': '#7C3AED'
        };
        
        const notification = $(`
            <div id="${notificationId}" style="
                position: fixed !important;
                top: 50px !important;
                right: 20px !important;
                z-index: 999999 !important;
                background: ${colors[type]} !important;
                color: ${textColors[type]} !important;
                border: 1px solid ${borderColors[type]} !important;
                padding: 12px 20px !important;
                border-radius: 8px !important;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
                font-weight: 500 !important;
                max-width: 350px !important;
                opacity: 0 !important;
                transform: translateX(100%) !important;
                transition: all 0.3s ease !important;
                pointer-events: none !important;
            ">${message}</div>
        `);
        
        $('body').append(notification);
        
        // Animate in
        setTimeout(() => {
            notification.css({
                'opacity': '1',
                'transform': 'translateX(0)'
            });
        }, 10);
        
        // Auto fade out after 3 seconds
        setTimeout(() => {
            notification.css({
                'opacity': '0',
                'transform': 'translateX(100%)'
            });
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Handle the click event for executing GPT prompt - now using class selector and event delegation
    $(document).on('click', '.slmm-execute-gpt-prompt, #slmm-execute-gpt-prompt', function(e) {
        e.preventDefault();
        
        const button = this;
        const $button = $(button);
        
        // Check if slmmGptPromptData is available
        if (typeof slmmGptPromptData === 'undefined') {
            showNotification('GPT Prompt data not available. Please refresh the page.', 'error');
            console.error('slmmGptPromptData is undefined');
            return;
        }
        
        // Get the instance ID and find the corresponding dropdown
        const instanceId = $button.data('instance');
        let $dropdown;
        
        if (instanceId) {
            // Use instance-specific dropdown
            $dropdown = $('#slmm-gpt-prompt-dropdown-' + instanceId);
        } else {
            // Fallback: find dropdown in the same container or use old IDs
            $dropdown = $button.closest('.slmm-gpt-prompt-container').find('.slmm-gpt-prompt-dropdown');
            if (!$dropdown.length) {
                $dropdown = $('#slmm-gpt-prompt-dropdown, #gpt-prompt-select').first();
            }
        }
        
        const promptIndex = $dropdown.val();
        if (!promptIndex && promptIndex !== '0') {
            showNotification('Please select a GPT prompt', 'warning');
            return;
        }

        // Get the selected text from the editor
        var selectedText = '';
        if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden()) {
            selectedText = tinyMCE.activeEditor.selection.getContent({format: 'text'});
            // If no text is selected, get all content
            if (!selectedText) {
                selectedText = tinyMCE.activeEditor.getContent({format: 'text'});
            }
        } else {
            var textarea = $('#content');
            if (textarea.length > 0) {
                // Check if there's selected text
                var start = textarea[0].selectionStart;
                var end = textarea[0].selectionEnd;
                if (start !== end) {
                    selectedText = textarea.val().substring(start, end);
                } else {
                    selectedText = textarea.val();
                }
            }
        }

        if (!selectedText || selectedText.trim() === '') {
            showNotification('Please select some text in the editor or ensure the editor has content', 'warning');
            return;
        }

        // Validate prompt exists
        if (!slmmGptPromptData.prompts || !slmmGptPromptData.prompts[promptIndex]) {
            showNotification('Selected prompt not found', 'error');
            console.error('Prompt not found at index:', promptIndex);
            return;
        }

        console.log('Selected Text:', selectedText);
        console.log('Prompt Index:', promptIndex);
        console.log('Button Instance:', instanceId);
        console.log('Available Prompts:', slmmGptPromptData.prompts);

        // Show loading state
        var originalText = $button.text();
        $button.text('Processing...').prop('disabled', true);

        // Show processing notification
        showNotification('Generating content...', 'info');

        // Make an AJAX request to execute the GPT prompt
        $.ajax({
            url: slmmGptPromptData.ajax_url,
            method: 'POST',
            data: {
                action: 'slmm_execute_gpt_prompt',
                nonce: slmmGptPromptData.nonce,
                prompt_index: promptIndex,
                selected_text: selectedText
            },
            success: function(response) {
                console.log('API Response:', response);
                
                // Handle the successful response from API
                if (response.success) {
                    if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden()) {
                        tinyMCE.activeEditor.selection.setContent(response.data);
                    } else {
                        var textarea = $('#content');
                        if (textarea.length > 0) {
                            var currentContent = textarea.val();
                            var selectionStart = textarea[0].selectionStart;
                            var selectionEnd = textarea[0].selectionEnd;
                            textarea.val(
                                currentContent.substring(0, selectionStart) +
                                response.data +
                                currentContent.substring(selectionEnd)
                            );
                        }
                    }
                    showNotification('✓ Content generated successfully!', 'success');
                } else {
                    showNotification('Error: ' + (response.data || 'Unknown error occurred'), 'error');
                    console.error('API Error:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.log('Full XHR object:', xhr);
                console.log('Response Text:', xhr.responseText);
                showNotification('An error occurred while processing your request', 'error');
            },
            complete: function() {
                // Reset button state
                $button.text(originalText).prop('disabled', false);
            }
        });
    });
});
