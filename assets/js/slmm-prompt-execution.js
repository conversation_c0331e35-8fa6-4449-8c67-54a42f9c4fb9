jQuery(document).ready(function($) {
    console.log('SLMM Prompt Execution Script loaded successfully.');

    // Add the passive listener for touchstart events globally
    document.addEventListener('touchstart', function() {}, { passive: true });

    // Dark Theme Notification system
    function showNotification(message, type = 'success') {
        const notificationId = 'slmm-prompt-notification';
        
        // Remove any existing notification
        $('#' + notificationId).remove();
        
        const colors = {
            'success': '#1a202c',
            'error': '#1a1a1a',
            'warning': '#1a202c',
            'info': '#1a202c'
        };
        
        const borderColors = {
            'success': '#2d3748',
            'error': '#4a5568',
            'warning': '#4a5568',
            'info': '#4a5568'
        };
        
        const textColors = {
            'success': '#e2e8f0',
            'error': '#f7fafc',
            'warning': '#ffffff',
            'info': '#ffffff'
        };
        
        const notification = $(`
            <div id="${notificationId}" style="
                position: fixed !important;
                top: 50px !important;
                right: 20px !important;
                z-index: 999999 !important;
                background: ${colors[type]} !important;
                color: ${textColors[type]} !important;
                border: 1px solid ${borderColors[type]} !important;
                padding: 12px 20px !important;
                border-radius: 8px !important;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
                font-weight: 500 !important;
                max-width: 350px !important;
                opacity: 0 !important;
                transform: translateX(100%) !important;
                transition: all 0.3s ease !important;
                pointer-events: none !important;
            ">${message}</div>
        `);
        
        $('body').append(notification);
        
        // Animate in
        setTimeout(() => {
            notification.css({
                'opacity': '1',
                'transform': 'translateX(0)'
            });
        }, 10);
        
        // Auto fade out after 3 seconds
        setTimeout(() => {
            notification.css({
                'opacity': '0',
                'transform': 'translateX(100%)'
            });
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Function to find the target editor/textarea for a given button
    function findTargetEditor(button) {
        const $button = $(button);
        const container = $button.closest('.slmm-gpt-prompt-container');
        
        // Method 1: Look for textarea immediately after the button's container
        let $textarea = container.parent().find('textarea').first();
        
        // Method 2: If no textarea found, look in the same parent container
        if (!$textarea.length) {
            $textarea = $button.closest('div, td, .acf-field').find('textarea').first();
        }
        
        // Method 3: Look for common WordPress/ACF textarea patterns
        if (!$textarea.length) {
            const possibleSelectors = [
                'textarea[name*="content"]',
                'textarea.wp-editor-area', 
                'textarea[id*="content"]',
                'textarea[class*="acf"]',
                '.acf-field textarea',
                '#content'
            ];
            
            for (let selector of possibleSelectors) {
                $textarea = $button.closest('form, .wrap, .acf-field, td').find(selector).first();
                if ($textarea.length) break;
            }
        }
        
        // Method 4: Fallback to any textarea in the vicinity
        if (!$textarea.length) {
            $textarea = $button.closest('div').siblings().find('textarea').first();
        }
        
        return $textarea;
    }

    // Function to get selected text from various editor types
    function getSelectedText(button) {
        const $button = $(button);
        let selectedText = '';
        
        // Try TinyMCE first (for main WordPress editor)
        if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden()) {
            selectedText = tinyMCE.activeEditor.selection.getContent({format: 'text'});
            if (!selectedText) {
                selectedText = tinyMCE.activeEditor.getContent({format: 'text'});
            }
        }
        
        // If no TinyMCE content, try to find the associated textarea
        if (!selectedText) {
            const $textarea = findTargetEditor(button);
            
            if ($textarea.length > 0) {
                const textarea = $textarea[0];
                // Check if there's selected text
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                if (start !== end) {
                    selectedText = $textarea.val().substring(start, end);
                } else {
                    selectedText = $textarea.val();
                }
            }
        }
        
        // Fallback to main WordPress content area
        if (!selectedText) {
            const $mainContent = $('#content');
            if ($mainContent.length > 0) {
                const textarea = $mainContent[0];
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                if (start !== end) {
                    selectedText = $mainContent.val().substring(start, end);
                } else {
                    selectedText = $mainContent.val();
                }
            }
        }
        
        return selectedText;
    }

    // Function to insert content into the correct editor
    function insertContent(button, content) {
        const $button = $(button);
        let inserted = false;
        
        // Try TinyMCE first
        if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden()) {
            tinyMCE.activeEditor.selection.setContent(content);
            inserted = true;
        } else {
            // Find the associated textarea
            const $textarea = findTargetEditor(button);
            
            if ($textarea.length > 0) {
                const textarea = $textarea[0];
                const currentContent = $textarea.val();
                const selectionStart = textarea.selectionStart || 0;
                const selectionEnd = textarea.selectionEnd || 0;
                
                $textarea.val(
                    currentContent.substring(0, selectionStart) +
                    content +
                    currentContent.substring(selectionEnd)
                );
                
                // Trigger change event for ACF and other field types
                $textarea.trigger('input').trigger('change');
                inserted = true;
            }
        }
        
        return inserted;
    }

    // Handle both new class-based selectors and old IDs for backward compatibility
    function executePrompt(e) {
        if (e) e.preventDefault();
        
        const button = e.target;
        const $button = $(button);
        
        // Check if slmmGptPromptData is available
        if (typeof slmmGptPromptData === 'undefined') {
            showNotification('GPT Prompt data not available. Please refresh the page.', 'error');
            console.error('slmmGptPromptData is undefined');
            return;
        }
        
        // Get the instance ID and find the corresponding dropdown
        const instanceId = $button.data('instance');
        let $dropdown;
        
        if (instanceId) {
            // Use instance-specific dropdown
            $dropdown = $('#slmm-gpt-prompt-dropdown-' + instanceId);
        } else {
            // Fallback: find dropdown in the same container or use old IDs
            $dropdown = $button.closest('.slmm-gpt-prompt-container').find('.slmm-gpt-prompt-dropdown');
            if (!$dropdown.length) {
                $dropdown = $('#slmm-gpt-prompt-dropdown, #gpt-prompt-select').first();
            }
        }
        
        const promptIndex = $dropdown.val();
        if (!promptIndex && promptIndex !== '0') {
            showNotification('Please select a GPT prompt', 'warning');
            return;
        }

        // Get the selected text using smart detection
        const selectedText = getSelectedText(button);

        if (!selectedText || selectedText.trim() === '') {
            showNotification('Please select some text in the editor or ensure the editor has content', 'warning');
            return;
        }

        // Validate prompt exists
        if (!slmmGptPromptData.prompts || !slmmGptPromptData.prompts[promptIndex]) {
            showNotification('Selected prompt not found', 'error');
            console.error('Prompt not found at index:', promptIndex);
            return;
        }

        console.log('Selected Text:', selectedText);
        console.log('Prompt Index:', promptIndex);
        console.log('Button Instance:', instanceId);
        console.log('Available Prompts:', slmmGptPromptData.prompts);

        // Show loading state
        const originalText = $button.text();
        $button.text('Processing...').prop('disabled', true);

        // Show processing notification
        showNotification('Generating content...', 'info');

        // Make an AJAX request to execute the GPT prompt
        $.ajax({
            url: slmmGptPromptData.ajax_url,
            method: 'POST',
            data: {
                action: 'slmm_execute_gpt_prompt',
                nonce: slmmGptPromptData.nonce,
                prompt_index: promptIndex,
                selected_text: selectedText
            },
            success: function(response) {
                console.log('API Response:', response);
                
                // Handle the successful response from API
                if (response.success) {
                    const inserted = insertContent(button, response.data);
                    
                    if (inserted) {
                        showNotification('✓ Content generated successfully!', 'success');
                    } else {
                        showNotification('Content generated but could not insert into editor', 'warning');
                        console.log('Generated content:', response.data);
                    }
                } else {
                    showNotification('Error: ' + (response.data || 'Unknown error occurred'), 'error');
                    console.error('API Error:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.log('Full XHR object:', xhr);
                console.log('Response Text:', xhr.responseText);
                showNotification('An error occurred while processing your request', 'error');
            },
            complete: function() {
                // Reset button state
                $button.text(originalText).prop('disabled', false);
            }
        });
    }
    
    // Bind click events to both new class-based selectors and old IDs for backward compatibility
    $(document).on('click', '.slmm-execute-gpt-prompt, #slmm-execute-gpt-prompt, #execute-gpt-prompt', executePrompt);
});