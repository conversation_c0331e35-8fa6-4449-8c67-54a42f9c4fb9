/**
 * SLMM Settings JavaScript
 * Handles tabbed interface and other interactive features
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize settings page
    initializeSettingsPage();

    function initializeSettingsPage() {
        initializeTabs();
        initializeFormHandling();
        initializeFileUpload();
        initializeFormValidation();
        initializeTooltips();
        initializeAnimations();
        initializeKeyboardShortcuts();
        initializeColorPickers();
        initializeSearchReplace();
    }

    // Tab functionality
    function initializeTabs() {
        const $tabButtons = $('.slmm-tab-button');
        const $tabPanes = $('.slmm-tab-pane');
        
        // Handle tab clicks
        $tabButtons.on('click', function(e) {
            e.preventDefault();
            
            const targetTab = $(this).data('tab');
            
            // Remove active class from all tabs and panes
            $tabButtons.removeClass('active');
            $tabPanes.removeClass('active');
            
            // Add active class to clicked tab and corresponding pane
            $(this).addClass('active');
            $('#' + targetTab).addClass('active');
            
            // Hide/show Save Settings button based on active tab
            const $saveButtonContainer = $('.slmm-form-actions');
            if (targetTab === 'export-import' || targetTab === 'lorem-detector' || targetTab === 'search-replace') {
                $saveButtonContainer.hide();
            } else {
                $saveButtonContainer.show();
            }
            
            // Save current tab in localStorage
            localStorage.setItem('slmm_active_tab', targetTab);
            
            // Trigger tab change event
            $(document).trigger('slmm:tabChanged', [targetTab]);
        });
        
        // Restore last active tab from localStorage
        const savedTab = localStorage.getItem('slmm_active_tab');
        if (savedTab && $('#' + savedTab).length) {
            $tabButtons.filter('[data-tab="' + savedTab + '"]').trigger('click');
        }
        
        // Handle hash navigation
        if (window.location.hash) {
            const hashTab = window.location.hash.substring(1);
            if ($('#' + hashTab).length) {
                $tabButtons.filter('[data-tab="' + hashTab + '"]').trigger('click');
            }
        }
        
        // Ensure Save Settings button is hidden on page load if export-import, lorem-detector, or search-replace tab is active
        const $activeTab = $('.slmm-tab-button.active');
        if ($activeTab.length && ($activeTab.data('tab') === 'export-import' || $activeTab.data('tab') === 'lorem-detector' || $activeTab.data('tab') === 'search-replace')) {
            $('.slmm-form-actions').hide();
        }
    }

    // Form handling
    function initializeFormHandling() {
        const $form = $('.slmm-settings-form');
        const $saveButton = $('.slmm-save-button');
        
        // Handle form submission
        $form.on('submit', function(e) {
            // Show loading state
            $form.addClass('saving');
            $saveButton.addClass('saving');
            
            // Create a timeout to ensure loading state shows even for fast submissions
            setTimeout(function() {
                // The form will submit normally, WordPress will handle the reload
            }, 500);
        });
        
        // Handle form changes
        $form.on('change input', 'input, textarea, select', function() {
            // Mark form as changed
            $form.addClass('changed');
            
            // Show unsaved changes indicator
            if (!$('.slmm-unsaved-indicator').length) {
                $saveButton.parent().append('<span class="slmm-unsaved-indicator">• Unsaved changes</span>');
            }
        });
        
        // Remove unsaved indicator on save
        $form.on('submit', function() {
            $('.slmm-unsaved-indicator').remove();
        });
        
        // Handle page unload with unsaved changes
        $(window).on('beforeunload', function() {
            if ($form.hasClass('changed')) {
                return 'You have unsaved changes. Are you sure you want to leave?';
            }
        });
    }

    // File upload handling
    function initializeFileUpload() {
        const $fileInput = $('#slmm-import-file');
        const $fileLabel = $('.slmm-file-label');
        
        $fileInput.on('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'Choose settings file...';
            $fileLabel.find('span:last-child').text(fileName);
            
            if (this.files[0]) {
                $fileLabel.addClass('file-selected');
            } else {
                $fileLabel.removeClass('file-selected');
            }
        });
        
        // Drag and drop functionality
        $fileLabel.on('dragover dragenter', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('drag-over');
        });
        
        $fileLabel.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('drag-over');
        });
        
        $fileLabel.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('drag-over');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                $fileInput[0].files = files;
                $fileInput.trigger('change');
            }
        });
    }

    // Form validation
    function initializeFormValidation() {
        // Real-time validation for API keys
        $('input[type="password"]').on('input', function() {
            const $input = $(this);
            const value = $input.val();
            const fieldName = $input.attr('name');
            
            // Remove existing validation classes
            $input.removeClass('valid invalid');
            
            if (value.length > 0) {
                if (fieldName.includes('openai') && value.startsWith('sk-')) {
                    $input.addClass('valid');
                } else if (fieldName.includes('openrouter') && value.startsWith('sk-or-')) {
                    $input.addClass('valid');
                } else if (value.length < 10) {
                    $input.addClass('invalid');
                }
            }
        });
        
        // URL validation for webhook URLs
        $('input[type="url"]').on('input', function() {
            const $input = $(this);
            const value = $input.val();
            
            $input.removeClass('valid invalid');
            
            if (value.length > 0) {
                try {
                    new URL(value);
                    $input.addClass('valid');
                } catch (e) {
                    $input.addClass('invalid');
                }
            }
        });
    }

    // Tooltips for complex fields
    function initializeTooltips() {
        // Add tooltips to complex fields
        $('[data-tooltip]').each(function() {
            const $element = $(this);
            const tooltip = $element.data('tooltip');
            
            $element.on('mouseenter focus', function() {
                showTooltip($element, tooltip);
            });
            
            $element.on('mouseleave blur', function() {
                hideTooltip();
            });
        });
    }

    function showTooltip($element, text) {
        const $tooltip = $('<div class="slmm-tooltip">' + text + '</div>');
        $('body').append($tooltip);
        
        const elementOffset = $element.offset();
        const elementHeight = $element.outerHeight();
        
        $tooltip.css({
            top: elementOffset.top + elementHeight + 10,
            left: elementOffset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
        }).fadeIn(200);
    }

    function hideTooltip() {
        $('.slmm-tooltip').fadeOut(200, function() {
            $(this).remove();
        });
    }

    // Smooth animations
    function initializeAnimations() {
        // Animate form fields on focus
        $('.slmm-input, .slmm-textarea').on('focus', function() {
            $(this).closest('.slmm-form-field').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.slmm-form-field').removeClass('focused');
        });
        
        // Animate toggles
        $('.slmm-toggle').on('change', function() {
            const $wrapper = $(this).closest('.slmm-toggle-wrapper');
            if (this.checked) {
                $wrapper.addClass('checked');
            } else {
                $wrapper.removeClass('checked');
            }
        });
        
        // Animate progress indicators
        animateProgressBars();
    }

    function animateProgressBars() {
        $('.slmm-progress-bar').each(function() {
            const $bar = $(this);
            const percentage = $bar.data('percentage') || 0;
            
            $bar.animate({
                width: percentage + '%'
            }, 1000, 'easeOutCubic');
        });
    }

    // Keyboard shortcuts
    function initializeKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + S to save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                $('.slmm-save-button').trigger('click');
                return false;
            }
            
            // Ctrl/Cmd + E to export
            if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                e.preventDefault();
                $('.slmm-export-button').trigger('click');
                return false;
            }
            
            // Escape to close any open modals or reset forms
            if (e.key === 'Escape') {
                hideTooltip();
                $('.slmm-modal').fadeOut(200);
            }
            
            // Tab navigation between settings tabs
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                const $activeTab = $('.slmm-tab-button.active');
                if ($activeTab.length && e.target === document.body) {
                    e.preventDefault();
                    
                    const $tabs = $('.slmm-tab-button');
                    const currentIndex = $tabs.index($activeTab);
                    let newIndex;
                    
                    if (e.key === 'ArrowLeft') {
                        newIndex = currentIndex > 0 ? currentIndex - 1 : $tabs.length - 1;
                    } else {
                        newIndex = currentIndex < $tabs.length - 1 ? currentIndex + 1 : 0;
                    }
                    
                    $tabs.eq(newIndex).trigger('click');
                }
            }
        });
    }

    // Color picker initialization
    function initializeColorPickers() {
        // Check if wp-color-picker is available
        if (typeof $.wp === 'undefined' || typeof $.wp.wpColorPicker === 'undefined') {
            console.warn('WordPress Color Picker not available');
            return;
        }

        // Initialize color picker for development mode
        $('.slmm-color-picker').wpColorPicker({
            defaultColor: '#7a39e8',
            change: function(event, ui) {
                // Optional: Add real-time preview functionality
                const color = ui.color.toString();
                console.log('Color changed to:', color);
                
                // Mark form as changed
                $(this).closest('form').addClass('changed');
                
                // Show unsaved changes indicator
                const $saveButton = $('.slmm-save-button');
                if (!$('.slmm-unsaved-indicator').length) {
                    $saveButton.parent().append('<span class="slmm-unsaved-indicator">• Unsaved changes</span>');
                }
            },
            clear: function() {
                // Handle color clear
                console.log('Color cleared');
                
                // Reset to default color
                $(this).wpColorPicker('color', '#7a39e8');
            }
        });
    }

    // Search and Replace functionality
    function initializeSearchReplace() {
        let isSearchReplaceRunning = false;
        
        // Show/hide search-replace form based on active tab
        $(document).on('slmm:tabChanged', function(e, tabId) {
            if (tabId === 'search-replace') {
                // Show the search & replace form
                $('.slmm-search-replace-form-container').show();
                // Position it within the search-replace tab
                $('#slmm-search-replace-form-placeholder').append($('.slmm-search-replace-form-container'));
                // Load tables
                loadDatabaseTables();
            } else {
                // Hide the search & replace form and move it back to its original location
                $('.slmm-search-replace-form-container').hide();
                $('body').append($('.slmm-search-replace-form-container'));
            }
        });
        
        // If search-replace tab is already active on page load
        if ($('.slmm-tab-button.active').data('tab') === 'search-replace') {
            $('.slmm-search-replace-form-container').show();
            $('#slmm-search-replace-form-placeholder').append($('.slmm-search-replace-form-container'));
            loadDatabaseTables();
        }
        
        // Table selection buttons
        $(document).on('click', '#select-all-tables', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', true);
        });
        
        $(document).on('click', '#select-core-tables', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', false);
            $('#table-list input[type="checkbox"][data-core="true"]').prop('checked', true);
        });
        
        $(document).on('click', '#deselect-all-tables', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', false);
        });
        
        // BSR-style form interception - bind to button click instead of form submit
        $(document).on('click', '#slmm-start-search-replace', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Search & Replace button clicked'); // Debug log
            
            if (isSearchReplaceRunning) {
                console.log('Search & Replace already running, ignoring click');
                return false;
            }
            
            const $button = $(this);
            const $form = $button.closest('form');
            const $searchText = $button.find('.slmm-search-text');
            const $loadingText = $button.find('.slmm-search-loading');
            const $resultsContainer = $('#slmm-search-replace-results');
            
            console.log('Form element:', $form); // Debug log
            
            // Get form data
            const formData = {
                action: 'slmm_search_replace',
                nonce: $form.find('input[name="slmm_search_replace_nonce"]').val(),
                search_text: $form.find('#search_text').val(),
                replace_text: $form.find('#replace_text').val(),
                case_insensitive: $form.find('#case_insensitive').is(':checked'),
                dry_run: $form.find('#dry_run').is(':checked'),
                whole_words: $form.find('#whole_words').is(':checked'),
                selected_tables: []
            };
            
            // Get selected tables
            $form.find('#table-list input[type="checkbox"]:checked').each(function() {
                formData.selected_tables.push($(this).val());
            });
            
            console.log('Form data:', formData); // Debug log
            
            // Enhanced validation
            if (!formData.nonce) {
                console.error('Missing nonce field');
                showNotification('Security error: Missing security token. Please refresh the page and try again.', 'error');
                return false;
            }
            
            // Validate AJAX URL availability
            if (typeof slmmSettings === 'undefined' || !slmmSettings.ajaxurl) {
                console.error('AJAX URL not available');
                showNotification('Configuration error: AJAX endpoint not available. Please refresh the page.', 'error');
                return false;
            }
            
            // Validate form
            if (!formData.search_text.trim()) {
                showNotification('Please enter text to search for', 'error');
                return false;
            }
            
            if (formData.selected_tables.length === 0) {
                showNotification('Please select at least one table to search', 'error');
                return false;
            }
            
            // Show confirmation for non-dry-run operations
            if (!formData.dry_run) {
                const confirmMessage = 'This will modify your database. Are you sure you want to proceed?\n\nMake sure you have a backup of your database.';
                if (!confirm(confirmMessage)) {
                    return false;
                }
            }
            
            console.log('Starting AJAX request'); // Debug log
            
            // Start search and replace
            isSearchReplaceRunning = true;
            $button.prop('disabled', true);
            $searchText.hide();
            $loadingText.show();
            $resultsContainer.empty();
            
            // Perform AJAX request
            $.ajax({
                url: slmmSettings.ajaxurl,
                type: 'POST',
                data: formData,
                timeout: 300000, // 5 minutes timeout
                beforeSend: function() {
                    console.log('Sending AJAX request to:', slmmSettings.ajaxurl);
                    console.log('Request data:', formData);
                },
                success: function(response) {
                    console.log('AJAX success response:', response); // Debug log
                    if (response.success) {
                        displaySearchReplaceResults(response.data);
                        const totalReplacements = response.data.total_replacements || 0;
                        const rowsAffected = response.data.total_rows_affected || 0;
                        if (response.data.dry_run) {
                            showNotification(`Dry run completed successfully. Found ${totalReplacements} potential replacements in ${rowsAffected} posts/items (excluding revisions).`, 'success');
                        } else {
                            showNotification(`Search and replace completed successfully! Made ${totalReplacements} replacements in ${rowsAffected} posts/items (excluding revisions).`, 'success');
                        }
                    } else {
                        console.error('Server returned error:', response.data);
                        showNotification('Error: ' + response.data, 'error');
                        $resultsContainer.html('<div class="slmm-error">Error: ' + response.data + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX error details:', { xhr, status, error, responseText: xhr.responseText }); // Enhanced debug log
                    
                    let errorMessage = 'An unexpected error occurred';
                    let showRetry = false;
                    
                    // Enhanced error handling based on different error types
                    if (status === 'timeout') {
                        errorMessage = 'Operation timed out. This usually happens with large databases. Try selecting fewer tables or contact support.';
                        showRetry = true;
                    } else if (status === 'abort') {
                        errorMessage = 'Request was cancelled. Please try again.';
                        showRetry = true;
                    } else if (xhr.status === 0) {
                        errorMessage = 'Network connection lost. Please check your internet connection and try again.';
                        showRetry = true;
                    } else if (xhr.status === 403) {
                        errorMessage = 'Permission denied. You may not have sufficient privileges to perform this operation.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'Server endpoint not found. Please refresh the page and try again.';
                        showRetry = true;
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error occurred. Please try again with fewer tables or contact support.';
                        showRetry = true;
                    } else if (xhr.responseJSON && xhr.responseJSON.data) {
                        errorMessage = xhr.responseJSON.data;
                    } else if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.data) {
                                errorMessage = response.data;
                            }
                        } catch (e) {
                            // If response is not JSON, show a generic error
                            errorMessage = 'Server returned an invalid response. Please try again.';
                            showRetry = true;
                        }
                    }
                    
                    showNotification('Error: ' + errorMessage, 'error');
                    
                    let errorHtml = '<div class="slmm-error">';
                    errorHtml += '<strong>Error:</strong> ' + errorMessage;
                    if (showRetry) {
                        errorHtml += '<br><br><button type="button" class="button button-secondary" onclick="$(\'.slmm-search-replace-btn\').click();">Try Again</button>';
                    }
                    errorHtml += '</div>';
                    
                    $resultsContainer.html(errorHtml);
                },
                complete: function() {
                    console.log('AJAX complete'); // Debug log
                    isSearchReplaceRunning = false;
                    $button.prop('disabled', false);
                    $searchText.show();
                    $loadingText.hide();
                }
            });
        });
        
        // Also prevent form submission as a backup
        $(document).on('submit', '#slmm-search-replace-form', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Form submission prevented'); // Debug log
            return false;
        });
        
        function loadDatabaseTables() {
            const $tableList = $('#table-list');
            
            if ($tableList.length === 0) {
                return;
            }
            
            $tableList.html('<div class="slmm-loading">Loading tables...</div>');
            
            $.ajax({
                url: slmmSettings.ajaxurl,
                type: 'POST',
                data: {
                    action: 'slmm_get_tables',
                    nonce: $('input[name="slmm_search_replace_nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        displayTableList(response.data.tables);
                    } else {
                        $tableList.html('<div class="slmm-error">Error loading tables: ' + response.data + '</div>');
                    }
                },
                error: function() {
                    $tableList.html('<div class="slmm-error">Error loading tables. Please refresh the page.</div>');
                }
            });
        }
        
        function displayTableList(tables) {
            const $tableList = $('#table-list');
            let html = '';
            
            // Sort tables: selected first, then by name
            tables.sort(function(a, b) {
                // First, check if either table was previously selected
                const aSelected = $tableList.find('input[value="' + a.name + '"]').is(':checked');
                const bSelected = $tableList.find('input[value="' + b.name + '"]').is(':checked');
                
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                
                // If both same selection state, sort by name
                return a.name.localeCompare(b.name);
            });
            
            tables.forEach(function(table) {
                const coreClass = table.is_core ? ' slmm-core-table' : '';
                const coreLabel = table.is_core ? ' (Core)' : '';
                
                html += '<label class="slmm-table-checkbox' + coreClass + '">';
                html += '<input type="checkbox" value="' + escapeHtml(table.name) + '" data-core="' + table.is_core + '">';
                html += '<span class="slmm-checkmark"></span>';
                html += '<span class="slmm-table-name">' + escapeHtml(table.name) + coreLabel + '</span>';
                html += '<span class="slmm-table-rows">(' + table.rows + ' rows)</span>';
                html += '</label>';
            });
            
            $tableList.html(html);
            
            // Re-bind checkbox change handlers for selection styling and sorting
            initializeTableSelection();
        }
        
        // Initialize table selection behavior
        function initializeTableSelection() {
            $('#table-list').off('change.table-selection').on('change.table-selection', 'input[type="checkbox"]', function() {
                const $label = $(this).closest('.slmm-table-checkbox');
                const $tableList = $('#table-list');
                
                // Update visual state
                if ($(this).is(':checked')) {
                    $label.addClass('selected');
                } else {
                    $label.removeClass('selected');
                }
                
                // Move selected items to top with animation
                setTimeout(function() {
                    sortTableList();
                }, 100);
            });
        }
        
        // Sort table list with selected items at top
        function sortTableList() {
            const $tableList = $('#table-list');
            const $labels = $tableList.find('.slmm-table-checkbox');
            
            // Convert to array and sort
            const labelsArray = $labels.toArray().sort(function(a, b) {
                const aSelected = $(a).find('input').is(':checked');
                const bSelected = $(b).find('input').is(':checked');
                const aName = $(a).find('.slmm-table-name').text();
                const bName = $(b).find('.slmm-table-name').text();
                
                // Selected items first
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                
                // Then sort by name
                return aName.localeCompare(bName);
            });
            
            // Reorder with smooth animation
            $tableList.fadeOut(200, function() {
                $tableList.empty().append(labelsArray).fadeIn(200);
            });
        }
        
        function displaySearchReplaceResults(data) {
            const $resultsContainer = $('#slmm-search-replace-results');
            let html = '<div class="slmm-search-replace-results">';
            
            html += '<h3>Search and Replace Results</h3>';
            
            if (data.dry_run) {
                html += '<div class="slmm-dry-run-notice">🔍 <strong>Dry Run</strong> - No changes were made to the database</div>';
            }
            
            // Enhanced summary with detailed metrics
            html += '<div class="slmm-results-summary">';
            html += '<div class="slmm-summary-stats">';
            html += '<div class="slmm-stat"><strong>String Replacements:</strong> ' + data.total_replacements + '</div>';
            html += '<div class="slmm-stat"><strong>Rows Affected:</strong> ' + data.total_rows_affected + '</div>';
            html += '<div class="slmm-stat"><strong>Fields Modified:</strong> ' + data.total_field_replacements + '</div>';
            html += '<div class="slmm-stat"><strong>Tables Processed:</strong> ' + data.summary.tables_processed + '</div>';
            html += '</div>';
            html += '</div>';
            
            if (Object.keys(data.results).length === 0) {
                html += '<div class="slmm-no-results">No matches found in the selected tables.</div>';
            } else {
                // Table-by-table breakdown
                html += '<div class="slmm-results-table">';
                html += '<h4>Tables Modified</h4>';
                html += '<table class="slmm-results-table-inner">';
                html += '<thead><tr><th>Table</th><th>String Replacements</th><th>Rows Affected</th><th>Fields Modified</th></tr></thead>';
                html += '<tbody>';
                
                Object.keys(data.results).forEach(function(tableName) {
                    const result = data.results[tableName];
                    html += '<tr>';
                    html += '<td>' + escapeHtml(tableName) + '</td>';
                    html += '<td>' + result.replacements + '</td>';
                    html += '<td>' + result.rows_affected + '</td>';
                    html += '<td>' + result.field_replacements + '</td>';
                    html += '</tr>';
                });
                
                html += '</tbody></table>';
                html += '</div>';
                
                // Affected posts section (if any) - with edit/view buttons
                if (data.affected_posts && data.affected_posts.length > 0) {
                    html += '<div class="slmm-affected-posts">';
                    html += '<h4>Affected Posts/Items</h4>';
                    
                    // Filter posts vs options
                    const actualPosts = data.affected_posts.filter(function(item) { return item.ID; });
                    const optionItems = data.affected_posts.filter(function(item) { return item.option_name; });
                    
                    if (actualPosts.length > 0) {
                        html += '<div class="slmm-posts-table-wrapper">';
                        html += '<table class="slmm-posts-table">';
                        html += '<thead>';
                        html += '<tr>';
                        html += '<th>Post Title</th>';
                        html += '<th>Type</th>';
                        html += '<th>Status</th>';
                        html += '<th>Actions</th>';
                        html += '</tr>';
                        html += '</thead>';
                        html += '<tbody>';
                        
                        actualPosts.forEach(function(post) {
                            html += '<tr>';
                            html += '<td class="slmm-post-title">' + escapeHtml(post.post_title || '(No Title)') + '</td>';
                            html += '<td class="slmm-post-type">' + escapeHtml(post.post_type || 'post') + '</td>';
                            html += '<td class="slmm-post-status">';
                            html += '<span class="slmm-status-badge slmm-status-' + (post.post_status || 'publish') + '">';
                            html += escapeHtml((post.post_status || 'publish').charAt(0).toUpperCase() + (post.post_status || 'publish').slice(1));
                            html += '</span>';
                            html += '</td>';
                            html += '<td class="slmm-post-actions">';
                            
                            // Edit button (always available for posts)
                            const editUrl = generateEditUrl(post.ID, post.post_type);
                            if (editUrl) {
                                html += '<a href="' + escapeHtml(editUrl) + '" class="slmm-action-link slmm-edit-link" target="_blank">Edit</a>';
                            }
                            
                            // View button (only for published posts)
                            if (post.post_status === 'publish') {
                                const viewUrl = generateViewUrl(post.ID);
                                if (viewUrl) {
                                    html += '<a href="' + escapeHtml(viewUrl) + '" class="slmm-action-link slmm-view-link" target="_blank">View</a>';
                                }
                            }
                            
                            html += '</td>';
                            html += '</tr>';
                        });
                        
                        html += '</tbody>';
                        html += '</table>';
                        html += '</div>';
                    }
                    
                    // Show options separately if any
                    if (optionItems.length > 0) {
                        if (actualPosts.length > 0) {
                            html += '<h5>WordPress Options Modified</h5>';
                        }
                        html += '<div class="slmm-options-list">';
                        
                        optionItems.forEach(function(option) {
                            html += '<div class="slmm-option-item">';
                            html += '<strong>Option:</strong> ' + escapeHtml(option.option_name) + ' ';
                            html += '<span class="slmm-option-preview">' + escapeHtml(option.option_value_preview) + '</span>';
                            html += '</div>';
                        });
                        
                        html += '</div>';
                    }
                    
                    html += '</div>';
                }
                
                // Column modification statistics
                if (data.modified_columns && Object.keys(data.modified_columns).length > 0) {
                    html += '<div class="slmm-column-stats">';
                    html += '<h4>Column Modification Statistics</h4>';
                    html += '<div class="slmm-column-list">';
                    
                    Object.keys(data.modified_columns).forEach(function(column) {
                        const count = data.modified_columns[column];
                        html += '<div class="slmm-column-stat">';
                        html += '<strong>' + escapeHtml(column) + ':</strong> ' + count + ' modifications';
                        html += '</div>';
                    });
                    
                    html += '</div>';
                    html += '</div>';
                }
                
                // Detailed change information (collapsed by default)
                if (data.change_details && data.change_details.length > 0) {
                    html += '<div class="slmm-change-details">';
                    html += '<h4>Detailed Changes <button type="button" class="slmm-toggle-details" onclick="toggleChangeDetails()">Show Details</button></h4>';
                    html += '<div id="slmm-change-details-content" style="display: none;">';
                    
                    data.change_details.forEach(function(change) {
                        html += '<div class="slmm-change-item">';
                        html += '<strong>Post ID:</strong> ' + change.post_id + ' ';
                        html += '<span class="slmm-change-title">' + escapeHtml(change.post_title) + '</span><br>';
                        html += '<strong>Column:</strong> ' + escapeHtml(change.column) + ' ';
                        html += '<strong>Replacements:</strong> ' + change.replacements + '<br>';
                        if (change.preview && change.preview.context_original) {
                            html += '<div class="slmm-change-preview">';
                            html += '<strong>Before:</strong> ...' + escapeHtml(change.preview.context_original) + '...<br>';
                            html += '<strong>After:</strong> ...' + escapeHtml(change.preview.context_updated) + '...';
                            html += '</div>';
                        }
                        html += '</div>';
                    });
                    
                    html += '</div>';
                    html += '</div>';
                }
            }
            
            html += '</div>';
            $resultsContainer.html(html);
            
            // Initialize action links after results are displayed
            initializeResultsActions();
        }
        
        // Initialize action links behavior (same as Lorem Ipsum Detector)
        function initializeResultsActions() {
            // Handle action link clicks
            $('.slmm-action-link').off('click.search-replace').on('click.search-replace', function(e) {
                // Links will open in new tabs as specified in HTML
                const action = $(this).hasClass('slmm-edit-link') ? 'edit' : 'view';
                const postTitle = $(this).closest('tr').find('.slmm-post-title').text();
                
                console.log(`Opening ${action} for: ${postTitle}`);
            });
            
            // Add hover effects to result tables
            $('.slmm-posts-table tbody tr').hover(
                function() {
                    $(this).addClass('hover');
                },
                function() {
                    $(this).removeClass('hover');
                }
            );
        }
        
        // Toggle function for detailed changes
        function toggleChangeDetails() {
            const $content = $('#slmm-change-details-content');
            const $button = $('.slmm-toggle-details');
            
            if ($content.is(':visible')) {
                $content.slideUp();
                $button.text('Show Details');
            } else {
                $content.slideDown();
                $button.text('Hide Details');
            }
        }
        
        // Generate WordPress edit URL for posts (same pattern as Lorem Ipsum Detector)
        function generateEditUrl(postId, postType) {
            if (!postId) return null;
            
            // Use WordPress admin URL pattern
            const adminUrl = (typeof ajaxurl !== 'undefined') ? 
                ajaxurl.replace('/admin-ajax.php', '') : 
                '/wp-admin';
            
            return adminUrl + '/post.php?post=' + postId + '&action=edit';
        }
        
        // Generate WordPress view URL for published posts
        function generateViewUrl(postId) {
            if (!postId) return null;
            
            // For published posts, we can use the standard WordPress URL pattern
            // Note: This is a basic implementation - in a full implementation you might
            // want to make an AJAX call to get the actual permalink
            const siteUrl = window.location.origin;
            return siteUrl + '/?p=' + postId;
        }
        
        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    }

    // Utility functions
    function showNotification(message, type = 'success') {
        const $notification = $('<div class="slmm-notification slmm-notification-' + type + '">' + message + '</div>');
        
        $('body').append($notification);
        
        $notification.fadeIn(300).delay(3000).fadeOut(300, function() {
            $(this).remove();
        });
    }

    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // Search functionality for large option lists
    function initializeSearch() {
        $('.slmm-search-input').on('input', debounce(function() {
            const query = $(this).val().toLowerCase();
            const $target = $($(this).data('target'));
            
            $target.find('.slmm-option').each(function() {
                const text = $(this).text().toLowerCase();
                $(this).toggle(text.includes(query));
            });
        }, 300));
    }

    // Auto-save functionality for large forms
    let autoSaveTimeout;
    function initializeAutoSave() {
        $('.slmm-settings-form').on('change input', 'input, textarea, select', debounce(function() {
            if (typeof slmmSettings !== 'undefined' && slmmSettings.autoSave) {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(function() {
                    // Auto-save implementation would go here
                    console.log('Auto-saving...');
                }, 2000);
            }
        }, 500));
    }

    // Handle AJAX form submissions for individual sections
    function initializeAjaxForms() {
        $('.slmm-ajax-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $button = $form.find('button[type="submit"]');
            const originalText = $button.text();
            
            $button.text('Saving...').prop('disabled', true);
            
            $.ajax({
                url: slmmSettings.ajaxurl,
                type: 'POST',
                data: $form.serialize(),
                success: function(response) {
                    if (response.success) {
                        showNotification('Settings saved successfully!', 'success');
                        $form.removeClass('changed');
                    } else {
                        showNotification('Error saving settings: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showNotification('Network error. Please try again.', 'error');
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });
    }

    // Initialize additional features
    initializeSearch();
    initializeAutoSave();
    initializeAjaxForms();

    // Global event handlers
    $(document).on('slmm:tabChanged', function(e, tabId) {
        // Update URL hash without triggering page scroll
        if (history.replaceState) {
            history.replaceState(null, null, '#' + tabId);
        }
        
        // Focus first input in new tab
        setTimeout(function() {
            $('#' + tabId).find('input, textarea, select').first().focus();
        }, 300);
    });

    // Handle responsive tab navigation
    function handleResponsiveTabs() {
        const $tabsNav = $('.slmm-tabs-nav');
        const $activeTab = $('.slmm-tab-button.active');
        
        if ($activeTab.length && $tabsNav.length) {
            const navScrollLeft = $tabsNav.scrollLeft();
            const navWidth = $tabsNav.outerWidth();
            const tabLeft = $activeTab.position().left;
            const tabWidth = $activeTab.outerWidth();
            
            if (tabLeft < 0) {
                $tabsNav.animate({ scrollLeft: navScrollLeft + tabLeft - 20 }, 300);
            } else if (tabLeft + tabWidth > navWidth) {
                $tabsNav.animate({ scrollLeft: navScrollLeft + (tabLeft + tabWidth - navWidth) + 20 }, 300);
            }
        }
    }

    // Call on tab change and window resize
    $(document).on('slmm:tabChanged', handleResponsiveTabs);
    $(window).on('resize', debounce(handleResponsiveTabs, 250));

    // Debug helper (remove in production)
    if (typeof console !== 'undefined' && console.log) {
        console.log('SLMM Settings initialized successfully');
    }
}); 