(function(){var g,k=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},l=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},m=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},p=m(this),q=function(a,b){if(b)a:{var c=p;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&l(c,a,{configurable:!0,writable:!0,value:b})}};
    q("Symbol",function(a){if(a)return a;var b=function(f,h){this.$jscomp$symbol$id_=f;l(this,"description",{configurable:!0,writable:!0,value:h})};b.prototype.toString=function(){return this.$jscomp$symbol$id_};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6","es3");
    q("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=p[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&l(d.prototype,a,{configurable:!0,writable:!0,value:function(){return r(k(this))}})}return a},"es6","es3");var r=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
    q("globalThis",function(a){return a||p},"es_2020","es3");var t=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};q("Array.prototype.keys",function(a){return a?a:function(){return t(this,function(b){return b})}},"es6","es3");
    q("Array.prototype.values",function(a){return a?a:function(){return t(this,function(b,c){return c})}},"es8","es3");var u=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
    q("String.prototype.repeat",function(a){return a?a:function(b){var c=u(this,null,"repeat");if(b<0||b>1342177279)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}},"es6","es3");q("String.prototype.padStart",function(a){return a?a:function(b,c){var d=u(this,null,"padStart");b-=d.length;c=c!==void 0?String(c):" ";return(b>0&&c?c.repeat(Math.ceil(b/c.length)).substring(0,b):"")+d}},"es8","es3");
    q("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var h=d[f];if(b.call(c,h,f,d)){b=h;break a}}b=void 0}return b}},"es6","es3");/*

     Copyright The Closure Library Authors.
     SPDX-License-Identifier: Apache-2.0
     */
    var v=this||self,y=function(a,b,c,d){a=a.split(".");d=d||v;for(var e;a.length&&(e=a.shift());)if(a.length||b===void 0)d=d[e]&&d[e]!==Object.prototype[e]?d[e]:d[e]={};else if(!c&&w(b)&&w(d[e]))for(var f in b)b.hasOwnProperty(f)&&(d[e][f]=b[f]);else d[e]=b},w=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"};/*

     Copyright Google LLC
     SPDX-License-Identifier: Apache-2.0
     */
    var z={};function A(a){if(a!==z)throw Error("Bad secret");};var B=globalThis.trustedTypes,C;function D(){var a=null;if(!B)return a;try{var b=function(c){return c};a=B.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){throw c;}return a};var E=function(a,b){A(a);this.privateDoNotAccessOrElseWrappedUrl=b};E.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedUrl};new E(z,"about:blank");new E(z,"about:invalid#zClosurez");var F=[],G=function(a){console.warn("A URL with content '"+a+"' was sanitized away.")};F.indexOf(G)===-1&&F.push(G);var I=function(a,b){A(a);this.privateDoNotAccessOrElseWrappedHtml=b};I.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml+""};var J=function(a,b){this.lodash_=a;this.timeout_=b};J.prototype.copyWidgetData=function(a){a=a||[];this.copyToClipboard_(this.parseWidgetData_(a))};J.prototype.parseWidgetData_=function(a){var b=a[1].values,c=[];c.push(a[0].value);b.forEach(function(d,e){return c.push(e+1+") "+d)});return c};
    J.prototype.copyToClipboard_=function(a){var b=a.join("\r\n"),c=document.createElement("div");c.textContent=b;c.style.opacity="0";document.body.appendChild(c);var d=document.createRange();d.selectNode(c);window.getSelection().removeAllRanges();window.getSelection().addRange(d);var e=function(f){document.removeEventListener("copy",e);f.clipboardData!==void 0&&(f.clipboardData.setData("text/plain",b),f.clipboardData.setData("text/html",a.join("<br>")),f.preventDefault())};document.addEventListener("copy",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         e);document.execCommand("copy");c.remove();this.onCopyNotify_()};J.prototype.onCopyNotify_=function(){if(document.getElementsByClassName("copy-notification").length===0){var a=document.createElement("div");a.textContent=document.getElementsByClassName("translate-patch")[0].textContent;a.className="copy-notification";document.body.appendChild(a);this.timeout_(this.lodash_.bind(function(){a.remove()},a),2E3)}};y("$jscomp$scope$1445061910$0$ClipboardService",J,!0,void 0);J.$inject=["lodash","$timeout"];var K=function(){this.oldTypeToNewTypeDictionary_={TIMESERIES_GRAPH_AVERAGES_CHART:"TIMESERIES",TIMESERIES_GRAPH_0:"TIMESERIES",GEO_TABLE:"GEO_MAP",TOP_ENTITIES:"RELATED_TOPICS",TOP_QUERIES:"RELATED_QUERIES",RISING_QUERIES:"RELATED_QUERIES",GEO_MAP:"GEO_MAP"}};g=K.prototype;g.convertDateWithHours_=function(a,b){var c=a.match(b);b=c[1];a=parseInt(c[2],10);c=c[4];var d=new Date(b);d.setHours(a);b=new Date(b);b.setHours(parseInt(a,10)+parseInt(c,10));return""+this.dateTimeStringFormat_(d)+(" "+this.dateTimeStringFormat_(b))};
    g.dateStringFormat_=function(a){return""+a.getFullYear()+("-"+(a.getMonth()+1).toString().padStart(2,"0"))+("-"+a.getDate().toString().padStart(2,"0"))};g.convertDateWithMonth_=function(a,b){a=a.match(b);b=parseInt(a[1],10);var c=parseInt(a[2],10),d=new Date(c,b-1,1);c=new Date(c,b-1,1);c.setMonth(b+parseInt(a[3],10)-1,0);return""+this.dateStringFormat_(d)+(" "+this.dateStringFormat_(c))};
    g.dateTimeStringFormat_=function(a){return this.dateStringFormat_(a)+("T"+a.getHours().toString().padStart(2,"0"))};
    g.convertOldEmbedParams=function(a){var b=this,c={};if(Object.keys(a).length===0)return null;var d;a.geo&&(d=a.geo.replace(/(\w+,?)\s?/g,"$1"));var e=a.date?a.date.split(",").map(function(x){return b.convertDateParam_(x)}).join(","):"all";a.cat&&(c.cat=a.cat.replace(/(.*-)?(\d+)$/,"$2"));c.comparisonItems={};var f=[];a.q&&a.q.split(",").forEach(function(x){f.push({keyword:x,geo:d||"",time:e})},this);var h=a.cid||"",H="",n=h.match(/(^\w*)[_](\d)[_]\d/);n&&n.length>0&&(h=n[1],n[2]!=0&&(H="_"+n[2]));
        h=this.oldTypeToNewTypeDictionary_[h]+H;c.q=a.q;c.widgetType=h;c.comparisonItems.comparisonItem=f;c.comparisonItems.category=c.cat||0;c.comparisonItems.property=a.gprop||"";return c};g.convertDateParam_=function(a){if(a==="")return"all";a=a.replace(/\+/g," ");if(/now|today/.test(a))return a;var b=null,c=/(\d{1,2})\/(\d{4}) (\d*)M/i,d=/(\d{4}-\d{2}-\d{2})T(\d{2})([\d\\:]*) (\d+)H/i;c.test(a)?b=this.convertDateWithMonth_(a,c):d.test(a)&&(b=this.convertDateWithHours_(a,d));return b};
    y("$jscomp$scope$1847708173$0$OldFeCompatibilityUtil",K,!0,void 0);var L,M=new K,N=function(a,b,c){c||(c={});c.guestPath||(c.guestPath="https://trends.google.com/trends/embed/");this.url_=a||"";this.params_=b||{};this.config_=c},O=function(a,b,c){return new N(b?a+"/"+b:a,{},c)},P=function(a,b,c){return new N("explore/"+a,{req:b,tz:(new Date).getTimezoneOffset()},c)};g=N.prototype;g.createIframe_=function(a){var b=document.createElement("iframe"),c;for(c in a)b.setAttribute(c,a[c]);return b};
    g.createIframeCycle_=function(a,b){var c=this;this.socketHandshake_(a,b,function(d){d.data.isReady&&(a.style.borderRadius="2px",a.style.boxShadow="0px 0px 2px 0px rgba(0,0,0,0.12), 0px 2px 2px 0px rgba(0,0,0,0.24)");d.data.height&&(a.style.height=d.data.height+"px",d.data.isIeAndLineChart&&c.setMobilePreviewLineChartWidthInIe_())})};
    g.setMobilePreviewLineChartWidthInIe_=function(){var a=document.getElementsByTagName("embed-widget-preview")[0];a.length!==0&&a.getAttribute("force-mobile-mode")!=="false"&&(a=a.find("iframe"))&&(a=a.contentDocument.getElementsByClassName("fe-line-chart"),a.length!==0&&a[0].setAttribute("width",this.IFRAME_WIDTH_+"px"))};
    g.generate_=function(){if(!/^(https?:\/\/)?([a-z0-9\.]+)?(\.google\.com)?/i.test(this.config_.guestPath)&&/^[^.]+\.[a-z]{2,4}\/?/i.test(this.config_.guestPath))console.error("Iframe caller domain name not allowed!");else if(this.config_.width&&!/^\d{1,4}(px|%)?$/i.test(this.config_.width))console.error("Width parameter contain illegal value!");else{var a=this.config_.guestPath+this.url_,b=[],c="trends-widget-"+this.generateSeed_(),d;for(d in this.params_)Object.prototype.hasOwnProperty.call(this.params_,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    d)&&b.push(d+"="+encodeURIComponent(JSON.stringify(this.params_[d])));this.config_.timeStamp&&b.push("ts="+this.config_.timeStamp);this.config_.forceMobileMode!==void 0&&b.push("forceMobileMode="+!!this.config_.forceMobileMode);this.config_.isPreviewMode!==void 0&&b.push("isPreviewMode="+!!this.config_.isPreviewMode);this.config_.exploreQuery&&b.push("eq="+encodeURIComponent(this.config_.exploreQuery));this.config_.locale&&b.push("hl="+this.config_.locale.replace(/[^a-z]/ig,""));this.config_.geo&&
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        b.push("geo="+this.config_.geo);b.length&&(a+="?"+b.join("&"));a=this.createIframe_({id:c,title:c,src:a,width:this.config_.width||"100%",frameBorder:0,scrolling:0});return{id:c,element:a}}};g.generateSeed_=function(){return L?++L:L=1};
    g.render=function(){var a=this.generate_();if(a){var b=a.element.outerHTML;b=b===null?"null":b===void 0?"undefined":b;if(typeof b!=="string")throw Error("Expected a string");C===void 0&&(C=D());var c=C;var d=new I(z,c?c.createHTML(b):b);b=document;c=b.write;if(d instanceof I)d=d.privateDoNotAccessOrElseWrappedHtml;else throw Error("Unexpected type when unwrapping SafeHtml");c.call(b,d);b=document.getElementById(a.id);this.createIframeCycle_(b,a.id)}return b};
    g.renderTo=function(a){var b=this.generate_();if(b){var c=b.element;a.appendChild(c);this.createIframeCycle_(c,b.id)}return c};g.socketHandshake_=function(a,b,c){a.addEventListener("load",function(){a.contentWindow.postMessage({uniqueID:b},"*")});window.addEventListener("message",function(d){d.data.uniqueID===b&&c(d)},!1)};y("trends.embed.renderWidget",function(a,b,c){return(new O(a,b,c)).render()},!0,void 0);y("trends.embed.renderWidgetTo",function(a,b,c,d){return(new O(b,c,d)).renderTo(a)},!0,void 0);
    var Q=function(a,b,c){return(new P(a,b,c)).render()};y("trends.embed.renderExploreWidget",Q,!0,void 0);y("trends.embed.renderExploreWidgetTo",function(a,b,c,d){return(new P(b,c,d)).renderTo(a)},!0,void 0);
    y("trends.embed.renderExploreWidgetFromOldParamaters",function(a){a=R(a);var b=M.convertOldEmbedParams(a),c="";a.cat&&(c=a.cat.split("-"),c="&cat="+c[c.length-1]);Q(b.widgetType,b.comparisonItems,{exploreQuery:"date="+b.comparisonItems.comparisonItem[0].time+"&q="+b.q+(a.geo?"&geo="+a.geo:"")+(a.gprop?"&gprop="+a.gprop:"")+c},{exploreQuery:"q="+b.q+"&date="+b.comparisonItems.comparisonItem[0].time,guestPath:"https://trends.google.com/trends/"})},!0,void 0);
    var R=function(a){var b={};a.replace(/[?&](.+?)=([^&#]*)/g,function(c,d,e){b[d]=decodeURI(e).replace(/\+/g," ")});return b};}).call(this);