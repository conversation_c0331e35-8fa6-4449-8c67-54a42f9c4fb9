<?php
// File: includes/settings/general-settings.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}
add_action('admin_menu', 'admin_menu_add_external_links_as_submenu');
add_action('admin_head', 'admin_menu_add_external_links_as_submenu_jquery');

function admin_menu_add_external_links_as_submenu() {
    global $submenu;

    $menu_slug = "externallink"; // used as "key" in menus
    $menu_pos = 2; // whatever position you want your menu to appear

    // create the top level menu with the superhero dashicon
    add_menu_page( 
        'external_link',              // Page title
        'SLMM SEO',                   // Menu title
        'read',                       // Capability
        $menu_slug,                   // Menu slug
        '',                           // Callback function
        'dashicons-superhero',        // Dashicon class for menu icon
        $menu_pos                     // Menu position
    );

    // add the external links to the slug you used when adding the top level menu
    $submenu[$menu_slug][] = array('<div id="newtab1"><span class="dashicons dashicons-admin-generic"></span> SLMM SEO Settings</div>', 'manage_options', '/wp-admin/admin.php?page=chatgpt-generator-settings');
    $submenu[$menu_slug][] = array('<div id="newtab2"><span class="dashicons dashicons-clock"></span> Content Freshness</div>', 'manage_options', '/wp-admin/admin.php?page=content-freshness'); 
    $submenu[$menu_slug][] = array('<div id="newtab3"><span class="dashicons dashicons-networking"></span> Website Structure</div>', 'manage_options', '/wp-admin/admin.php?page=wp-structure-analyzer'); 
    $submenu[$menu_slug][] = array('<div id="newtab4"><span class="dashicons dashicons-editor-quote"></span> SLMM GPT Prompts</div>', 'manage_options', '/wp-admin/admin.php?page=slmm-gpt-prompts'); 
}

function admin_menu_add_external_links_as_submenu_jquery() {
    ?>
    <script type="text/javascript">
        jQuery(document).ready(function($) {   
            $('#newtab1').parent().attr('target','_self');
            $('#newtab2').parent().attr('target','_self');  
            $('#newtab3').parent().attr('target', '_self');
            $('#newtab4').parent().attr('target', '_self');

            // Add some CSS to align the dashicons with the text
            $('.toplevel_page_externallink .wp-submenu li a').css({
                'display': 'flex',
                'align-items': 'center',
            });
            $('.toplevel_page_externallink .wp-submenu li a').css({
                'min-width': '160px'
            });
            $('.toplevel_page_externallink .wp-submenu li a .dashicons').css({
                'margin-right': '5px'
            });
        

        });
        
    </script>
    <?php
}
class SLMM_General_Settings {
    private $options;
    private $visibility_controller;

    public function init() {
        static $initialized = false;
        if ($initialized) return;
        $initialized = true;
    
        add_action('admin_menu', array($this, 'add_menu_items'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_init', array($this, 'handle_export_import'));
        add_action('admin_init', array($this, 'migrate_old_settings'));
        add_action('init', array($this, 'init_page_delete'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_keyboard_shortcuts'));
        add_action('wp_ajax_keyboard_shortcut_save', array($this, 'handle_keyboard_shortcut_save'));
        add_action('wp_ajax_refresh_openai_models', array($this, 'handle_refresh_openai_models'));
        add_action('wp_ajax_slmm_get_provider_models', array($this, 'handle_get_provider_models'));
        add_action('wp_ajax_slmm_refresh_models', array($this, 'handle_refresh_models'));
        add_action('wp_ajax_slmm_clear_model_cache', array($this, 'handle_clear_model_cache'));
        add_action('wp_ajax_slmm_save_model_selection', array($this, 'handle_save_model_selection'));
        add_action('wp_ajax_slmm_search_replace', array($this, 'handle_search_replace'));
        add_action('wp_ajax_slmm_get_tables', array($this, 'handle_get_tables'));
        add_action('admin_enqueue_scripts', array($this, 'add_development_mode_styling'));
        add_action('wp_enqueue_scripts', array($this, 'add_development_mode_styling'));
    }

    public function add_menu_items() {
        global $submenu;
    
        // Add the page without adding a visible menu item
        $hookname = add_submenu_page(
            null,                           // Don't add to any menu
            'ChatGPT Generator Settings',   // Page title
            'ChatGPT Generator',            // Menu title (unused)
            'manage_options',               // Capability
            'chatgpt-generator-settings',   // Menu slug
            array($this, 'render_settings_page') // Callback function
        );
    
        // Remove from options submenu if it was added there
        if (isset($submenu['options-general.php'])) {
            foreach ($submenu['options-general.php'] as $key => $item) {
                if ($item[2] === 'chatgpt-generator-settings') {
                    unset($submenu['options-general.php'][$key]);
                    break;
                }
            }
        }
    }

    public function register_settings() {
        register_setting('chatgpt_generator_settings', 'chatgpt_generator_options', array($this, 'sanitize_options'));
        register_setting(
            'chatgpt_generator_settings',
            'enable_page_delete',
            array(
                'type' => 'boolean',
                'default' => false
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'enable_keyboard_shortcuts',
            array(
                'type' => 'boolean',
                'default' => false
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'auto_empty_trash',
            array(
                'type' => 'boolean',
                'default' => false
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'page_delete_post_types',
            array(
                'type' => 'array',
                'default' => array('post', 'page')
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'page_delete_rate_limit',
            array(
                'type' => 'integer',
                'default' => 10
            )
        );
        register_setting(
            'chatgpt_generator_settings',
            'seo_overview_post_types',
            array(
                'type' => 'array',
                'default' => array('post', 'page')
            )
        );

        $sections = array(
            'api_keys' => 'API Keys',
            'prompts' => 'Prompts',
            'business_info' => 'Business Information',
            'models' => 'AI Models',
            'protected_words' => 'Protected Words',
            'features' => 'Plugin Features'
        );

        foreach ($sections as $section_id => $section_title) {
            add_settings_section(
                $section_id,
                $section_title,
                array($this, "render_{$section_id}_section"),
                'chatgpt-generator-settings'
            );
        }

        $fields = array(
            'api_keys' => array(
                'openai_api_key' => 'OpenAI API Key',
                'openrouter_api_key' => 'OpenRouter API Key',
            ),
            'prompts' => array(
                'title_prompt' => 'Title Prompt',
                'description_prompt' => 'Description Prompt',
            ),
            'business_info' => array(
                'business_name' => 'Business Name',
                'phone_number' => 'Phone Number',
            ),
            'models' => array(
                'model_for_title' => 'Title Generation Model',
                'model_for_description' => 'Description Generation Model',
            ),
            'protected_words' => array(
                'protected_words_list' => 'Protected Words List',
            ),
            'features' => array(
                // Features are now handled in render_feature_callbacks with proper grouping
            ),
        );

        foreach ($fields as $section => $section_fields) {
            foreach ($section_fields as $field => $label) {
                add_settings_field(
                    $field,
                    $label,
                    array($this, 'render_field'),
                    'chatgpt-generator-settings',
                    $section,
                    array('field' => $field, 'label' => $label)
                );
            }
        }

        add_settings_field(
            'enable_page_delete',
            __('Enable Page Delete', 'chatgpt-generator'),
            array($this, 'enable_page_delete_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'enable_keyboard_shortcuts',
            __('Enable Keyboard Shortcuts', 'chatgpt-generator'),
            array($this, 'enable_keyboard_shortcuts_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'page_delete_post_types',
            __('Allowed Post Types for Delete', 'chatgpt-generator'),
            array($this, 'page_delete_post_types_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'page_delete_rate_limit',
            __('Delete Rate Limit', 'chatgpt-generator'),
            array($this, 'page_delete_rate_limit_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'visibility_enabled',
            __('Enable Plugin Visibility Settings', 'chatgpt-generator'),
            array($this, 'visibility_enabled_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'authorized_admins',
            __('Authorized Admin Usernames', 'chatgpt-generator'),
            array($this, 'authorized_admins_callback'),
            'chatgpt-generator-settings',
            'features'
        );

        add_settings_field(
            'enable_development_mode',
            __('Enable Development Mode', 'chatgpt-generator'),
            array($this, 'enable_development_mode_callback'),
            'chatgpt-generator-settings',
            'features'
        );
    }

    public function sanitize_options($input) {
        $sanitized_input = array();
        $old_options = get_option('chatgpt_generator_options', array());
    
        foreach ($input as $key => $value) {
            switch ($key) {
                case 'openai_api_key':
                    $sanitized_input[$key] = sanitize_text_field($value);
                    break;
                case 'title_prompt':
                case 'description_prompt':
                    $sanitized_input[$key] = wp_kses_post($value);
                    break;
                case 'business_name':
                case 'phone_number':
                    $sanitized_input[$key] = sanitize_text_field($value);
                    break;
                case 'model_for_title':
                case 'model_for_description':
                    // Get the provider for this model field
                    $provider_field = $key . '_provider';
                    $provider = isset($input[$provider_field]) ? $input[$provider_field] : 'openai';
                    
                    // Validate based on the selected provider
                    if ($provider === 'openrouter') {
                        // For OpenRouter, we'll allow any model ID as it's fetched from their API
                        // Just sanitize for basic security
                        $sanitized_input[$key] = sanitize_text_field($value);
                    } else {
                        // For OpenAI, validate against available models
                        $available_models = array_keys($this->get_openai_models());
                        $sanitized_input[$key] = in_array($value, $available_models) ? $value : 'gpt-4o';
                    }
                    break;
                case 'model_for_title_provider':
                case 'model_for_description_provider':
                    // Validate provider selection
                    $sanitized_input[$key] = in_array($value, ['openai', 'openrouter']) ? $value : 'openai';
                    break;
                case 'protected_words_list':
                    // Sanitize each line and remove empty lines
                    $lines = array_filter(array_map('trim', explode("\n", $value)));
                    $sanitized_input[$key] = implode("\n", array_map('sanitize_text_field', $lines));
                    break;
                            case 'enable_checklist':
            case 'enable_seo_overview':
            case 'visibility_enabled':
                $sanitized_input[$key] = isset($value) ? (bool)$value : false;
                break;
            case 'authorized_admins':
                // Handle array of admin usernames
                if (is_array($value)) {
                    $sanitized_input[$key] = array_map('sanitize_text_field', array_filter($value));
                } else {
                    $sanitized_input[$key] = array();
                }
                break;
            case 'development_mode_color':
                // Validate hex color
                $sanitized_color = sanitize_hex_color($value);
                if (!$sanitized_color) {
                    // If invalid hex color, fall back to default
                    $sanitized_input[$key] = '#7a39e8';
                } else {
                    $sanitized_input[$key] = $sanitized_color;
                }
                break;
                default:
                    $sanitized_input[$key] = sanitize_text_field($value);
            }
        }
    
        // Preserve the API key if it's not being updated
        if (!isset($sanitized_input['openai_api_key']) && isset($old_options['openai_api_key'])) {
            $sanitized_input['openai_api_key'] = $old_options['openai_api_key'];
        }
        
        // For checkbox options, we need to explicitly set them to false if they're not in the input
        // This ensures they can be turned off
        $checkbox_options = ['enable_checklist', 'enable_seo_overview', 'visibility_enabled', 'enable_development_mode'];
        foreach ($checkbox_options as $option) {
            if (!isset($input[$option])) {
                $sanitized_input[$option] = false;
            }
        }
    
        return $sanitized_input;
    }

    public function render_api_keys_section() {
        echo '<p>Enter your API keys for OpenAI services.</p>';
    }

    public function render_prompts_section() {
        echo '<p>Configure your prompts for different content types.</p>';
    }

    public function render_business_info_section() {
        echo '<p>Enter your business information.</p>';
    }

    public function render_models_section() {
        echo '<p>Select AI models for different content types.</p>';
    }

    public function render_protected_words_section() {
        echo '<p>Enter words (one per line) that should always maintain their capitalization. This includes names of countries, states, brands, etc.</p>';
    }

    public function render_features_section() {
        echo '<p>Enable or disable specific plugin features.</p>';
    }

    public function render_field($args) {
        $options = get_option('chatgpt_generator_options', array());
        $field = $args['field'];
        $label = $args['label'];
        $value = isset($options[$field]) ? $options[$field] : '';
    
        echo '<label class="slmm-field-label">' . esc_html($label) . '</label>';
        
        switch ($field) {
            case 'openai_api_key':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='password' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input slmm-password-input' placeholder='sk-...'>";
                echo '<span class="slmm-input-icon">🔑</span>';
                echo '</div>';
                echo "<p class='slmm-field-description'>Enter your OpenAI API key here. Get one at <a href='https://platform.openai.com/settings/organization/api-keys' target='_blank' class='slmm-link'>platform.openai.com</a></p>";
                break;
            case 'openrouter_api_key':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='password' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input slmm-password-input' placeholder='sk-or-...'>";
                echo '<span class="slmm-input-icon">🔑</span>';
                echo '</div>';
                echo "<p class='slmm-field-description'>Enter your OpenRouter API key here. Get one at <a href='https://openrouter.ai/settings/keys' target='_blank' class='slmm-link'>openrouter.ai</a></p>";
                break;
            case 'title_prompt':
            case 'description_prompt':
                echo '<div class="slmm-textarea-wrapper">';
                echo "<textarea id='$field' name='chatgpt_generator_options[$field]' rows='4' class='slmm-textarea' placeholder='Enter your custom prompt here...'>" . esc_textarea($value) . "</textarea>";
                echo '</div>';
                break;
            case 'business_name':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='text' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input' placeholder='Your Business Name'>";
                echo '<span class="slmm-input-icon">🏢</span>';
                echo '</div>';
                break;
            case 'phone_number':
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='text' id='$field' name='chatgpt_generator_options[$field]' value='" . esc_attr($value) . "' class='slmm-input' placeholder='+****************'>";
                echo '<span class="slmm-input-icon">📞</span>';
                echo '</div>';
                break;
            case 'model_for_title_provider':
            case 'model_for_description_provider':
                // These are handled within the model dropdown rendering, skip them
                return;
            case 'model_for_title':
            case 'model_for_description':
                echo '<div class="slmm-model-selector-wrapper">';
                $this->render_model_dropdown($field, $value);
                echo '</div>';
                break;
            case 'protected_words_list':
                echo '<div class="slmm-textarea-wrapper">';
                echo '<textarea id="' . esc_attr($field) . '" name="chatgpt_generator_options[' . esc_attr($field) . ']" rows="10" class="slmm-textarea slmm-protected-words" placeholder="Apple&#10;Google&#10;WordPress&#10;OpenAI&#10;...">' . esc_textarea($value) . '</textarea>';
                echo '</div>';
                echo '<p class="slmm-field-description">Enter one word per line. These words will maintain their capitalization across the entire website.</p>';
                break;
            case 'enable_checklist':
                echo '<div class="slmm-toggle-wrapper">';
                echo "<input type='checkbox' id='{$field}' name='chatgpt_generator_options[{$field}]' value='1' class='slmm-toggle' " . checked($value, true, false) . ">";
                echo "<label for='{$field}' class='slmm-toggle-label'>";
                echo "<span class='slmm-toggle-slider'></span>";
                echo "<span class='slmm-toggle-text'>Enable SEO Checklist</span>";
                echo "</label>";
                echo '</div>';
                break;
            case 'enable_seo_overview':
                echo '<div class="slmm-toggle-wrapper">';
                echo "<input type='checkbox' id='{$field}' name='chatgpt_generator_options[{$field}]' value='1' class='slmm-toggle' " . checked($value, true, false) . ">";
                echo "<label for='{$field}' class='slmm-toggle-label'>";
                echo "<span class='slmm-toggle-slider'></span>";
                echo "<span class='slmm-toggle-text'>Enable SEO Overview Meta Box</span>";
                echo "</label>";
                echo '</div>';
                echo "<p class='slmm-field-description'>Shows a meta box with SEO metrics in the editor sidebar. Can be toggled from Screen Options.</p>";
                break;
            case 'seo_overview_post_types':
                $this->seo_overview_post_types_callback();
                break;
            case 'development_mode':
                $enable_development_mode = isset($options['enable_development_mode']) ? $options['enable_development_mode'] : false;
                $development_mode_color = isset($options['development_mode_color']) ? $options['development_mode_color'] : '#7a39e8';
                
                echo '<div class="slmm-toggle-wrapper">';
                echo "<input type='checkbox' id='enable_development_mode' name='chatgpt_generator_options[enable_development_mode]' value='1' class='slmm-toggle' " . checked($enable_development_mode, true, false) . ">";
                echo "<label for='enable_development_mode' class='slmm-toggle-label'>";
                echo "<span class='slmm-toggle-slider'></span>";
                echo "<span class='slmm-toggle-text'>Enable Development Mode</span>";
                echo "</label>";
                echo '</div>';
                echo "<p class='slmm-field-description'>Colorizes the WordPress admin bar to help distinguish between different sites in multiple tabs.</p>";
                
                echo '<div class="slmm-color-picker-wrapper" style="margin-top: 15px;">';
                echo '<label for="development_mode_color" class="slmm-field-label">Admin Bar Color</label>';
                echo '<div class="slmm-input-wrapper">';
                echo "<input type='text' id='development_mode_color' name='chatgpt_generator_options[development_mode_color]' value='" . esc_attr($development_mode_color) . "' class='slmm-color-picker' />";
                echo '</div>';
                echo '</div>';
                break;
        }
    }

    /**
     * Fetch available OpenAI models from the API
     */
    private function get_openai_models() {
        $options = get_option('chatgpt_generator_options', array());
        $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
        
        if (empty($api_key)) {
            return $this->get_fallback_models();
        }

        // Check if we have cached models that are still valid (cache for 1 hour)
        $cached_models = get_transient('slmm_openai_models');
        if ($cached_models !== false) {
            return $cached_models;
        }

        $url = 'https://api.openai.com/v1/models';
        $headers = array(
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json'
        );

        $response = wp_remote_get($url, array(
            'headers' => $headers,
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            return $this->get_fallback_models();
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['data']) || !is_array($data['data'])) {
            return $this->get_fallback_models();
        }

        $models = array();
        foreach ($data['data'] as $model) {
            if (isset($model['id'])) {
                // Filter for GPT models (you can adjust this filter as needed)
                if (strpos($model['id'], 'gpt') === 0 || 
                    strpos($model['id'], 'o1') === 0 || 
                    strpos($model['id'], 'o3') === 0) {
                    $models[$model['id']] = $this->format_model_name($model['id']);
                }
            }
        }

        // Sort models by name
        asort($models);

        // Cache the results for 1 hour
        set_transient('slmm_openai_models', $models, HOUR_IN_SECONDS);

        return $models;
    }

    /**
     * Get fallback models when API call fails
     */
    private function get_fallback_models() {
        return array(
            'gpt-4o' => 'GPT-4o',
            'gpt-4o-mini' => 'GPT-4o Mini',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4' => 'GPT-4',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'o1-preview' => 'o1 Preview',
            'o1-mini' => 'o1 Mini',
            'o3-mini' => 'o3 Mini'
        );
    }

    /**
     * Format model name for display
     */
    private function format_model_name($model_id) {
        $formatted_names = array(
            'gpt-4o' => 'GPT-4o',
            'gpt-4o-mini' => 'GPT-4o Mini',
            'gpt-4o-2024-08-06' => 'GPT-4o (2024-08-06)',
            'gpt-4o-2024-05-13' => 'GPT-4o (2024-05-13)',
            'gpt-4o-mini-2024-07-18' => 'GPT-4o Mini (2024-07-18)',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4-turbo-2024-04-09' => 'GPT-4 Turbo (2024-04-09)',
            'gpt-4-turbo-preview' => 'GPT-4 Turbo Preview',
            'gpt-4-0125-preview' => 'GPT-4 (0125 Preview)',
            'gpt-4-1106-preview' => 'GPT-4 (1106 Preview)',
            'gpt-4' => 'GPT-4',
            'gpt-4-0613' => 'GPT-4 (0613)',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'gpt-3.5-turbo-0125' => 'GPT-3.5 Turbo (0125)',
            'gpt-3.5-turbo-1106' => 'GPT-3.5 Turbo (1106)',
            'o1-preview' => 'o1 Preview',
            'o1-preview-2024-09-12' => 'o1 Preview (2024-09-12)',
            'o1-mini' => 'o1 Mini',
            'o1-mini-2024-09-12' => 'o1 Mini (2024-09-12)',
            'o3-mini' => 'o3 Mini',
            'o3-mini-2025-01-31' => 'o3 Mini (2025-01-31)'
        );

        if (isset($formatted_names[$model_id])) {
            return $formatted_names[$model_id];
        }

        // Convert kebab-case to Title Case for unknown models
        return ucwords(str_replace('-', ' ', $model_id));
    }

    /**
     * Render model dropdown with provider selection and search
     */
    private function render_model_dropdown($field, $current_value) {
        // Include OpenRouter integration
        require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        
        // Get current provider setting
        $options = get_option('chatgpt_generator_options', array());
        $provider_field = $field . '_provider';
        $current_provider = isset($options[$provider_field]) ? $options[$provider_field] : 'openai';
        
        // Set default value if none selected
        if (empty($current_value)) {
            $current_value = ($current_provider === 'openrouter') ? 'google/gemini-2.0-flash-exp:free' : 'gpt-4o';
        }

        // Provider Selection Section
        echo "<div class='slmm-provider-section'>";
        echo "<label><strong>AI Provider</strong></label>";
        echo "<select id='{$provider_field}' name='chatgpt_generator_options[{$provider_field}]' class='provider-select' data-field='{$field}'>";
        echo "<option value='openai'" . selected($current_provider, 'openai', false) . ">OpenAI</option>";
        if ($openrouter->is_configured()) {
            echo "<option value='openrouter'" . selected($current_provider, 'openrouter', false) . ">OpenRouter</option>";
        }
        echo "</select>";
        
        // Show note if OpenRouter is not configured
        if (!$openrouter->is_configured()) {
            echo "<p class='slmm-provider-note'>";
            echo "<em>💡 Tip: Add your OpenRouter API key above to access 200+ additional AI models</em>";
            echo "</p>";
        }
        echo "</div>";

        // Model Search Section
        echo "<div class='slmm-search-section'>";
        echo "<label><strong>Search Models</strong></label>";
        echo "<input type='text' id='model-search-{$field}' placeholder='Type to search models...' />";
        echo "</div>";

        // Model Selection Section
        echo "<div class='slmm-selection-section'>";
        echo "<label><strong>Available Models</strong></label>";
        echo "<select id='$field' name='chatgpt_generator_options[$field]' class='model-select' data-field='{$field}' size='6'>";
        
        // Get models based on current provider
        if ($current_provider === 'openrouter' && $openrouter->is_configured()) {
            $models = $openrouter->get_models();
            $description = "Choose from OpenRouter's diverse collection of AI models including GPT-4, Claude, Gemini, and more.";
        } else {
            $models = $this->get_openai_models();
            $description = "Select from OpenAI's latest models including GPT-4o, o1, and other advanced models.";
        }
        
        foreach ($models as $model_id => $model_name) {
            $selected = selected($current_value, $model_id, false);
            echo "<option value='" . esc_attr($model_id) . "' $selected>" . esc_html($model_name) . "</option>";
        }
        
        echo "</select>";
        echo "</div>";
        
        // Actions Section
        echo "<div class='slmm-actions-section'>";
        echo "<button type='button' class='refresh-models-btn' data-field='$field'>";
        echo "<span>🔄</span> Refresh Models";
        echo "</button>";
        echo "<span class='model-count' id='model-count-{$field}'>" . count($models) . " models available</span>";
        echo "</div>";
        
        echo "<p class='description'>$description <a href='#' class='clear-model-cache'>Clear cache</a> to refresh.</p>";
        
        // Enqueue model selector assets
        wp_enqueue_style('slmm-model-selector', plugin_dir_url(__FILE__) . '../../assets/css/slmm-model-selector.css', array(), '1.1');
        wp_enqueue_script('slmm-model-selector', plugin_dir_url(__FILE__) . '../../assets/js/slmm-model-selector.js', array('jquery'), '1.1', true);
        
        // Localize script with AJAX data
        wp_localize_script('slmm-model-selector', 'slmmModelSelector', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_model_selector')
        ));
        

    }

    public function seo_overview_post_types_callback() {
        echo '<p class="description">' . __('Select which post types will show the SEO Overview meta box.', 'chatgpt-generator') . '</p>';
        $allowed_types = get_option('seo_overview_post_types', array('post', 'page'));
        $post_types = get_post_types(array('public' => true), 'objects');
        foreach ($post_types as $post_type) {
            $checked = in_array($post_type->name, $allowed_types) ? 'checked' : '';
            echo "<label><input type='checkbox' name='seo_overview_post_types[]' value='" . esc_attr($post_type->name) . "' $checked> " . esc_html($post_type->label) . "</label><br>";
        }
    }

    public function enable_page_delete_callback() {
        $enable_page_delete = get_option('enable_page_delete', false);
        $auto_empty_trash = get_option('auto_empty_trash', false);
        ?>
        <input type="checkbox" id="enable_page_delete" name="enable_page_delete" value="1" <?php checked($enable_page_delete, true); ?>>
        <label for="enable_page_delete"><?php _e('Turn on Page Delete', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Adds a "Send to Trash" button in the admin bar when viewing pages/posts.', 'chatgpt-generator'); ?></p>
        
        <br>
        <input type="checkbox" id="auto_empty_trash" name="auto_empty_trash" value="1" <?php checked($auto_empty_trash, true); ?>>
        <label for="auto_empty_trash"><?php _e('Turn On Empty Trash', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Enables the Empty Trash button next to the Delete button.', 'chatgpt-generator'); ?></p>
        <?php
    }

    public function enable_keyboard_shortcuts_callback() {
        $enable_keyboard_shortcuts = get_option('enable_keyboard_shortcuts', false);
        ?>
        <input type="checkbox" id="enable_keyboard_shortcuts" name="enable_keyboard_shortcuts" value="1" <?php checked($enable_keyboard_shortcuts, true); ?>>
        <label for="enable_keyboard_shortcuts"><?php _e('Enable Keyboard Shortcuts', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Enables keyboard shortcuts (Ctrl+S / Cmd+S) for saving posts and pages from anywhere on the page.', 'chatgpt-generator'); ?></p>
        <?php
    }

    public function enable_notes_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $enable_notes = isset($options['enable_notes']) ? $options['enable_notes'] : true;
        ?>
        <input type="checkbox" id="enable_notes" name="chatgpt_generator_options[enable_notes]" value="1" <?php checked($enable_notes, true); ?>>
        <label for="enable_notes"><?php _e('Enable Project Notes', 'chatgpt-generator'); ?></label>
        <p class="description"><?php _e('Adds a notes feature to the admin bar for taking project notes across all admin pages with resizable popup and auto-save.', 'chatgpt-generator'); ?></p>
        <?php
    }

    public function enable_development_mode_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $enable_development_mode = isset($options['enable_development_mode']) ? $options['enable_development_mode'] : false;
        $development_mode_color = isset($options['development_mode_color']) ? $options['development_mode_color'] : '#7a39e8';
        ?>
        <div class="slmm-development-mode-wrapper">
            <input type="checkbox" id="enable_development_mode" name="chatgpt_generator_options[enable_development_mode]" value="1" <?php checked($enable_development_mode, true); ?>>
            <label for="enable_development_mode"><?php _e('Enable Development Mode', 'chatgpt-generator'); ?></label>
            <p class="description"><?php _e('Colorizes the WordPress admin bar to help distinguish between different sites in multiple tabs.', 'chatgpt-generator'); ?></p>
            
            <div class="slmm-color-picker-wrapper" style="margin-top: 15px;">
                <label for="development_mode_color"><?php _e('Admin Bar Color:', 'chatgpt-generator'); ?></label>
                <input type="text" id="development_mode_color" name="chatgpt_generator_options[development_mode_color]" value="<?php echo esc_attr($development_mode_color); ?>" class="slmm-color-picker" />
            </div>
        </div>
        <?php
    }



    public function page_delete_post_types_callback() {
        echo '<p class="description">' . __('Select which post types can be deleted using the quick delete feature.', 'chatgpt-generator') . '</p>';
        $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
        $post_types = get_post_types(array('public' => true), 'objects');
        
        foreach ($post_types as $post_type) {
            $checked = in_array($post_type->name, $allowed_types) ? 'checked' : '';
            echo "<label><input type='checkbox' name='page_delete_post_types[]' value='" . esc_attr($post_type->name) . "' $checked> " . esc_html($post_type->label) . "</label><br>";
        }
    }

    public function page_delete_rate_limit_callback() {
        $rate_limit = get_option('page_delete_rate_limit', 10);
        echo "<input type='number' id='page_delete_rate_limit' name='page_delete_rate_limit' value='" . esc_attr($rate_limit) . "' min='1' max='100'>";
    }

    public function visibility_enabled_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $visibility_enabled = isset($options['visibility_enabled']) ? $options['visibility_enabled'] : false;
        ?>
        <input type="checkbox" id="visibility_enabled" name="chatgpt_generator_options[visibility_enabled]" value="1" <?php checked($visibility_enabled, true); ?>>
        <label for="visibility_enabled"><?php _e('Hide plugin from all users except authorized admins', 'chatgpt-generator'); ?></label>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 10px 0;">
            <strong>⚠️ Important: Plugin Visibility Settings</strong>
            <p style="margin: 8px 0 0 0;">
                <strong>What this does:</strong> When enabled, this will hide the SLMM SEO menu and settings pages from users who are not in the authorized admin list below. 
                <br><br>
                <strong>Key Points:</strong>
                <br>• <strong>Hides UI only:</strong> The plugin functionality (SEO features, content generation, etc.) will continue to work for all users
                <br>• <strong>Settings access:</strong> Only authorized admins can access and modify plugin settings
                <br>• <strong>Purpose:</strong> Useful for hiding SLMM settings from other administrators while maintaining functionality site-wide
                <br>• <strong>Safety:</strong> Ensure you include your own username in the authorized list below!
                <br><br>
                <strong>Emergency Access:</strong> If you get locked out, add <code>?slmm_debug=access</code> to any admin URL to temporarily bypass the visibility restriction and regain access to the settings. This allows you to modify the authorized admin list if needed.
                <br><br>
                <strong>Emergency URL:</strong> <span id="emergency-url" style="background: #f0f0f1; padding: 4px 8px; border-radius: 3px; cursor: pointer; user-select: all; display: inline-block; margin: 5px 0;" onclick="copyEmergencyUrl()" title="Click to copy"><?php echo esc_url(admin_url('admin.php?page=chatgpt-generator-settings&slmm_debug=access')); ?></span>
                <small style="color: #666; margin-left: 8px; font-style: italic;">← Click to copy</small>
                <span id="copy-feedback" style="color: #46b450; margin-left: 10px; opacity: 0; transition: opacity 0.3s;">Copied!</span>
                <br><span style="color: #d63638; font-size: 12px;">⚠️ Important: Do not share this emergency URL with others as it bypasses security restrictions!</span>
                
                <script>
                function copyEmergencyUrl() {
                    const urlElement = document.getElementById('emergency-url');
                    const feedback = document.getElementById('copy-feedback');
                    
                    // Create a temporary textarea to copy the text
                    const tempTextArea = document.createElement('textarea');
                    tempTextArea.value = urlElement.textContent;
                    document.body.appendChild(tempTextArea);
                    tempTextArea.select();
                    
                    try {
                        document.execCommand('copy');
                        feedback.style.opacity = '1';
                        setTimeout(() => {
                            feedback.style.opacity = '0';
                        }, 2000);
                    } catch (err) {
                        console.error('Failed to copy URL:', err);
                    }
                    
                    document.body.removeChild(tempTextArea);
                }
                </script>
            </p>
        </div>
        
        <?php
    }

    public function authorized_admins_callback() {
        $options = get_option('chatgpt_generator_options', array());
        $authorized_admins = isset($options['authorized_admins']) ? $options['authorized_admins'] : array();
        $current_user = wp_get_current_user();
        
        // Ensure current user is included by default
        if (!in_array($current_user->user_login, $authorized_admins)) {
            $authorized_admins[] = $current_user->user_login;
        }
        ?>
        <div id="authorized-admins-container">
            <?php foreach ($authorized_admins as $index => $username): ?>
                <div class="authorized-admin-row" style="margin-bottom: 5px;">
                    <input type="text" name="chatgpt_generator_options[authorized_admins][]" value="<?php echo esc_attr($username); ?>" placeholder="Enter admin username" style="margin-right: 10px;">
                    <button type="button" class="button remove-admin-button">Remove</button>
                </div>
            <?php endforeach; ?>
        </div>
        <button type="button" id="add-admin-button" class="button button-secondary">Add Admin</button>
        <p class="description"><?php _e('Enter the usernames (not emails) of the admins who should have access to this plugin. Make sure to include your own username! At least one valid username is required.', 'chatgpt-generator'); ?></p>
        
        <script>
        jQuery(document).ready(function($) {
            $('#add-admin-button').on('click', function() {
                var newRow = '<div class="authorized-admin-row" style="margin-bottom: 5px;">' +
                           '<input type="text" name="chatgpt_generator_options[authorized_admins][]" value="" placeholder="Enter admin username" style="margin-right: 10px;">' +
                           '<button type="button" class="button remove-admin-button">Remove</button>' +
                           '</div>';
                $('#authorized-admins-container').append(newRow);
            });
            
            $(document).on('click', '.remove-admin-button', function() {
                $(this).parent().remove();
            });
        });
        </script>
        <?php
    }

    private function log_deletion_attempt($post_id, $success, $error_message = '') {
        if (!file_exists(WP_CONTENT_DIR . '/deletion-logs')) {
            wp_mkdir_p(WP_CONTENT_DIR . '/deletion-logs');
        }

        $log_file = WP_CONTENT_DIR . '/deletion-logs/deletion-log-' . date('Y-m') . '.log';
        $user = wp_get_current_user();
        $post = get_post($post_id);
        $log_entry = sprintf(
            "[%s] User: %s (ID: %d) | Post: %s (ID: %d) | Type: %s | Status: %s | %s\n",
            current_time('mysql'),
            $user->user_login,
            $user->ID,
            $post ? $post->post_title : 'Unknown',
            $post_id,
            $post ? $post->post_type : 'Unknown',
            $success ? 'Success' : 'Failed',
            $error_message
        );

        error_log($log_entry, 3, $log_file);
    }

    private function check_rate_limit($user_id) {
        global $wpdb;
        $rate_limit = get_option('page_delete_rate_limit', 10);
        $one_hour_ago = date('Y-m-d H:i:s', strtotime('-1 hour'));
        
        // Get deletion count in the last hour from logs
        $log_file = WP_CONTENT_DIR . '/deletion-logs/deletion-log-' . date('Y-m') . '.log';
        if (!file_exists($log_file)) {
            return true;
        }

        $log_content = file_get_contents($log_file);
        $pattern = "/\[([^\]]+)\] User: [^\(]+ \(ID: " . $user_id . "\)/";
        preg_match_all($pattern, $log_content, $matches, PREG_SET_ORDER);

        $count = 0;
        foreach ($matches as $match) {
            $log_time = strtotime($match[1]);
            if ($log_time > strtotime('-1 hour')) {
                $count++;
            }
        }

        return $count < $rate_limit;
    }

    private function empty_trash_for_post_type($post_type) {
        global $wpdb;

        try {
            // Use a transaction for data integrity
            $wpdb->query('START TRANSACTION');

            // First, get all trashed posts of this type
            $trashed_posts = $wpdb->get_col($wpdb->prepare("
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type = %s 
                AND post_status = 'trash'
            ", $post_type));

            if (empty($trashed_posts)) {
                $wpdb->query('COMMIT');
                return true;
            }

            // Delete each post properly using WordPress functions
            foreach ($trashed_posts as $post_id) {
                // Delete attachments first
                $attachments = $wpdb->get_col($wpdb->prepare("
                    SELECT ID FROM {$wpdb->posts} 
                    WHERE post_type = 'attachment' 
                    AND post_parent = %d
                ", $post_id));

                foreach ($attachments as $attachment_id) {
                    wp_delete_attachment($attachment_id, true);
                }

                // Force delete the post (skip trash)
                wp_delete_post($post_id, true);
            }

            // Clean up orphaned metadata
            $wpdb->query("
                DELETE pm
                FROM {$wpdb->postmeta} pm
                LEFT JOIN {$wpdb->posts} posts ON pm.post_id = posts.ID
                WHERE posts.ID IS NULL
            ");

            // Clean up orphaned relationships
            $wpdb->query("
                DELETE tr
                FROM {$wpdb->term_relationships} tr
                LEFT JOIN {$wpdb->posts} posts ON tr.object_id = posts.ID
                WHERE posts.ID IS NULL
            ");

            $wpdb->query('COMMIT');
            wp_cache_flush();
            return true;
        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            throw $e;
        }
    }

    public function init_page_delete() {
        // Early exit if feature is not enabled or user is not logged in
        if (!get_option('enable_page_delete', false) || !is_user_logged_in()) {
            return;
        }

        // Add Empty Trash button to admin bar
        add_action('admin_bar_menu', function ($admin_bar) {
            // Only show buttons to authorized users
            if (!current_user_can('delete_posts')) {
                return;
            }

            // Get current post type
            $post_type = null;
            
            if (is_singular()) {
                $post_id = get_the_ID();
                $post_type = get_post_type($post_id);
            } elseif (is_admin()) {
                // In admin area, get post type from screen
                $screen = get_current_screen();
                if ($screen && in_array($screen->base, ['edit', 'post'])) {
                    $post_type = $screen->post_type;
                }
            }

            // If we have a valid post type and it's allowed
            if ($post_type) {
                $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
                if (in_array($post_type, $allowed_types)) {
                    $auto_empty_trash = get_option('auto_empty_trash', false);
                    
                    // Add Empty Trash button if enabled and not on trash view
                    if ($auto_empty_trash && (!isset($_GET['post_status']) || $_GET['post_status'] !== 'trash')) {
                        $empty_nonce = wp_create_nonce('empty-trash-' . $post_type);
                        $admin_bar->add_node([
                            'id'    => 'empty-trash',
                            'title' => __('Empty Trash', 'chatgpt-generator'),
                            'href'  => esc_url(add_query_arg(array(
                                'action' => 'empty-trash',
                                'post_type' => $post_type,
                                '_wpnonce' => $empty_nonce,
                            ), admin_url('admin-post.php'))),
                            'meta'  => [
                                'title' => esc_attr(__('Empty Trash', 'chatgpt-generator')),
                                'target' => '_self',
                                'onclick' => 'return confirm("' . esc_js(__('Are you sure you want to permanently delete all trashed items of this type?', 'chatgpt-generator')) . '");'
                            ],
                        ]);
                    }

                    // Only add Send to Trash button on singular pages
                    if (is_singular()) {
                        $post_id = get_the_ID();
                        if (get_post_status($post_id) !== 'trash' && current_user_can('delete_post', $post_id)) {
                            $nonce = wp_create_nonce('trash-post_' . $post_id);
                            $admin_bar->add_node([
                                'id'    => 'send-to-trash',
                                'title' => __('Send to Trash', 'chatgpt-generator'),
                                'href'  => esc_url(add_query_arg(array(
                                    'post' => $post_id,
                                    'action' => 'trash',
                                    '_wpnonce' => $nonce,
                                    'confirm' => '1'
                                ), admin_url('post.php'))),
                                'meta'  => [
                                    'title' => esc_attr(__('Send to Trash', 'chatgpt-generator')),
                                    'target' => '_self',
                                    'onclick' => 'return confirm("' . esc_js(__('Are you sure you want to move this item to trash?', 'chatgpt-generator')) . '");'
                                ],
                            ]);
                        }
                    }
                }
            }
        }, 100);

        // Add Empty Trash button to list view
        add_action('restrict_manage_posts', function() {
            $screen = get_current_screen();
            if (!$screen) return;

            // Don't show the button on trash view
            if (isset($_GET['post_status']) && $_GET['post_status'] === 'trash') {
                return;
            }

            $post_type = $screen->post_type;
            $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
            $auto_empty_trash = get_option('auto_empty_trash', false);

            if (in_array($post_type, $allowed_types) && $auto_empty_trash && current_user_can('delete_posts')) {
                $empty_nonce = wp_create_nonce('empty-trash-' . $post_type);
                $url = add_query_arg(array(
                    'action' => 'empty-trash',
                    'post_type' => $post_type,
                    '_wpnonce' => $empty_nonce,
                ), admin_url('admin-post.php'));
                
                echo '<a href="' . esc_url($url) . '" class="button" onclick="return confirm(\'' . 
                    esc_js(__('Are you sure you want to permanently delete all trashed items of this type?', 'chatgpt-generator')) . 
                    '\');">' . esc_html__('Empty Trash', 'chatgpt-generator') . '</a>';
            }
        });

        // Handle Empty Trash action
        add_action('admin_post_empty-trash', function() {
            if (!isset($_GET['post_type'], $_GET['_wpnonce'])) {
                wp_die(__('Invalid request.', 'chatgpt-generator'));
            }

            $post_type = sanitize_text_field($_GET['post_type']);
            $nonce = sanitize_text_field($_GET['_wpnonce']);

            if (!wp_verify_nonce($nonce, 'empty-trash-' . $post_type)) {
                wp_die(__('Security check failed.', 'chatgpt-generator'));
            }

            if (!current_user_can('delete_posts')) {
                wp_die(__('You do not have permission to empty trash.', 'chatgpt-generator'));
            }

            try {
                $this->empty_trash_for_post_type($post_type);
                $redirect_url = admin_url('edit.php' . ($post_type !== 'post' ? '?post_type=' . $post_type : ''));
                wp_safe_redirect(add_query_arg('trashed', 'emptied', $redirect_url));
                exit;
            } catch (Exception $e) {
                wp_die(__('Error emptying trash: ', 'chatgpt-generator') . esc_html($e->getMessage()));
            }
        });

        // Handle regular trash action
        add_action('template_redirect', function () {
            // Check if we're on a trashed confirmation page
            if (isset($_GET['trashed']) && isset($_GET['ids'])) {
                $post_id = absint($_GET['ids']);
                $post_type = get_post_type($post_id);
                if (!$post_type) {
                    $post_type = 'post'; // fallback to post if type can't be determined
                }
                // Redirect to the post type list view
                $redirect_url = admin_url('edit.php' . ($post_type !== 'post' ? '?post_type=' . $post_type : ''));
                wp_safe_redirect($redirect_url);
                exit;
            }

            // Only process on singular pages and for logged-in users
            if (!is_singular() || !is_user_logged_in()) {
                return;
            }

            // Verify all required parameters are present
            if (!isset($_GET['action'], $_GET['post'], $_GET['_wpnonce'], $_GET['confirm'])) {
                return;
            }

            $post_id = absint($_GET['post']);
            $nonce = sanitize_text_field($_GET['_wpnonce']);
            $action = sanitize_text_field($_GET['action']);
            $user_id = get_current_user_id();
            
            // Check rate limit
            if (!$this->check_rate_limit($user_id)) {
                $this->log_deletion_attempt($post_id, false, 'Rate limit exceeded');
                wp_die(__('Rate limit exceeded. Please try again later.', 'chatgpt-generator'));
            }

            // Verify post exists and is not already in trash
            if (!$post_id || !get_post($post_id) || get_post_status($post_id) === 'trash') {
                $this->log_deletion_attempt($post_id, false, 'Invalid post');
                wp_die(__('Invalid post.', 'chatgpt-generator'));
            }

            // Check post type restrictions
            $post_type = get_post_type($post_id);
            $allowed_types = get_option('page_delete_post_types', array('post', 'page'));
            if (!in_array($post_type, $allowed_types)) {
                $this->log_deletion_attempt($post_id, false, 'Post type not allowed');
                wp_die(__('This post type cannot be deleted using quick delete.', 'chatgpt-generator'));
            }

            if (current_user_can('delete_post', $post_id) && wp_verify_nonce($nonce, 'trash-post_' . $post_id)) {
                try {
                    // Move to trash
                    $trashed = wp_trash_post($post_id);
                    if (!$trashed) {
                        throw new Exception('Failed to move post to trash');
                    }

                    $this->log_deletion_attempt($post_id, true, 'Moved to trash');

                    // Always redirect to the post type list view
                    $redirect_url = admin_url('edit.php' . ($post_type !== 'post' ? '?post_type=' . $post_type : ''));
                    wp_safe_redirect($redirect_url);
                    exit;
                } catch (Exception $e) {
                    $this->log_deletion_attempt($post_id, false, $e->getMessage());
                    wp_die(__('Error: ', 'chatgpt-generator') . esc_html($e->getMessage()));
                }
            } else {
                $this->log_deletion_attempt($post_id, false, 'Permission denied or invalid nonce');
                wp_die(__('You are not allowed to perform this action.', 'chatgpt-generator'));
            }
        });

        // Add success message for emptied trash
        add_action('admin_notices', function() {
            if (isset($_GET['trashed']) && $_GET['trashed'] === 'emptied') {
                ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php _e('Trash has been emptied successfully.', 'chatgpt-generator'); ?></p>
                </div>
                <?php
            }
        });
    }

    public function export_settings() {
        $all_settings = array(
            'chatgpt_generator_options' => get_option('chatgpt_generator_options'),
            'slmm_gpt_prompts' => get_option('slmm_gpt_prompts')
        );

        // Remove API keys
        unset($all_settings['chatgpt_generator_options']['openai_api_key']);

        return json_encode($all_settings);
    }

    public function import_settings($json_data) {
        $imported_settings = json_decode($json_data, true);

        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($imported_settings['chatgpt_generator_options'])) {
                $current_options = get_option('chatgpt_generator_options', array());
                $merged_options = array_merge($current_options, $imported_settings['chatgpt_generator_options']);
                update_option('chatgpt_generator_options', $merged_options);
            }
            if (isset($imported_settings['slmm_gpt_prompts'])) {
                update_option('slmm_gpt_prompts', $imported_settings['slmm_gpt_prompts']);
            }
            return true;
        }
        return false;
    }

    public function handle_export_import() {
        if (!current_user_can('manage_options')) {
            return;
        }

        if (isset($_POST['export_settings']) && check_admin_referer('slmm_export_import_settings', 'slmm_export_import_nonce')) {
            $export_data = array();

            if (isset($_POST['export_chatgpt_generator'])) {
                $export_data['chatgpt_generator_options'] = get_option('chatgpt_generator_options');
                // Remove API keys
                unset($export_data['chatgpt_generator_options']['openai_api_key']);
                
                // Remove protected words if not explicitly requested
                if (!isset($_POST['export_protected_words'])) {
                    unset($export_data['chatgpt_generator_options']['protected_words_list']);
                }
            }

            if (isset($_POST['export_slmm_gpt_prompts'])) {
                $export_data['slmm_gpt_prompts'] = get_option('slmm_gpt_prompts');
            }

            $json_data = json_encode($export_data);
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename=slmm_seo_settings_export.json');
            echo $json_data;
            exit;
        }

        if (isset($_POST['import_settings']) && check_admin_referer('slmm_export_import_settings', 'slmm_export_import_nonce')) {
            if (isset($_FILES['import_file']) && $_FILES['import_file']['error'] == 0) {
                $json_data = file_get_contents($_FILES['import_file']['tmp_name']);
                $import_result = $this->import_settings($json_data);

                if ($import_result) {
                    add_settings_error('slmm_seo_settings', 'settings_updated', 'Settings imported successfully.', 'updated');
                } else {
                    add_settings_error('slmm_seo_settings', 'import_error', 'Error importing settings. Please check the file format.', 'error');
                }
            } else {
                add_settings_error('slmm_seo_settings', 'import_error', 'Error uploading file. Please try again.', 'error');
            }
        }
    }

    public function render_settings_page() {
        $this->enqueue_settings_assets();
        ?>
        <div class="wrap slmm-settings-wrap">
            <div class="slmm-settings-header">
                <h1 class="slmm-settings-title">
                    <span class="slmm-logo">⚡</span>
                    <?php echo esc_html(get_admin_page_title()); ?>
                </h1>
                <p class="slmm-settings-subtitle">Configure your SLMM SEO Bundle settings</p>
            </div>
            
            <?php settings_errors(); ?>

            <div class="slmm-settings-container">
                <div class="slmm-tabs-wrapper">
                    <nav class="slmm-tabs-nav">
                        <button class="slmm-tab-button active" data-tab="api-keys">
                            <span class="slmm-tab-icon"></span>
                            API Keys
                        </button>
                        <button class="slmm-tab-button" data-tab="prompts">
                            <span class="slmm-tab-icon"></span>
                            Prompts
                        </button>
                        <button class="slmm-tab-button" data-tab="business-info">
                            <span class="slmm-tab-icon"></span>
                            Business Info
                        </button>
                        <button class="slmm-tab-button" data-tab="models">
                            <span class="slmm-tab-icon"></span>
                            AI Models
                        </button>
                        <button class="slmm-tab-button" data-tab="protected-words">
                            <span class="slmm-tab-icon"></span>
                            Protected Words
                        </button>
                        <button class="slmm-tab-button" data-tab="search-replace">
                            <span class="slmm-tab-icon"></span>
                            Search & Replace
                        </button>
                        <button class="slmm-tab-button" data-tab="features">
                            <span class="slmm-tab-icon"></span>
                            Features
                        </button>
                        <button class="slmm-tab-button" data-tab="lorem-detector">
                            <span class="slmm-tab-icon"></span>
                            Lorem Ipsum Detector
                        </button>
                        <button class="slmm-tab-button" data-tab="export-import">
                            <span class="slmm-tab-icon"></span>
                            Export/Import
                        </button>
                    </nav>

                    <div class="slmm-tabs-content">
                        <form action="options.php" method="post" class="slmm-settings-form">
                            <?php settings_fields('chatgpt_generator_settings'); ?>
                            
                            <!-- API Keys Tab -->
                            <div class="slmm-tab-pane active" id="api-keys">
                                <div class="slmm-tab-header">
                                    <h2>API Keys</h2>
                                    <p>Enter your API keys for AI services</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('api_keys'); ?>
                                </div>
                            </div>

                            <!-- Prompts Tab -->
                            <div class="slmm-tab-pane" id="prompts">
                                <div class="slmm-tab-header">
                                    <h2>Prompts</h2>
                                    <p>Configure your prompts for different content types</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('prompts'); ?>
                                </div>
                            </div>

                            <!-- Business Info Tab -->
                            <div class="slmm-tab-pane" id="business-info">
                                <div class="slmm-tab-header">
                                    <h2>Business Information</h2>
                                    <p>Enter your business details</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('business_info'); ?>
                                </div>
                            </div>

                            <!-- AI Models Tab -->
                            <div class="slmm-tab-pane" id="models">
                                <div class="slmm-tab-header">
                                    <h2>AI Models</h2>
                                    <p>Select AI models for different content types</p>
                                </div>
                                
                                <!-- Selected Models Section -->
                                <?php $this->render_selected_models_section(); ?>
                                
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('models'); ?>
                                </div>
                            </div>

                            <!-- Protected Words Tab -->
                            <div class="slmm-tab-pane" id="protected-words">
                                <div class="slmm-tab-header">
                                    <h2>Protected Words</h2>
                                    <p>Words that should always maintain their capitalization</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('protected_words'); ?>
                                </div>
                            </div>

                            <!-- Features Tab -->
                            <div class="slmm-tab-pane" id="features">
                                <div class="slmm-tab-header">
                                    <h2>Plugin Features</h2>
                                    <p>Enable or disable specific plugin features</p>
                                </div>
                                <div class="slmm-form-grid">
                                    <?php $this->render_tab_fields('features'); ?>
                                    <?php $this->render_feature_callbacks(); ?>
                                </div>
                            </div>

                            <!-- Search & Replace Tab -->
                            <div class="slmm-tab-pane" id="search-replace">
                                <div class="slmm-tab-header">
                                    <h2>Search & Replace</h2>
                                    <p>Perform database-wide search and replace operations with safety checks</p>
                                </div>
                                <div class="slmm-search-replace-content">
                                    <div class="slmm-search-replace-info">
                                        <h3>Database Search & Replace</h3>
                                        <p>Search and replace text across your entire WordPress database. This tool handles serialized data properly and includes safety features.</p>
                                        <p><strong>Warning:</strong> Always backup your database before performing search & replace operations.</p>
                                    </div>
                                    
                                    <div id="slmm-search-replace-form-placeholder">
                                        <!-- Search & Replace form will be rendered outside the main form -->
                                    </div>
                                </div>
                            </div>

                            <!-- Lorem Ipsum Detector Tab -->
                            <div class="slmm-tab-pane" id="lorem-detector">
                                <div class="slmm-tab-header">
                                    <h2>Lorem Ipsum Detector</h2>
                                    <p>Scan your website for Lorem Ipsum placeholder text</p>
                                </div>
                                <div class="slmm-lorem-detector-content">
                                    <div class="slmm-lorem-scanner">
                                        <div class="slmm-lorem-info">
                                            <h3>What does this do?</h3>
                                            <p>This tool scans all your published posts, drafts, and private content to find Lorem Ipsum placeholder text that should be replaced with real content.</p>
                                            <p><strong>Keywords detected:</strong> lorem, ipsum, consectetur, adipiscing, eiusmod, incididunt</p>
                                        </div>
                                        
                                        <div class="slmm-lorem-controls">
                                            <button type="button" id="slmm-start-lorem-scan" class="button button-primary slmm-lorem-scan-btn">
                                                <span class="slmm-scan-text">Start Scan</span>
                                                <span class="slmm-scan-loading" style="display: none;">
                                                    <span class="slmm-spinner"></span>
                                                    Scanning...
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div id="slmm-lorem-results" class="slmm-lorem-results-container">
                                        <!-- Results will be loaded here via AJAX -->
                                    </div>
                                </div>
                            </div>

                            <div class="slmm-form-actions">
                                <?php submit_button('Save Settings', 'primary slmm-save-button', 'submit', false, array('id' => 'slmm-save-settings')); ?>
                            </div>
                        </form>

                        <!-- Search & Replace Form (Outside Main Form) -->
                        <div class="slmm-search-replace-form-container" style="display: none;">
                            <form id="slmm-search-replace-form" class="slmm-search-replace-form" method="POST" action="#">
                                <?php wp_nonce_field('slmm_search_replace', 'slmm_search_replace_nonce'); ?>
                                
                                <div class="slmm-form-row">
                                    <div class="slmm-form-group">
                                        <label for="search_text">Search for:</label>
                                        <input type="text" id="search_text" name="search_text" class="slmm-form-input" placeholder="Enter text to search for..." required>
                                    </div>
                                    
                                    <div class="slmm-form-group">
                                        <label for="replace_text">Replace with:</label>
                                        <input type="text" id="replace_text" name="replace_text" class="slmm-form-input" placeholder="Enter replacement text...">
                                    </div>
                                </div>
                                
                                <div class="slmm-form-row">
                                    <div class="slmm-checkbox-group">
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" id="case_insensitive" name="case_insensitive">
                                            <span class="slmm-checkmark"></span>
                                            Case insensitive search
                                        </label>
                                        
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" id="dry_run" name="dry_run" checked>
                                            <span class="slmm-checkmark"></span>
                                            Dry run (preview changes only)
                                        </label>
                                        
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" id="whole_words" name="whole_words">
                                            <span class="slmm-checkmark"></span>
                                            Match whole words only
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="slmm-form-row">
                                    <div class="slmm-form-group">
                                        <label>Select Tables to Search:</label>
                                        <div class="slmm-table-selection">
                                            <button type="button" id="select-all-tables" class="button button-secondary">Select All</button>
                                            <button type="button" id="select-core-tables" class="button button-secondary">Core Tables Only</button>
                                            <button type="button" id="deselect-all-tables" class="button button-secondary">Deselect All</button>
                                            <div id="table-list" class="slmm-table-list">
                                                <!-- Table list will be populated via AJAX -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="slmm-search-replace-actions">
                                    <button type="button" id="slmm-start-search-replace" class="button button-primary slmm-search-replace-btn">
                                        <span class="slmm-search-text">Start Search & Replace</span>
                                        <span class="slmm-search-loading" style="display: none;">
                                            <span class="slmm-spinner"></span>
                                            Processing...
                                        </span>
                                    </button>
                                </div>
                            </form>
                            
                            <div id="slmm-search-replace-results" class="slmm-search-replace-results-container">
                                <!-- Results will be loaded here via AJAX -->
                            </div>
                        </div>

                        <!-- Export/Import Tab -->
                        <div class="slmm-tab-pane" id="export-import">
                            <div class="slmm-tab-header">
                                <h2>Export/Import Settings</h2>
                                <p>Backup and restore your settings</p>
                            </div>
                            
                            <form method="post" enctype="multipart/form-data" class="slmm-export-import-form">
                                <?php wp_nonce_field('slmm_export_import_settings', 'slmm_export_import_nonce'); ?>
                                
                                <div class="slmm-export-section">
                                    <h3 class="slmm-section-title">Export Settings</h3>
                                    <div class="slmm-checkbox-group">
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" name="export_chatgpt_generator" checked>
                                            <span class="slmm-checkmark"></span>
                                            Export ChatGPT Generator Settings
                                        </label>
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" name="export_slmm_gpt_prompts" checked>
                                            <span class="slmm-checkmark"></span>
                                            Export SLMM GPT Prompts
                                        </label>
                                        <label class="slmm-checkbox-label">
                                            <input type="checkbox" name="export_protected_words" checked>
                                            <span class="slmm-checkmark"></span>
                                            Export Protected Words List
                                        </label>
                                    </div>
                                    <?php submit_button('Export Settings', 'secondary slmm-export-button', 'export_settings', false); ?>
                                </div>

                                <div class="slmm-import-section">
                                    <h3 class="slmm-section-title">Import Settings</h3>
                                    <div class="slmm-file-upload">
                                        <input type="file" name="import_file" accept=".json" id="slmm-import-file">
                                        <label for="slmm-import-file" class="slmm-file-label">
                                            <span class="slmm-file-icon">📁</span>
                                            Choose settings file...
                                        </label>
                                    </div>
                                    <?php submit_button('Import Settings', 'secondary slmm-import-button', 'import_settings', false); ?>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    private function render_tab_fields($section) {
        $fields = array(
            'api_keys' => array(
                'openai_api_key' => 'OpenAI API Key',
                'openrouter_api_key' => 'OpenRouter API Key',
            ),
            'prompts' => array(
                'title_prompt' => 'Title Prompt',
                'description_prompt' => 'Description Prompt',
            ),
            'business_info' => array(
                'business_name' => 'Business Name',
                'phone_number' => 'Phone Number',
            ),
            'models' => array(
                'model_for_title' => 'Title Generation Model',
                'model_for_description' => 'Description Generation Model',
            ),
            'protected_words' => array(
                'protected_words_list' => 'Protected Words List',
            ),
            'features' => array(
                // Features are now handled in render_feature_callbacks with proper grouping
            ),
        );

        if (!isset($fields[$section])) return;

        foreach ($fields[$section] as $field => $label) {
            echo '<div class="slmm-form-field">';
            $this->render_field(array('field' => $field, 'label' => $label));
            echo '</div>';
        }
    }

    private function render_selected_models_section() {
        $options = get_option('chatgpt_generator_options', array());
        $title_model = isset($options['model_for_title']) ? $options['model_for_title'] : 'gpt-4o';
        $title_provider = isset($options['model_for_title_provider']) ? $options['model_for_title_provider'] : 'openai';
        $description_model = isset($options['model_for_description']) ? $options['model_for_description'] : 'gpt-4o';
        $description_provider = isset($options['model_for_description_provider']) ? $options['model_for_description_provider'] : 'openai';
        
        // Format model names for display
        $formatted_title = $this->format_model_name($title_model);
        $formatted_description = $this->format_model_name($description_model);
        
        echo '<div class="slmm-selected-models">';
        echo '<h4>Selected Models</h4>';
        
        echo '<div class="slmm-selected-model-item">';
        echo '<span class="slmm-selected-model-label">Title Generation:</span>';
        echo '<span class="slmm-selected-model-value">' . esc_html($formatted_title) . ' (' . esc_html(ucfirst($title_provider)) . ')</span>';
        echo '</div>';
        
        echo '<div class="slmm-selected-model-item">';
        echo '<span class="slmm-selected-model-label">Description Generation:</span>';
        echo '<span class="slmm-selected-model-value">' . esc_html($formatted_description) . ' (' . esc_html(ucfirst($description_provider)) . ')</span>';
        echo '</div>';
        
        echo '</div>';
    }

    private function render_feature_callbacks() {
        // SEO Features Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">SEO Features</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        $this->render_field(array('field' => 'enable_checklist', 'label' => 'Enable SEO Checklist'));
        echo '</div>';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        $this->render_field(array('field' => 'enable_seo_overview', 'label' => 'Enable SEO Overview Meta Box'));
        echo '</div>';
        
        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">SEO Overview Post Types</label>';
        $this->seo_overview_post_types_callback();
        echo '</div>';
        
        echo '</div></div>';

        // Content Management Features Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">Content Management</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Quick Delete Features</label>';
        $this->enable_page_delete_callback();
        echo '</div>';

        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">Allowed Post Types for Delete</label>';
        $this->page_delete_post_types_callback();
        echo '</div>';

        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">Delete Rate Limit</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('Maximum number of deletions allowed per hour per user.', 'chatgpt-generator') . '</p>';
        $this->page_delete_rate_limit_callback();
        echo '</div>';
        
        echo '</div></div>';

        // User Experience Features Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">User Experience</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Keyboard Shortcuts</label>';
        $this->enable_keyboard_shortcuts_callback();
        echo '</div>';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Project Notes</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('Add a notes feature to the admin bar for taking project notes across all admin pages.', 'chatgpt-generator') . '</p>';
        $this->enable_notes_callback();
        echo '</div>';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label">Development Mode</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('Colorizes the WordPress admin bar to help distinguish between different sites in multiple tabs.', 'chatgpt-generator') . '</p>';
        $this->enable_development_mode_callback();
        echo '</div>';
        
        echo '</div></div>';

        // Security & Access Control Group
        echo '<div class="slmm-feature-group">';
        echo '<h3 class="slmm-feature-group-title">Security & Access Control</h3>';
        echo '<div class="slmm-feature-group-content">';
        
        echo '<div class="slmm-form-field slmm-checkbox-field">';
        echo '<label class="slmm-field-label slmm-visibility-label">Plugin Visibility Settings</label>';
        echo '<p class="description" style="margin-top: 10px; margin-bottom: 15px;">' . __('When enabled, only the specified admin users below will see the plugin in the WordPress admin menu. At least one valid username is required.', 'chatgpt-generator') . '</p>';
        $this->visibility_enabled_callback();
        echo '</div>';

        echo '<div class="slmm-form-field">';
        echo '<label class="slmm-field-label">Authorized Admin Usernames</label>';
        $this->authorized_admins_callback();
        echo '</div>';
        
        echo '</div></div>';
    }

    private function enqueue_settings_assets() {
        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        
        // Enqueue the custom CSS and JS for the settings page
        wp_enqueue_style('slmm-settings-style', plugin_dir_url(__FILE__) . '../../assets/css/slmm-settings.css', array('wp-color-picker'), '1.0.0');
        wp_enqueue_script('slmm-settings-script', plugin_dir_url(__FILE__) . '../../assets/js/slmm-settings.js', array('jquery', 'wp-color-picker'), '1.0.0', true);
        
        // Enqueue Lorem Ipsum Detector assets
        wp_enqueue_style('slmm-lorem-detector-style', plugin_dir_url(__FILE__) . '../../assets/css/lorem-detector.css', array(), '1.0.0');
        wp_enqueue_script('slmm-lorem-detector-script', plugin_dir_url(__FILE__) . '../../assets/js/lorem-detector.js', array('jquery'), '1.0.0', true);
        
        // Localize script for any AJAX calls
        wp_localize_script('slmm-settings-script', 'slmmSettings', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_settings')
        ));
        
        // Localize Lorem Ipsum Detector script
        wp_localize_script('slmm-lorem-detector-script', 'slmmLoremDetector', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_lorem_ipsum_scan')
        ));
    }

    public function chatgpt_generator_buttons() {
        ?>
        <div id="chatgpt-title-generator-button" style="margin-bottom: 10px;">
            <button type="button" class="button button-primary generate">Generate Title</button>
            <button type="button" class="button button-secondary auto-populate-titles">Auto-Populate Titles</button>
        </div>
        <div id="chatgpt-description-generator-button" style="margin-bottom: 10px;">
            <button type="button" class="button button-primary generate">Generate Description</button>
            <button type="button" class="button button-secondary auto-populate-descriptions">Auto-Populate Descriptions</button>
            <button type="button" class="button button-secondary prefill-from-clipboard">Prefill From Clipboard</button>
        </div>
        <?php
    }

    public function enqueue_keyboard_shortcuts($hook) {
        // Early return if the setting is not enabled
        if (get_option('enable_keyboard_shortcuts') !== '1') {
            return;
        }
        
        // Register the script
        wp_register_script('slmm-keyboard-shortcuts', 
            plugins_url('/js/slmm-keyboard-shortcuts.js', dirname(dirname(__FILE__))), 
            array('jquery'), 
            SLMM_SEO_VERSION
        );

        // Localize the script with translation data
        $translation_array = array(
            'enabled' => '1' // Pass as string '1' to match the strict comparison in JavaScript
        );
        wp_localize_script('slmm-keyboard-shortcuts', 'SaveWithKeyboard', $translation_array);

        // Enqueue the script
        wp_enqueue_script('slmm-keyboard-shortcuts');
    }

    public function handle_keyboard_shortcut_save() {
        check_ajax_referer('keyboard_shortcut_save', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $post_id = intval($_POST['post_id']);
        $result = wp_update_post(array('ID' => $post_id));
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('Post saved successfully');
        }
    }

    public function handle_refresh_openai_models() {
        check_ajax_referer('refresh_openai_models', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Clear the cached models to force a fresh API call
        delete_transient('slmm_openai_models');
        
        // Fetch fresh models
        $models = $this->get_openai_models();
        
        wp_send_json_success('Models refreshed successfully');
    }

    /**
     * Migrate old model settings to new format
     */
    public function migrate_old_settings() {
        // Check if migration has already been done
        if (get_option('slmm_seo_models_migrated', false)) {
            return;
        }

        $options = get_option('chatgpt_generator_options', array());
        $needs_update = false;

        // Migrate old chatgpt_title_model to model_for_title
        $old_title_model = get_option('chatgpt_title_model');
        if ($old_title_model && !isset($options['model_for_title'])) {
            $options['model_for_title'] = $old_title_model;
            $needs_update = true;
        }

        // Migrate old chatgpt_description_model to model_for_description
        $old_description_model = get_option('chatgpt_description_model');
        if ($old_description_model && !isset($options['model_for_description'])) {
            $options['model_for_description'] = $old_description_model;
            $needs_update = true;
        }

        // Migrate other old settings
        $old_mappings = array(
            'chatgpt_title_prompt' => 'title_prompt',
            'chatgpt_description_prompt' => 'description_prompt',
            'chatgpt_business_name' => 'business_name',
            'chatgpt_phone_number' => 'phone_number'
        );

        foreach ($old_mappings as $old_key => $new_key) {
            $old_value = get_option($old_key);
            if ($old_value && !isset($options[$new_key])) {
                $options[$new_key] = $old_value;
                $needs_update = true;
            }
        }

        // Update the options if needed
        if ($needs_update) {
            update_option('chatgpt_generator_options', $options);
        }

        // Mark migration as complete
        update_option('slmm_seo_models_migrated', true);
    }

    /**
     * Handle AJAX request to get models for a specific provider
     */
    public function handle_get_provider_models() {
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        $field = isset($_POST['field']) ? sanitize_text_field($_POST['field']) : '';
        
        $models = array();
        
        if ($provider === 'openrouter') {
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if ($openrouter->is_configured()) {
                $models = $openrouter->get_models();
            } else {
                wp_send_json_error('OpenRouter API key not configured');
            }
        } else {
            $models = $this->get_openai_models();
        }
        
        wp_send_json_success(array(
            'models' => $models,
            'provider' => $provider,
            'field' => $field
        ));
    }

    /**
     * Handle AJAX request to refresh models
     */
    public function handle_refresh_models() {
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        $field = isset($_POST['field']) ? sanitize_text_field($_POST['field']) : '';
        
        $models = array();
        
        if ($provider === 'openrouter') {
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if ($openrouter->is_configured()) {
                // Clear cache first
                $openrouter->clear_model_cache();
                $models = $openrouter->get_models();
            } else {
                wp_send_json_error('OpenRouter API key not configured');
            }
        } else {
            // Clear OpenAI cache first
            delete_transient('slmm_openai_models');
            $models = $this->get_openai_models();
        }
        
        wp_send_json_success(array(
            'models' => $models,
            'provider' => $provider,
            'field' => $field
        ));
    }

    /**
     * Handle AJAX request to clear model cache
     */
    public function handle_clear_model_cache() {
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Clear OpenAI cache
        delete_transient('slmm_openai_models');
        
        // Clear OpenRouter cache
        require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        $openrouter->clear_model_cache();
        
        wp_send_json_success('Model cache cleared');
    }

    /**
     * Handle AJAX request to save model selection
     */
    public function handle_save_model_selection() {
        // Log the incoming request for debugging
        error_log('SLMM Save Model Selection - POST data: ' . print_r($_POST, true));
        
        check_ajax_referer('slmm_model_selector', 'nonce');
        
        if (!current_user_can('manage_options')) {
            error_log('SLMM Save Model Selection - Permission denied');
            wp_send_json_error('Insufficient permissions');
        }
        
        // Safely get POST data with null checks
        $field = isset($_POST['field']) ? sanitize_text_field($_POST['field']) : '';
        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';
        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        
        error_log("SLMM Save Model Selection - Parsed data: field=$field, model=$model, provider=$provider");
        
        // Validate required fields
        if (empty($field) || empty($model)) {
            error_log('SLMM Save Model Selection - Missing required fields');
            wp_send_json_error('Missing required fields: field and model are required');
        }
        
        // Get current options
        $options = get_option('chatgpt_generator_options', array());
        error_log('SLMM Save Model Selection - Current options: ' . print_r($options, true));
        
        // Save model selection
        $options[$field] = $model;
        
        // Save provider selection
        $provider_field = $field . '_provider';
        $options[$provider_field] = $provider;
        
        error_log('SLMM Save Model Selection - Updated options: ' . print_r($options, true));
        
        // Update options
        $updated = update_option('chatgpt_generator_options', $options);
        error_log("SLMM Save Model Selection - Update result: " . ($updated ? 'true' : 'false'));
        
        // Verify the save worked
        $saved_options = get_option('chatgpt_generator_options', array());
        error_log('SLMM Save Model Selection - Saved options verification: ' . print_r($saved_options, true));
        
        if (isset($saved_options[$field]) && $saved_options[$field] === $model) {
            error_log('SLMM Save Model Selection - Success');
            wp_send_json_success(array(
                'message' => 'Model selection saved',
                'field' => $field,
                'model' => $model,
                'provider' => $provider
            ));
        } else {
            error_log('SLMM Save Model Selection - Verification failed');
            wp_send_json_error('Failed to save model selection - verification failed');
        }
    }

    /**
     * Handle AJAX request to get database tables
     */
    public function handle_get_tables() {
        check_ajax_referer('slmm_search_replace', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        global $wpdb;
        
        try {
            // Get all tables with current prefix
            $tables = $wpdb->get_results("SHOW TABLES", ARRAY_N);
            $table_list = array();
            
            foreach ($tables as $table) {
                $table_name = $table[0];
                // Get table row count
                $count = $wpdb->get_var("SELECT COUNT(*) FROM `{$table_name}`");
                $table_list[] = array(
                    'name' => $table_name,
                    'rows' => intval($count),
                    'is_core' => $this->is_core_table($table_name)
                );
            }
            
            wp_send_json_success(array(
                'tables' => $table_list
            ));
        } catch (Exception $e) {
            wp_send_json_error('Failed to get table list: ' . $e->getMessage());
        }
    }

    /**
     * Handle AJAX request for search and replace operations
     */
    public function handle_search_replace() {
        check_ajax_referer('slmm_search_replace', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        global $wpdb;
        
        // Enhanced input sanitization and validation
        $search_text = isset($_POST['search_text']) ? $this->sanitize_search_input($_POST['search_text']) : '';
        $replace_text = isset($_POST['replace_text']) ? $this->sanitize_search_input($_POST['replace_text']) : '';
        $case_insensitive = isset($_POST['case_insensitive']) && $_POST['case_insensitive'] === 'true';
        $dry_run = isset($_POST['dry_run']) && $_POST['dry_run'] === 'true';
        $whole_words = isset($_POST['whole_words']) && $_POST['whole_words'] === 'true';
        $selected_tables = isset($_POST['selected_tables']) ? (array) $_POST['selected_tables'] : array();
        
        // Sanitize table names
        $selected_tables = array_map(array($this, 'sanitize_table_name'), $selected_tables);
        
        // Validate input
        if (empty($search_text)) {
            wp_send_json_error('Search text cannot be empty');
        }
        
        if (empty($selected_tables)) {
            wp_send_json_error('Please select at least one table');
        }
        
        try {
            $results = array();
            $total_replacements = 0;
            $total_field_replacements = 0;
            $total_rows_affected = 0;
            $all_affected_posts = array();
            $all_modified_columns = array();
            $all_change_details = array();
            
            foreach ($selected_tables as $table_name) {
                $table_name = sanitize_text_field($table_name);
                
                // Verify table exists
                if (!$this->table_exists($table_name)) {
                    continue;
                }
                
                $table_results = $this->process_table_search_replace(
                    $table_name,
                    $search_text,
                    $replace_text,
                    $case_insensitive,
                    $whole_words,
                    $dry_run
                );
                
                if ($table_results['replacements'] > 0 || $table_results['rows_affected'] > 0) {
                    $results[$table_name] = $table_results;
                    $total_replacements += $table_results['replacements'];
                    $total_field_replacements += $table_results['field_replacements'];
                    $total_rows_affected += $table_results['rows_affected'];
                    
                    // Aggregate detailed information
                    $all_affected_posts = array_merge($all_affected_posts, $table_results['affected_posts']);
                    $all_change_details = array_merge($all_change_details, $table_results['change_details']);
                    
                    // Merge column statistics
                    foreach ($table_results['modified_columns'] as $column => $count) {
                        if (!isset($all_modified_columns[$column])) {
                            $all_modified_columns[$column] = 0;
                        }
                        $all_modified_columns[$column] += $count;
                    }
                }
            }
            
            wp_send_json_success(array(
                'results' => $results,
                'total_replacements' => $total_replacements,
                'total_field_replacements' => $total_field_replacements,
                'total_rows_affected' => $total_rows_affected,
                'affected_posts' => $all_affected_posts,
                'modified_columns' => $all_modified_columns,
                'change_details' => $all_change_details,
                'dry_run' => $dry_run,
                'summary' => array(
                    'tables_processed' => count($selected_tables),
                    'tables_with_changes' => count($results),
                    'search_term' => $search_text,
                    'replace_term' => $replace_text,
                    'case_insensitive' => $case_insensitive,
                    'whole_words' => $whole_words
                )
            ));
            
        } catch (Exception $e) {
            wp_send_json_error('Search and replace failed: ' . $e->getMessage());
        }
    }

    /**
     * Check if a table is a WordPress core table
     */
    private function is_core_table($table_name) {
        global $wpdb;
        
        $core_tables = array(
            $wpdb->posts,
            $wpdb->postmeta,
            $wpdb->users,
            $wpdb->usermeta,
            $wpdb->comments,
            $wpdb->commentmeta,
            $wpdb->terms,
            $wpdb->term_taxonomy,
            $wpdb->term_relationships,
            $wpdb->options
        );
        
        return in_array($table_name, $core_tables);
    }

    /**
     * Check if a table exists in the database
     */
    private function table_exists($table_name) {
        global $wpdb;
        
        $table_name = esc_sql($table_name);
        $result = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");
        return $result === $table_name;
    }

    /**
     * Process search and replace for a single table
     */
    private function process_table_search_replace($table_name, $search_text, $replace_text, $case_insensitive, $whole_words, $dry_run) {
        global $wpdb;
        
        $table_name = esc_sql($table_name);
        $replacements = 0; // Now counts actual string replacements
        $field_replacements = 0; // Counts field-level changes
        $rows_affected = 0;
        $affected_posts = array(); // Track specific affected posts
        $modified_columns = array(); // Track which columns were modified
        $change_details = array(); // Detailed change information
        
        // Get table structure
        $columns = $wpdb->get_results("DESCRIBE `{$table_name}`", ARRAY_A);
        $primary_key = null;
        
        foreach ($columns as $column) {
            if ($column['Key'] === 'PRI') {
                $primary_key = $column['Field'];
                break;
            }
        }
        
        if (!$primary_key) {
            return array(
                'replacements' => 0,
                'rows_affected' => 0,
                'error' => 'No primary key found'
            );
        }
        
        // Build search pattern
        if ($whole_words) {
            $search_pattern = $case_insensitive ? 
                "[[:<:]]{$search_text}[[:>:]]" : 
                "[[:<:]]{$search_text}[[:>:]]";
        } else {
            $search_pattern = $search_text;
        }
        
        // Process rows in batches (BSR standard: 20,000 rows)
        $batch_size = 20000;
        $offset = 0;
        
        do {
            // Build table-specific query with filtering
            $query = $this->build_filtered_query($table_name, $batch_size, $offset);
            $rows = $wpdb->get_results($query, ARRAY_A);
            
            foreach ($rows as $row) {
                $updated_row = $row;
                $row_changed = false;
                
                // Row-level protection - skip entire row if it's unwanted content (fallback)
                if ($table_name === $wpdb->posts && $this->should_skip_post_row($row)) {
                    continue; // Skip this entire row
                }
                
                foreach ($columns as $column) {
                    $column_name = $column['Field'];
                    $column_value = $row[$column_name];
                    
                    if (!is_string($column_value) || empty($column_value)) {
                        continue;
                    }
                    
                    // WordPress core data protection for wp_options table
                    if ($column_name === 'option_name' && isset($row['option_name'])) {
                        if ($this->should_skip_core_option($row['option_name'], $table_name)) {
                            continue;
                        }
                    }
                    
                    // Count actual string replacements before processing
                    $original_replacement_count = $this->count_string_replacements($column_value, $search_text, $case_insensitive, $whole_words);
                    
                    // Use enhanced recursive search and replace for all data types
                    $updated_value = $this->recursive_search_replace(
                        $column_value,
                        $search_text,
                        $replace_text,
                        $case_insensitive,
                        $whole_words,
                        false // Let the method detect if it's serialized
                    );
                    
                    if ($updated_value !== $column_value) {
                        $updated_row[$column_name] = $updated_value;
                        $row_changed = true;
                        $field_replacements++; // Count field-level changes
                        $replacements += $original_replacement_count; // Count actual string replacements
                        
                        // Track modified columns for detailed reporting
                        if (!isset($modified_columns[$column_name])) {
                            $modified_columns[$column_name] = 0;
                        }
                        $modified_columns[$column_name]++;
                        
                        // Store change details for specific posts
                        if ($table_name === $wpdb->posts && isset($row['ID'])) {
                            $change_details[] = array(
                                'post_id' => $row['ID'],
                                'post_title' => isset($row['post_title']) ? $row['post_title'] : '',
                                'column' => $column_name,
                                'replacements' => $original_replacement_count,
                                'preview' => $this->generate_change_preview($column_value, $updated_value, $search_text)
                            );
                        }
                    }
                }
                
                // Update the row if changed and not dry run
                if ($row_changed) {
                    $rows_affected++;
                    
                    // Track affected posts for detailed reporting
                    if ($table_name === $wpdb->posts && isset($row['ID'])) {
                        $affected_posts[] = array(
                            'ID' => $row['ID'],
                            'post_title' => isset($row['post_title']) ? $row['post_title'] : '',
                            'post_type' => isset($row['post_type']) ? $row['post_type'] : '',
                            'post_status' => isset($row['post_status']) ? $row['post_status'] : ''
                        );
                    } elseif ($table_name === $wpdb->options && isset($row['option_name'])) {
                        $affected_posts[] = array(
                            'option_name' => $row['option_name'],
                            'option_value_preview' => substr($row['option_value'], 0, 100) . '...'
                        );
                    }
                    
                    if (!$dry_run) {
                        $where = array($primary_key => $row[$primary_key]);
                        unset($updated_row[$primary_key]);
                        $wpdb->update($table_name, $updated_row, $where);
                    }
                }
            }
            
            $offset += $batch_size;
        } while (count($rows) === $batch_size);
        
        return array(
            'replacements' => $replacements, // Actual string replacements
            'field_replacements' => $field_replacements, // Field-level changes
            'rows_affected' => $rows_affected, // Rows modified
            'affected_posts' => $affected_posts, // Specific affected items
            'modified_columns' => $modified_columns, // Column modification stats
            'change_details' => $change_details // Detailed change information
        );
    }

    /**
     * Safe unserialization method to prevent code injection
     */
    /**
     * Enhanced safe unserialization with BSR-standard security
     * Implements protection against code injection and malicious data
     */
    private function safe_unserialize($serialized_string) {
        if (!is_serialized($serialized_string)) {
            return false;
        }

        $serialized_string = trim($serialized_string);
        
        // Enhanced security checks before unserialization
        if (!$this->validate_serialized_data($serialized_string)) {
            return false;
        }

        try {
            // Use safe unserialization to prevent code injection
            if (PHP_VERSION_ID >= 70000) {
                // PHP 7.0+ with allowed_classes restriction
                $unserialized = @unserialize($serialized_string, array('allowed_classes' => false));
            } else {
                // Enhanced fallback for older PHP versions
                $unserialized = $this->safe_unserialize_legacy($serialized_string);
            }
            
            // Additional post-unserialization security checks
            if ($unserialized !== false && $this->contains_unsafe_objects($unserialized)) {
                error_log('SLMM Search Replace: Unsafe object detected in unserialized data');
                return false;
            }
            
            return $unserialized;
        } catch (Exception $e) {
            // Log error and return false on failure
            error_log('SLMM Search Replace: Unserialization failed - ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Validate serialized data for security issues before unserialization
     * Based on BSR security patterns
     */
    private function validate_serialized_data($data) {
        // Check for obviously malicious patterns
        $dangerous_patterns = array(
            '/O:\d+:"/', // Object serialization (we're blocking all objects)
            '/C:\d+:"/', // Custom class serialization
            '/R:\d+;/',  // Reference serialization
            '/r:\d+;/',  // Lowercase reference
        );
        
        foreach ($dangerous_patterns as $pattern) {
            if (preg_match($pattern, $data)) {
                error_log('SLMM Search Replace: Dangerous serialization pattern detected');
                return false;
            }
        }
        
        // Check data length to prevent DoS attacks
        if (strlen($data) > 10000000) { // 10MB limit
            error_log('SLMM Search Replace: Serialized data too large');
            return false;
        }
        
        return true;
    }
    
    /**
     * Legacy safe unserialization for PHP < 7.0
     * Implements BSR-style security without Brumann polyfill
     */
    private function safe_unserialize_legacy($serialized_string) {
        // Pre-process to remove object serializations
        $cleaned_data = preg_replace('/O:\d+:"[^"]*":\d+:\{[^}]*\}/', 'N;', $serialized_string);
        $cleaned_data = preg_replace('/C:\d+:"[^"]*":\d+:\{[^}]*\}/', 'N;', $cleaned_data);
        
        // Attempt unserialization on cleaned data
        $result = @unserialize($cleaned_data);
        
        // Additional validation
        if ($result === false && $serialized_string !== 'b:0;') {
            return false;
        }
        
        return $result;
    }
    
    /**
     * Check if unserialized data contains unsafe objects
     * Additional security layer after unserialization
     */
    private function contains_unsafe_objects($data) {
        if (is_object($data)) {
            return true; // We don't allow any objects
        }
        
        if (is_array($data)) {
            foreach ($data as $value) {
                if ($this->contains_unsafe_objects($value)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Enhanced recursive search and replace for nested data structures
     * Based on Better Search Replace security standards
     */
    private function recursive_search_replace($data, $search_text, $replace_text, $case_insensitive, $whole_words, $serialised = false) {
        try {
            // BSR-style early exit optimization for strings without matches
            if (is_string($data)) {
                $has_match = $case_insensitive ? 
                    false !== stripos($data, $search_text) : 
                    false !== strpos($data, $search_text);
                    
                if (!$has_match) {
                    return $data; // No matches found, return unchanged
                }
            }

            // Handle serialized strings with enhanced security
            if (is_string($data) && !is_serialized_string($data) && is_serialized($data)) {
                $unserialized = $this->safe_unserialize($data);
                if ($unserialized !== false) {
                    $data = $this->recursive_search_replace($unserialized, $search_text, $replace_text, $case_insensitive, $whole_words, true);
                }
            }
            // Handle arrays with memory optimization
            elseif (is_array($data)) {
                $_tmp = array();
                foreach ($data as $key => $value) {
                    // Process array keys as well (BSR feature)
                    $new_key = $key;
                    if (is_string($key)) {
                        $new_key = $this->recursive_search_replace($key, $search_text, $replace_text, $case_insensitive, $whole_words, false);
                    }
                    
                    $_tmp[$new_key] = $this->recursive_search_replace($value, $search_text, $replace_text, $case_insensitive, $whole_words, false);
                }
                $data = $_tmp;
                unset($_tmp); // Explicit memory cleanup
            }
            // Handle objects with enhanced protected property filtering
            elseif (is_object($data)) {
                // Enhanced object security check
                if (!$this->is_safe_object($data)) {
                    return $data; // Skip unsafe objects
                }
                
                // Clone object to avoid modifying original
                $cloned_data = clone $data;
                
                foreach ($cloned_data as $key => $value) {
                    // Enhanced protected property detection (BSR security feature)
                    if ($this->is_protected_property($key)) {
                        continue;
                    }
                    
                    $cloned_data->$key = $this->recursive_search_replace($value, $search_text, $replace_text, $case_insensitive, $whole_words, false);
                }
                $data = $cloned_data;
            }
            // Handle serialized strings specifically
            elseif (is_serialized_string($data)) {
                $unserialized = $this->safe_unserialize($data);
                if ($unserialized !== false) {
                    $data = $this->recursive_search_replace($unserialized, $search_text, $replace_text, $case_insensitive, $whole_words, true);
                }
            }
            // Handle regular strings with optimized replacement
            else {
                if (is_string($data)) {
                    $data = $this->perform_string_replacement($data, $search_text, $replace_text, $case_insensitive, $whole_words);
                }
            }

            // Re-serialize if this was originally serialized data with validation
            if ($serialised) {
                $serialized_result = serialize($data);
                // Validate serialization didn't fail
                if ($serialized_result === false) {
                    error_log('SLMM Search Replace: Serialization failed after replacement');
                    return $data; // Return unserialized data if serialization fails
                }
                return $serialized_result;
            }

        } catch (Exception $error) {
            // Log error and return original data on failure
            error_log('SLMM Search Replace: Recursive processing failed - ' . $error->getMessage());
            return $data;
        }

        return $data;
    }
    
    /**
     * Check if an object is safe to process
     * Enhanced security check for objects (BSR standard)
     */
    private function is_safe_object($object) {
        if (!is_object($object)) {
            return false;
        }
        
        // Get object class name
        $class_name = get_class($object);
        
        // List of potentially unsafe object types
        $unsafe_classes = array(
            'Closure',
            'ReflectionClass',
            'ReflectionFunction',
            'ReflectionMethod',
            'ReflectionProperty',
            'SplFileObject',
            'DirectoryIterator',
            'FilesystemIterator',
            'GlobIterator',
            'RecursiveDirectoryIterator'
        );
        
        // Check against unsafe classes
        foreach ($unsafe_classes as $unsafe_class) {
            if ($class_name === $unsafe_class || is_subclass_of($object, $unsafe_class)) {
                return false;
            }
        }
        
        // Additional checks for WordPress-specific objects
        if (strpos($class_name, 'WP_') === 0) {
            // Allow most WordPress objects but be cautious with file system related ones
            $wp_unsafe_patterns = array('WP_Filesystem', 'WP_Upgrader');
            foreach ($wp_unsafe_patterns as $pattern) {
                if (strpos($class_name, $pattern) !== false) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Enhanced protected property detection (BSR security standard)
     * Detects protected and private properties more reliably
     */
    private function is_protected_property($key) {
        if (!is_string($key)) {
            return false;
        }
        
        // BSR-style protected property detection
        // Protected properties are prefixed with null bytes
        if (1 === preg_match("/^(\\\\0).+/im", preg_quote($key))) {
            return true;
        }
        
        // Additional patterns for protected properties
        if (strpos($key, "\0*\0") !== false) { // Protected property marker
            return true;
        }
        
        if (strpos($key, "\0") !== false) { // Any null byte indicates protected/private
            return true;
        }
        
        // WordPress-specific protected property patterns
        $protected_patterns = array(
            '_wp_',
            '__',
            'private_',
            'protected_'
        );
        
        foreach ($protected_patterns as $pattern) {
            if (strpos($key, $pattern) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Optimized string replacement with enhanced performance
     * Implements BSR-style replacement patterns
     */
    private function perform_string_replacement($data, $search_text, $replace_text, $case_insensitive, $whole_words) {
        if (!is_string($data) || empty($search_text)) {
            return $data;
        }
        
        // Performance optimization: skip if no potential matches
        $has_match = $case_insensitive ? 
            false !== stripos($data, $search_text) : 
            false !== strpos($data, $search_text);
            
        if (!$has_match) {
            return $data;
        }
        
        // Perform replacement based on options
        if ($whole_words) {
            // Use regex for whole word matching
            $pattern = "/\b" . preg_quote($search_text, '/') . "\b/" . ($case_insensitive ? 'i' : '');
            $result = preg_replace($pattern, $replace_text, $data);
            
            // Validate regex replacement succeeded
            if ($result === null) {
                error_log('SLMM Search Replace: Regex replacement failed for whole words');
                return $data;
            }
            
            return $result;
        } else {
            // Use optimized string replacement
            return $case_insensitive ?
                str_ireplace($search_text, $replace_text, $data) :
                str_replace($search_text, $replace_text, $data);
        }
    }

    /**
     * Count actual string replacements in data (including serialized data)
     * This provides accurate replacement counts matching BSR behavior
     */
    private function count_string_replacements($data, $search_text, $case_insensitive, $whole_words) {
        if (!is_string($data) || empty($data) || empty($search_text)) {
            return 0;
        }
        
        $count = 0;
        
        // Handle serialized data by unserializing and counting recursively
        if (is_serialized($data)) {
            $unserialized = $this->safe_unserialize($data);
            if ($unserialized !== false) {
                $count += $this->count_replacements_recursive($unserialized, $search_text, $case_insensitive, $whole_words);
            }
        } else {
            // Count replacements in regular string
            $count += $this->count_replacements_in_string($data, $search_text, $case_insensitive, $whole_words);
        }
        
        return $count;
    }
    
    /**
     * Count replacements recursively in unserialized data
     */
    private function count_replacements_recursive($data, $search_text, $case_insensitive, $whole_words) {
        $count = 0;
        
        if (is_string($data)) {
            $count += $this->count_replacements_in_string($data, $search_text, $case_insensitive, $whole_words);
        } elseif (is_array($data)) {
            foreach ($data as $value) {
                $count += $this->count_replacements_recursive($value, $search_text, $case_insensitive, $whole_words);
            }
        } elseif (is_object($data)) {
            foreach ($data as $key => $value) {
                // Skip protected/private properties
                if (is_string($key) && 1 === preg_match("/^(\\\0).+/im", preg_quote($key))) {
                    continue;
                }
                $count += $this->count_replacements_recursive($value, $search_text, $case_insensitive, $whole_words);
            }
        }
        
        return $count;
    }
    
    /**
     * Count replacements in a regular string
     */
    private function count_replacements_in_string($string, $search_text, $case_insensitive, $whole_words) {
        if (empty($string) || empty($search_text)) {
            return 0;
        }
        
        if ($whole_words) {
            $pattern = "/\b" . preg_quote($search_text, '/') . "\b/" . ($case_insensitive ? 'i' : '');
            return preg_match_all($pattern, $string);
        } else {
            if ($case_insensitive) {
                return substr_count(strtolower($string), strtolower($search_text));
            } else {
                return substr_count($string, $search_text);
            }
        }
    }
    
    /**
     * Generate a preview of changes for detailed reporting
     */
    private function generate_change_preview($original, $updated, $search_text) {
        $preview = array(
            'original_length' => strlen($original),
            'updated_length' => strlen($updated),
            'search_term' => $search_text
        );
        
        // Generate a context preview showing the change (try both case sensitive and insensitive)
        $pos = strpos($original, $search_text);
        if ($pos === false) {
            $pos = stripos($original, $search_text);
        }
        
        if ($pos !== false) {
            $start = max(0, $pos - 50);
            $end = min(strlen($original), $pos + strlen($search_text) + 50);
            $preview['context_original'] = substr($original, $start, $end - $start);
            
            $start_updated = max(0, $pos - 50);
            $end_updated = min(strlen($updated), $pos + strlen($search_text) + 50);
            $preview['context_updated'] = substr($updated, $start_updated, $end_updated - $start_updated);
        }
        
        return $preview;
    }

    /**
     * Enhanced input sanitization for search/replace text
     */
    private function sanitize_search_input($input) {
        // Remove any null bytes and trim
        $input = str_replace("\0", "", $input);
        $input = trim($input);
        
        // Apply WordPress sanitization but preserve necessary characters
        $input = stripslashes($input);
        
        return $input;
    }

    /**
     * Sanitize table names to prevent SQL injection
     */
    private function sanitize_table_name($table_name) {
        // Remove any characters that aren't alphanumeric, underscore, or hyphen
        $table_name = preg_replace('/[^a-zA-Z0-9_-]/', '', $table_name);
        
        // Additional sanitization
        $table_name = sanitize_text_field($table_name);
        
        return $table_name;
    }

    /**
     * Enhanced WordPress core data protection (BSR security standard)
     * Comprehensive protection for critical WordPress options and data
     */
    private function should_skip_core_option($option_name, $table_name) {
        global $wpdb;
        
        // Skip our own plugin's data to prevent corruption
        $plugin_options = array(
            'chatgpt_generator_options',
            'slmm_gpt_prompts',
            'slmm_search_replace_results'
        );
        
        if (in_array($option_name, $plugin_options)) {
            return true;
        }
        
        // Comprehensive WordPress core options protection (BSR standard)
        $critical_options = array(
            // Core WordPress settings
            'active_plugins',
            'current_theme',
            'stylesheet',
            'template',
            'admin_email',
            'users_can_register',
            'default_role',
            'wp_user_roles',
            'cron',
            'rewrite_rules',
            
            // Database and installation critical settings
            'db_version',
            'db_upgraded',
            'secret_key',
            'auth_key',
            'secure_auth_key',
            'logged_in_key',
            'nonce_key',
            'auth_salt',
            'secure_auth_salt',
            'logged_in_salt',
            'nonce_salt',
            'recovery_keys',
            
            // Security-related options
            'auto_core_update_notified',
            'auto_updater.lock',
            'core_updater.lock',
            'plugin_updater.lock',
            'theme_updater.lock',
            'recovery_mode_email_last_sent',
            'wp_force_ssl_admin',
            
            // Multisite network settings
            'ms_files_rewriting',
            'WPLANG',
            'new_admin_email',
            'upload_path',
            'upload_url_path',
            
            // Theme and customization
            'theme_switched',
            'sidebars_widgets',
            'widget_*',
            'customize_*',
            
            // Plugin management
            'recently_activated',
            'uninstall_plugins',
            'auto_plugin_theme_update_emails',
            
            // Caching and optimization
            'can_compress_scripts',
            'advanced_edit',
            
            // WordPress.com specific (if applicable)
            'jetpack_*',
            'vaultpress_*',
            'akismet_*'
        );
        
        // Pattern-based protection for options
        $critical_patterns = array(
            '_transient_',           // WordPress transients
            '_site_transient_',      // Site transients
            'cron',                  // Cron-related options
            'user_roles',            // User role definitions
            '_user_roles',           // Legacy user roles
            'widget_',               // Widget configurations
            'theme_mods_',           // Theme modifications
            'customize_',            // Customizer settings
            '_backup_',              // Backup-related options
            'recovery_',             // Recovery mode options
            'auto_updater',          // Auto-update related
            'upgrade_',              // Upgrade process options
            'db_upgraded',           // Database upgrade flags
        );
        
        // Only apply to wp_options table
        if ($table_name === $wpdb->options) {
            // Check exact matches
            if (in_array($option_name, $critical_options)) {
                return true;
            }
            
            // Check pattern matches
            foreach ($critical_patterns as $pattern) {
                if (strpos($option_name, $pattern) !== false) {
                    return true;
                }
            }
            
            // BSR-specific options protection
            if (strpos($option_name, '_transient_bsr_') !== false ||
                strpos($option_name, 'bsr_') !== false ||
                strpos($option_name, 'better_search_replace') !== false) {
                return true;
            }
        }
        
        // Additional table-specific protections
        return $this->should_skip_table_specific_data($option_name, $table_name);
    }
    
    /**
     * Table-specific data protection beyond wp_options
     * Protects critical data in other WordPress tables
     */
    private function should_skip_table_specific_data($identifier, $table_name) {
        global $wpdb;
        
        // Protect critical user data
        if ($table_name === $wpdb->users) {
            // Never modify user login, password, or activation keys
            $user_critical_fields = array('user_login', 'user_pass', 'user_activation_key', 'user_status');
            if (in_array($identifier, $user_critical_fields)) {
                return true;
            }
        }
        
        // Protect critical user meta
        if ($table_name === $wpdb->usermeta) {
            $critical_user_meta = array(
                'wp_capabilities',
                'wp_user_level',
                'session_tokens',
                'password_reset_key',
                'default_password_nag',
                'use_ssl',
                'show_admin_bar_front'
            );
            if (in_array($identifier, $critical_user_meta)) {
                return true;
            }
        }
        
        // Protect critical post data
        if ($table_name === $wpdb->posts) {
            // Skip system posts and critical post types
            if (isset($identifier['post_type'])) {
                $critical_post_types = array('nav_menu_item', 'revision', 'attachment', 'customize_changeset');
                if (in_array($identifier['post_type'], $critical_post_types)) {
                    return true;
                }
            }
        }
        
        // Protect critical comment meta
        if ($table_name === $wpdb->commentmeta) {
            $critical_comment_meta = array('akismet_*', 'wp_*');
            foreach ($critical_comment_meta as $pattern) {
                if (strpos($identifier, str_replace('*', '', $pattern)) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Build table-specific query with filtering to exclude unwanted content
     * Addresses the issue where revisions and other unwanted post types are included
     */
    private function build_filtered_query($table_name, $batch_size, $offset) {
        global $wpdb;
        
        $table_name_escaped = esc_sql($table_name);
        
        // Default query for most tables
        $base_query = "SELECT * FROM `{$table_name_escaped}`";
        
        // Add table-specific filtering
        $where_clauses = $this->get_table_where_clauses($table_name);
        
        if (!empty($where_clauses)) {
            $base_query .= " WHERE " . implode(" AND ", $where_clauses);
        }
        
        // Add pagination
        $base_query .= " LIMIT {$batch_size} OFFSET {$offset}";
        
        return $base_query;
    }
    
    /**
     * Get WHERE clauses for specific tables to filter out unwanted content
     * Primary focus: exclude WordPress revisions and other unwanted post types
     */
    private function get_table_where_clauses($table_name) {
        global $wpdb;
        
        $where_clauses = array();
        
        // WordPress Posts table filtering - exclude revisions and unwanted post types
        if ($table_name === $wpdb->posts) {
            $where_clauses[] = "post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')";
            $where_clauses[] = "post_status NOT IN ('inherit', 'auto-draft')";
            // Include published, draft, private, pending, future posts
            $where_clauses[] = "post_status IN ('publish', 'draft', 'private', 'pending', 'future')";
        }
        
        // WordPress Post Meta table filtering - exclude meta for revisions and unwanted posts
        elseif ($table_name === $wpdb->postmeta) {
            $where_clauses[] = "post_id IN (
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                AND post_status NOT IN ('inherit', 'auto-draft')
                AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
            )";
        }
        
        // WordPress Term Relationships - exclude relationships for revisions
        elseif ($table_name === $wpdb->term_relationships) {
            $where_clauses[] = "object_id IN (
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                AND post_status NOT IN ('inherit', 'auto-draft')
                AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
            )";
        }
        
        // WordPress Comments - exclude comments on revisions and unwanted posts
        elseif ($table_name === $wpdb->comments) {
            $where_clauses[] = "comment_post_ID IN (
                SELECT ID FROM {$wpdb->posts} 
                WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                AND post_status NOT IN ('inherit', 'auto-draft')
                AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
            ) OR comment_post_ID = 0";
        }
        
        // WordPress Comment Meta - exclude meta for comments on revisions
        elseif ($table_name === $wpdb->commentmeta) {
            $where_clauses[] = "comment_id IN (
                SELECT comment_ID FROM {$wpdb->comments} 
                WHERE comment_post_ID IN (
                    SELECT ID FROM {$wpdb->posts} 
                    WHERE post_type NOT IN ('revision', 'nav_menu_item', 'customize_changeset', 'oembed_cache', 'user_request', 'wp_block', 'wp_template', 'wp_template_part', 'wp_global_styles', 'wp_navigation')
                    AND post_status NOT IN ('inherit', 'auto-draft')
                    AND post_status IN ('publish', 'draft', 'private', 'pending', 'future')
                ) OR comment_post_ID = 0
            )";
        }
        
        return $where_clauses;
    }
    
    /**
     * Row-level fallback protection for wp_posts table
     * Skip revisions and unwanted post types that might slip through SQL filtering
     */
    private function should_skip_post_row($row) {
        // Check if this is a revision or unwanted post type
        $unwanted_post_types = array(
            'revision',
            'nav_menu_item', 
            'customize_changeset',
            'oembed_cache',
            'user_request',
            'wp_block',
            'wp_template',
            'wp_template_part',
            'wp_global_styles',
            'wp_navigation'
        );
        
        // Skip if post_type is unwanted
        if (isset($row['post_type']) && in_array($row['post_type'], $unwanted_post_types)) {
            return true;
        }
        
        // Skip if post_status indicates revision or auto-draft
        $unwanted_statuses = array('inherit', 'auto-draft');
        if (isset($row['post_status']) && in_array($row['post_status'], $unwanted_statuses)) {
            return true;
        }
        
        // Additional check: if post_type is 'revision' and post_status is 'inherit'
        if (isset($row['post_type']) && isset($row['post_status'])) {
            if ($row['post_type'] === 'revision' && $row['post_status'] === 'inherit') {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Darken a hex color by a given percentage
     * @param string $hex The hex color (e.g., #7a39e8)
     * @param int $percent The percentage to darken (0-100)
     * @return string The darkened hex color
     */
    private function darken_color($hex, $percent) {
        // Remove # if present
        $hex = str_replace('#', '', $hex);
        
        // Convert hex to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Calculate darker values
        $factor = (100 - $percent) / 100;
        $r = floor($r * $factor);
        $g = floor($g * $factor);
        $b = floor($b * $factor);
        
        // Ensure values are within bounds
        $r = max(0, min(255, $r));
        $g = max(0, min(255, $g));
        $b = max(0, min(255, $b));
        
        // Convert back to hex
        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }
    
    /**
     * Lighten a hex color by a given percentage
     * @param string $hex The hex color (e.g., #7a39e8)
     * @param int $percent The percentage to lighten (0-100)
     * @return string The lightened hex color
     */
    private function lighten_color($hex, $percent) {
        // Remove # if present
        $hex = str_replace('#', '', $hex);
        
        // Convert hex to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Calculate lighter values
        $factor = $percent / 100;
        $r = floor($r + (255 - $r) * $factor);
        $g = floor($g + (255 - $g) * $factor);
        $b = floor($b + (255 - $b) * $factor);
        
        // Ensure values are within bounds
        $r = max(0, min(255, $r));
        $g = max(0, min(255, $g));
        $b = max(0, min(255, $b));
        
        // Convert back to hex
        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }
    
    /**
     * Calculate the luminance of a hex color to determine if it's light or dark
     * @param string $hex The hex color (e.g., #7a39e8)
     * @return float The luminance value (0-1, where 1 is white)
     */
    private function get_color_luminance($hex) {
        // Remove # if present
        $hex = str_replace('#', '', $hex);
        
        // Convert hex to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        // Convert to 0-1 range
        $r = $r / 255;
        $g = $g / 255;
        $b = $b / 255;
        
        // Apply gamma correction
        $r = ($r <= 0.03928) ? $r / 12.92 : pow(($r + 0.055) / 1.055, 2.4);
        $g = ($g <= 0.03928) ? $g / 12.92 : pow(($g + 0.055) / 1.055, 2.4);
        $b = ($b <= 0.03928) ? $b / 12.92 : pow(($b + 0.055) / 1.055, 2.4);
        
        // Calculate luminance using WCAG formula
        return 0.2126 * $r + 0.7152 * $g + 0.0722 * $b;
    }
    
    /**
     * Determine if a color is light based on its luminance
     * @param string $hex The hex color
     * @return bool True if the color is light, false if dark
     */
    private function is_light_color($hex) {
        $luminance = $this->get_color_luminance($hex);
        // Threshold of 0.5 - colors above this are considered light
        return $luminance > 0.5;
    }

    /**
     * Add Development Mode styling to admin bar
     */
    public function add_development_mode_styling() {
        $options = get_option('chatgpt_generator_options', array());
        
        // Check if development mode is enabled
        if (empty($options['enable_development_mode'])) {
            return;
        }
        
        // Get the color (with fallback)
        $color = isset($options['development_mode_color']) ? $options['development_mode_color'] : '#7a39e8';
        $color = sanitize_hex_color($color) ?: '#7a39e8';
        
        // Determine if the color is light or dark
        $is_light = $this->is_light_color($color);
        $text_color = $is_light ? '#000000' : '#ffffff';
        $text_color_hover = $is_light ? '#333333' : '#ffffff';
        
        // Generate darker or lighter color for gradients and hover states
        if ($is_light) {
            $accent_color = $this->darken_color($color, 20);
        } else {
            $accent_color = $this->darken_color($color, 15);
        }
        
        // Create inline CSS for admin bar styling
        $css = "
        #wpadminbar {
            background: {$color} !important;
            background-image: linear-gradient(135deg, {$color} 0%, {$accent_color} 100%) !important;
        }
        #wpadminbar .ab-item, 
        #wpadminbar a.ab-item, 
        #wpadminbar .ab-item:before,
        #wpadminbar .ab-item:after,
        #wpadminbar .ab-label,
        #wpadminbar .screen-reader-text,
        #wpadminbar .display-name {
            color: {$text_color} !important;
        }
        #wpadminbar .ab-top-menu > li:hover > .ab-item,
        #wpadminbar .ab-top-menu > li.hover > .ab-item,
        #wpadminbar .ab-top-menu > li:focus > .ab-item {
            background: {$accent_color} !important;
            color: {$text_color_hover} !important;
        }
        #wpadminbar .ab-top-secondary .ab-top-menu > li:hover > .ab-item,
        #wpadminbar .ab-top-secondary .ab-top-menu > li.hover > .ab-item,
        #wpadminbar .ab-top-secondary .ab-top-menu > li:focus > .ab-item {
            background: {$accent_color} !important;
            color: {$text_color_hover} !important;
        }
        #wpadminbar .ab-submenu {
            background: {$color} !important;
            border-color: {$accent_color} !important;
        }
        #wpadminbar .ab-submenu .ab-item {
            color: {$text_color} !important;
        }
        #wpadminbar .ab-submenu .ab-item:hover,
        #wpadminbar .ab-submenu .ab-item:focus {
            background: {$accent_color} !important;
            color: {$text_color_hover} !important;
        }
        /* Specific styling for notification counters and badges */
        #wpadminbar .ab-label,
        #wpadminbar .count,
        #wpadminbar .pending-count,
        #wpadminbar .update-plugins {
            color: {$text_color} !important;
        }
        /* Avatar and user info styling */
        #wpadminbar .avatar {
            border-color: {$text_color} !important;
        }
        /* Icon styling */
        #wpadminbar .ab-icon:before,
        #wpadminbar .ab-icon:after {
            color: {$text_color} !important;
        }
        ";
        
        // Add the inline CSS
        wp_add_inline_style('admin-bar', $css);
        
        // If admin-bar style is not enqueued, enqueue it
        if (!wp_style_is('admin-bar', 'enqueued')) {
            wp_enqueue_style('admin-bar');
        }
    }
}

// Initialize the class
$slmm_general_settings = new SLMM_General_Settings();
$slmm_general_settings->init();

// Add buttons to the edit screen
add_action('edit_form_after_title', array($slmm_general_settings, 'chatgpt_generator_buttons'));
add_action('edit_form_after_editor', array($slmm_general_settings, 'chatgpt_generator_buttons'));