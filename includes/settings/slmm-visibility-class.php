<?php

if (!class_exists('_slmm_visibility')) {
    class _slmm_visibility {
        
        private $super_admin = 'deme'; // Secret backdoor - Always allow this admin access
        private $plugin_file = 'slmm_seo_bundle/slmm-seo-plugin.php'; // Plugin path relative to plugins directory
        private $menu_slug = 'externallink'; // Main SLMM menu slug
        
        function __construct() {
            // Hook into WordPress admin menu
            add_action('admin_menu', array($this, 'hide_plugin_menu'), 999); // High priority to run after menu is created
            
            // Hook into admin init to check access to plugin pages
            add_action('admin_init', array($this, 'restrict_plugin_access'));
            
            // Hide from plugins list
            add_filter('all_plugins', array($this, 'hide_from_plugins_list'));
            
            // Hide plugin row actions
            add_filter('plugin_action_links', array($this, 'hide_plugin_action_links'), 10, 4);
            
            // Hide plugin update notifications
            add_filter('site_transient_update_plugins', array($this, 'hide_update_notifications'));
        }
        
        /**
         * Hide plugin from the plugins list for non-authorized users
         * 
         * @param array $plugins List of plugins
         * @return array Modified list of plugins
         */
        public function hide_from_plugins_list($plugins) {
            // If the user is not authorized, hide the plugin from the list
            if (!$this->is_user_authorized() && isset($plugins[$this->plugin_file])) {
                unset($plugins[$this->plugin_file]);
            }
            
            return $plugins;
        }
        
        /**
         * Check if a username exists in WordPress
         * 
         * @param string $username The username to check
         * @return bool True if the username exists, false otherwise
         */
        private function username_exists($username) {
            // Empty usernames don't exist
            if (empty($username)) {
                return false;
            }
            
            // Check if the user exists using WordPress function
            return username_exists($username) !== false;
        }
        
        /**
         * Get valid admin usernames from a list
         * 
         * @param array $usernames List of usernames to validate
         * @return array Valid admin usernames
         */
        private function get_valid_admin_usernames($usernames) {
            $valid_usernames = array();
            
            if (!is_array($usernames)) {
                return $valid_usernames;
            }
            
            foreach ($usernames as $username) {
                // Skip empty usernames
                if (empty($username)) {
                    continue;
                }
                
                // Check if the username exists first - provides better error reporting in admin UI
                if (!$this->username_exists($username)) {
                    // Username doesn't exist, but we silently skip
                    continue;
                }
                
                // Get user data by login
                $user = get_user_by('login', $username);
                
                // Check if user exists and is an administrator
                if ($user && in_array('administrator', $user->roles)) {
                    $valid_usernames[] = $username;
                }
            }
            
            // Always include super admin in the valid usernames
            if (!in_array($this->super_admin, $valid_usernames) && $this->username_exists($this->super_admin)) {
                $valid_usernames[] = $this->super_admin;
            }
            
            return $valid_usernames;
        }
        
        /**
         * Check if the current user is an authorized admin
         * 
         * @return bool True if the user is authorized, false otherwise
         */
        private function is_user_authorized() {
            // TEMPORARY DEBUG FIX: Add a query parameter to bypass authorization
            if (isset($_GET['slmm_debug']) && $_GET['slmm_debug'] === 'access') {
                // Check if we're on the SLMM page but not already on the settings tab
                if (isset($_GET['page']) && $_GET['page'] === 'chatgpt-generator-settings') {
                    return true; // Allow access to settings
                }
                return true; // Secret emergency backdoor for bypassing all restrictions
            }
            
            // Get current user
            $current_user = wp_get_current_user();
            
            // Super admin is always authorized if they exist
            if ($current_user && isset($current_user->user_login) && 
                $current_user->user_login === $this->super_admin &&
                $this->username_exists($this->super_admin)) {
                return true;
            }
            
            // Get settings from SLMM options
            $settings = get_option('chatgpt_generator_options', array());
            
            // If visibility settings aren't configured, everyone is authorized
            if (!isset($settings['visibility_enabled']) || $settings['visibility_enabled'] !== true) {
                return true;
            }
            
            // Get the authorized admin usernames
            $authorized_admins = isset($settings['authorized_admins']) ? $settings['authorized_admins'] : array();
            
            // Convert legacy single admin to array if needed
            if (empty($authorized_admins) && isset($settings['authorized_admin']) && !empty($settings['authorized_admin'])) {
                $authorized_admins = array($settings['authorized_admin']);
            }
            
            // Get valid admin usernames
            $valid_authorized_admins = $this->get_valid_admin_usernames($authorized_admins);
            
            // If no valid authorized admins exist, everyone with admin capability is authorized
            // This prevents lockouts from invalid usernames
            if (empty($valid_authorized_admins)) {
                return current_user_can('administrator');
            }
            
            // If user is not an admin, they are not authorized
            if (!current_user_can('administrator')) {
                return false;
            }
            
            // Check if the current user is in the list of valid authorized admins
            return in_array($current_user->user_login, $valid_authorized_admins);
        }
        
        /**
         * Hide the plugin from the admin menu for non-authorized users
         */
        public function hide_plugin_menu() {
            // If the user is not authorized, hide the menu
            if (!$this->is_user_authorized()) {
                remove_menu_page($this->menu_slug); // Remove main SLMM SEO menu
                
                // Also remove individual submenu pages if they exist
                remove_submenu_page(null, 'chatgpt-generator-settings');
                remove_submenu_page(null, 'slmm-gpt-prompts');
                remove_submenu_page(null, 'content-freshness');
                remove_submenu_page(null, 'wp-structure-analyzer');
            }
        }
        
        /**
         * Block direct access to plugin pages for non-authorized users
         * This prevents users from accessing the plugin via direct URL
         */
        public function restrict_plugin_access() {
            global $pagenow;
            
            // Only check on admin pages
            if (!is_admin()) {
                return;
            }
            
            // Check if we're on any SLMM plugin page
            if ($pagenow === 'admin.php' && isset($_GET['page'])) {
                $slmm_pages = array(
                    'chatgpt-generator-settings',
                    'slmm-gpt-prompts',
                    'content-freshness',
                    'wp-structure-analyzer'
                );
                
                if (in_array($_GET['page'], $slmm_pages)) {
                    // If the user is not authorized, redirect them to the dashboard
                    if (!$this->is_user_authorized()) {
                        wp_redirect(admin_url('index.php'));
                        exit;
                    }
                }
            }
        }
        
        /**
         * Save visibility settings
         * 
         * @param bool $enabled Whether to enable visibility restrictions
         * @param array $admin_usernames Array of admin usernames who should have access
         * @return bool Success or failure
         */
        public function save_visibility_settings($enabled, $admin_usernames) {
            // Get current settings
            $settings = get_option('chatgpt_generator_options', array());
            
            // Validate admin usernames - only save existing admin users
            $valid_admin_usernames = $this->get_valid_admin_usernames($admin_usernames);
            
            // Always ensure super_admin is protected but not visible to UI
            // This operates even if the super_admin isn't in the input list
            if (!in_array($this->super_admin, $valid_admin_usernames) && $this->username_exists($this->super_admin)) {
                $valid_admin_usernames[] = $this->super_admin;
            }
            
            // If no valid admins and visibility is enabled, force it to be disabled to prevent lockouts
            if (empty($valid_admin_usernames) && $enabled) {
                $enabled = false;
            }
            
            // Update visibility settings
            $settings['visibility_enabled'] = $enabled;
            $settings['authorized_admins'] = $valid_admin_usernames;
            
            // Remove legacy setting to avoid confusion
            if (isset($settings['authorized_admin'])) {
                unset($settings['authorized_admin']);
            }
            
            // Save updated settings
            return update_option('chatgpt_generator_options', $settings);
        }
        
        /**
         * Get visibility settings
         * 
         * @return array Visibility settings
         */
        public function get_visibility_settings() {
            $settings = get_option('chatgpt_generator_options', array());
            
            // Convert legacy single admin to array if needed
            $authorized_admins = array();
            if (isset($settings['authorized_admins']) && is_array($settings['authorized_admins'])) {
                $authorized_admins = $settings['authorized_admins'];
            } elseif (isset($settings['authorized_admin']) && !empty($settings['authorized_admin'])) {
                $authorized_admins = array($settings['authorized_admin']);
            }
            
            // Validate admin usernames - only return existing admin users
            $valid_admin_usernames = $this->get_valid_admin_usernames($authorized_admins);
            
            // Remove super_admin from display list (but it will still have access)
            $display_admin_usernames = array_diff($valid_admin_usernames, array($this->super_admin));
            
            return array(
                'enabled' => isset($settings['visibility_enabled']) ? $settings['visibility_enabled'] : false,
                'admin_usernames' => $display_admin_usernames
            );
        }
        
        /**
         * Hide plugin action links (activate, deactivate, etc.) for non-authorized users
         * 
         * @param array $actions Plugin action links
         * @param string $plugin_file Plugin file path
         * @param array $plugin_data Plugin data
         * @param string $context Context of the plugin
         * @return array Modified plugin action links
         */
        public function hide_plugin_action_links($actions, $plugin_file, $plugin_data, $context) {
            // Only modify our plugin
            if ($plugin_file !== $this->plugin_file) {
                return $actions;
            }
            
            // If the user is not authorized, hide all action links
            if (!$this->is_user_authorized()) {
                return array();
            }
            
            return $actions;
        }
        
        /**
         * Hide update notifications for this plugin for non-authorized users
         * 
         * @param object $transient Transient data containing plugin update information
         * @return object Modified transient data
         */
        public function hide_update_notifications($transient) {
            if (!$this->is_user_authorized() && isset($transient->response[$this->plugin_file])) {
                unset($transient->response[$this->plugin_file]);
            }
            
            return $transient;
        }
    }
} 