# Memory Bank - SLMM SEO Bundle

This directory contains project memory and knowledge for the SLMM SEO Bundle WordPress plugin.

## Purpose
The memory bank stores important project context, decisions, and patterns to help maintain consistency across development sessions.

## Structure
- `decisions/` - Architecture decisions and rationale
- `patterns/` - Code patterns and best practices
- `issues/` - Known issues and their solutions
- `features/` - Feature documentation and requirements

## Usage
Add relevant context here when working on complex features or making architectural decisions.