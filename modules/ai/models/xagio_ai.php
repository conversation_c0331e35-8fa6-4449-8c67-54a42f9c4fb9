<?php
if (!defined('ABSPATH'))
    exit; // Exit if accessed directly

if (!class_exists('XAGIO_MODEL_AI')) {

    class XAGIO_MODEL_AI
    {
        public static function initialize()
        {
            if (!XAGIO_HAS_ADMIN_PERMISSIONS)
                return;

            add_action('admin_post_xagio_get_ai_frontend_output', [
                'XAGIO_MODEL_AI',
                'getAiFrontEndOutput'
            ]);

            add_action('admin_post_xagio_ai_process_text', [
                'XAGIO_MODEL_AI',
                'processTextEdit'
            ]);

            add_action('admin_post_xagio_ai_check_status_text', [
                'XAGIO_MODEL_AI',
                'checkTextStatus'
            ]);

            add_action('admin_post_xagio_ai_process_image', [
                'XAGIO_MODEL_AI',
                'processImageEditByAttachmentID'
            ]);

            add_action('admin_post_xagio_ai_check_status_image', [
                'XAGIO_MODEL_AI',
                'checkImageStatus'
            ]);

            add_action('admin_post_xagio_ai_check_status_cluster', [
                'XAGIO_MODEL_AI',
                'checkClusterStatus'
            ]);

            add_action('admin_post_xagio_ai_get_attachment_id', [
                'XAGIO_MODEL_AI',
                'getAttachmentIdByURL'
            ]);

            add_action('admin_post_xagio_get_ai_history', [
                'XAGIO_MODEL_AI',
                'getHistory'
            ]);
            add_action('admin_post_xagio_get_ai_schema_history', [
                'XAGIO_MODEL_AI',
                'getSchemaHistory'
            ]);

            add_action('admin_post_xagio_ai_schema', [
                'XAGIO_MODEL_AI',
                'getAiSchema'
            ]);
            add_action('admin_post_xagio_ai_suggest', [
                'XAGIO_MODEL_AI',
                'getAiSuggestions'
            ]);
            add_action('admin_post_xagio_ai_content', [
                'XAGIO_MODEL_AI',
                'getAiContent'
            ]);

            add_action('admin_post_xagio_ai_template_content', [
                'XAGIO_MODEL_AI',
                'getAiContentTemplate'
            ]);

            add_action('admin_post_xagio_ai_clustering', [
                'XAGIO_MODEL_AI',
                'getAiClustering'
            ]);


            add_action('admin_post_xagio_ai_use_template_content', [
                'XAGIO_MODEL_AI',
                'useAiContentTemplate'
            ]);

            add_action('admin_post_xagio_ai_undo_template_content', [
                'XAGIO_MODEL_AI',
                'undoAiContentTemplate'
            ]);

            add_action('admin_post_xagio_ai_output', [
                'XAGIO_MODEL_AI',
                'getAiOutput'
            ]);
            add_action('admin_post_xagio_modify_suggestion', [
                'XAGIO_MODEL_AI',
                'modifySuggestion'
            ]);
            add_action('admin_post_xagio_ai_get_average_prices', [
                'XAGIO_MODEL_AI',
                'getAveragePrices'
            ]);

            add_action('admin_post_xagio_copy_template_page', [
                'XAGIO_MODEL_AI',
                'copyTemplatePage'
            ]);

        }

        public static function copyTemplatePage()
        {
            global $wpdb;

            $page_id    = intval($_POST['page_id']);
            $is_service = sanitize_text_field(wp_unslash($_POST['page_type']));
            $title      = get_post_meta($page_id, 'XAGIO_SEO_TITLE', true);

            if (empty($title)) {
                return [
                    'status'  => 'error',
                    'message' => 'SEO Title is missing',
                ];
            }

            $page_type = 'Service';
            if ($is_service === 'homepage') {
                $page_type = 'Home';
            }

            // Use WP_Query instead of deprecated get_page_by_title
            $query = new WP_Query([
                'post_type'      => 'page',
                'post_status'    => 'publish',
                'title'          => $page_type,
                'posts_per_page' => 1,
            ]);

            if ($query->have_posts()) {
                $query->the_post();
                $template_page = get_post(get_the_ID());
                wp_reset_postdata();

                if ($template_page) {
                    // Update the target page with the template content.
                    $post_data = [
                        'ID'           => $page_id,
                        'post_title'   => $title,
                        // preserving the SEO title from target page meta
                        'post_content' => $template_page->post_content,
                    ];
                    wp_update_post($post_data);

                    // Now copy post meta from the template page to the target page.
                    // Fetch all meta for the template page.
                    $meta = get_post_meta($template_page->ID);
                    foreach ($meta as $meta_key => $meta_values) {
                        // Optionally, skip copying specific meta keys if desired.
                        // For instance, we are preserving the target's SEO title.
                        if ($meta_key === 'XAGIO_SEO_TITLE') {
                            continue;
                        }

                        // Remove existing meta with the same key from the target.
                        delete_post_meta($page_id, $meta_key);

                        // Loop through and add each meta value.
                        foreach ($meta_values as $meta_value) {
                            // For Elementor data, you can replicate the original low‐level
                            // insertion if needed.
                            if ($meta_key === '_elementor_data') {
                                $wpdb->insert(
                                    $wpdb->postmeta, [
                                    'post_id'    => $page_id,
                                    'meta_key'   => '_elementor_data',
                                    'meta_value' => $meta_value
                                ], [
                                        '%d',
                                        '%s',
                                        '%s'
                                    ]
                                );
                            } else {
                                add_post_meta($page_id, $meta_key, maybe_unserialize($meta_value));
                            }
                        }
                    }
                } else {
                    xagio_jsonc([
                        'status'  => 'error',
                        'message' => "Cannot find page for $page_type",
                    ]);
                }
            } else {
                xagio_jsonc([
                    'status'  => 'error',
                    'message' => "Cannot find any pages for $page_type",
                ]);
            }

            XAGIO_MODEL_OCW::clearElementorCache();

            xagio_jsonc([
                'status'  => 'success',
                'message' => "Successfully copied $page_type template"
            ]);

        }

        public static function getAveragePrices()
        {
            $output = XAGIO_API::apiRequest('ai', 'POST', [], $http_code);
            if ($http_code == 203) {
                xagio_jsonc([
                    'status'  => 'success',
                    'message' => 'Average Prices loaded',
                    'data'    => $output
                ]);
            } elseif ($http_code == 406) {
                xagio_jsonc([
                    'status'  => 'upgrade',
                    'message' => 'Upgrade your account to use AI features!'
                ]);
            } else {
                xagio_jsonc([
                    'status'  => 'error',
                    'message' => 'Average Prices not loaded!',
                    'data'    => $output
                ]);
            }
        }

        public static function get_status_for_current_post()
        {
            global $wpdb;

            // Get the current post ID
            $target_id = get_the_ID();

            // Prepare and execute the query using wpdb::prepare
            $outputQuery = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT `output`, `status` 
             FROM `xag_ai` 
             WHERE `input` = %s AND `target_id` = %d 
             ORDER BY `id` DESC 
             LIMIT 1", 'PAGE_CONTENT', $target_id
                ), ARRAY_A
            );

            // Get the status from the query result
            $status = $outputQuery['status'] ?? false;

            // Return true if status is 'running', otherwise false
            return $status === 'running';
        }

        public static function get_status_for_template_current_post()
        {
            global $wpdb;

            // Get the current post ID
            $target_id = get_the_ID();

            // Prepare and execute the query using wpdb::prepare
            $outputQuery = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT `output`, `status` 
             FROM `xag_ai` 
             WHERE `input` = %s AND `target_id` = %d 
             ORDER BY `id` DESC 
             LIMIT 1", 'PAGE_CONTENT_TEMPLATE', $target_id
                ), ARRAY_A
            );

            // Get the status from the query result
            $status = $outputQuery['status'] ?? false;

            // Return true if status is 'running', otherwise false
            return $status === 'running';
        }


        public static function getSchemaHistory()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['post_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            // Sanitize the input
            $post_id = intval($_POST['post_id']);

            // Prepare and execute the query using wpdb::prepare
            $historyQuery = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT `id`, `status` FROM `xag_ai` WHERE `input` = %s AND `target_id` = %d ORDER BY `id` DESC", 'SCHEMA', $post_id
                ), ARRAY_A
            );

            $return = [];

            // Process the results into an array
            foreach ($historyQuery as $history) {
                $return[] = [
                    'id'     => $history['id'],
                    'status' => $history['status']
                ];
            }

            // Return the data as a JSON response
            xagio_jsonc([
                'status'  => 'success',
                'message' => 'Schema AI Status loaded',
                'data'    => $return
            ]);
        }


        public static function getHistory()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['post_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            // Sanitize input
            $post_id = intval($_POST['post_id']);
            $row_id  = isset($_POST['row_id']) ? intval($_POST['row_id']) : null;

            // Prepare the query depending on whether `row_id` is set or not
            if ($row_id !== null) {
                $historyQuery = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT `id`, `output`, DATE_FORMAT(`date_created`, '%%a, %%e %%b %%Y') as date_created, `status` 
                 FROM `xag_ai` 
                 WHERE `input` = %s AND `target_id` = %d AND `id` = %d 
                 ORDER BY `id` DESC", 'PAGE_CONTENT', $post_id, $row_id
                    ), ARRAY_A
                );
            } else {
                $historyQuery = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT `id`, `output`, DATE_FORMAT(`date_created`, '%%a, %%e %%b %%Y') as date_created, `status` 
                 FROM `xag_ai` 
                 WHERE `input` = %s AND `target_id` = %d 
                 ORDER BY `id` DESC", 'PAGE_CONTENT', $post_id
                    ), ARRAY_A
                );
            }

            $return = [];
            $count  = 0;

            // Loop through the results to build the response
            foreach ($historyQuery as $history) {
                $return[] = [
                    'id'           => $history['id'],
                    'status'       => $history['status'],
                    'output'       => ($count == 0) ? stripslashes($history['output']) : '',
                    'small'        => substr(stripslashes(wp_strip_all_tags($history['output'])), 0, 100),
                    'date_created' => $history['date_created']
                ];

                $count++;
            }

            // Return the history data as JSON
            xagio_jsonc([
                'status'  => 'success',
                'message' => 'History loaded',
                'data'    => $return
            ]);
        }


        public static function modifySuggestion()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['type'], $_POST['text'], $_POST['group_id'], $_POST['ai_input'], $_POST['row_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            global $wpdb;

            $type     = sanitize_text_field(wp_unslash($_POST['type']));
            $text     = sanitize_text_field(wp_unslash($_POST['text']));
            $group_id = intval($_POST['group_id']);
            $ai_input = sanitize_text_field(wp_unslash($_POST['ai_input']));
            $row_id   = intval($_POST['row_id']);

            $type  = explode('-', $type);
            $key   = $type[0];
            $index = $type[1];

            switch ($key) {
                case "header":
                    $key = "h1";
                    break;
                case "desc":
                    $key = "description";
                    break;
            }

            $find_group = $wpdb->get_row(
                $wpdb->prepare("SELECT output FROM xag_ai WHERE id = %d AND target_id = %d AND input = %s", $row_id, $group_id, $ai_input), ARRAY_A
            );


            if (!isset($find_group['output'])) {
                xagio_jsonc([
                    'status'  => 'success',
                    'message' => 'Group suggestion not found, try again.'
                ]);
            }

            $output               = json_decode($find_group['output'], true);
            $output[$index][$key] = $text;

            $t = $wpdb->update('xag_ai', [
                'output' => wp_json_encode($output, JSON_PRETTY_PRINT)
            ], [
                'id'        => $row_id,
                'target_id' => $group_id,
                'input'     => $ai_input
            ]);

            xagio_jsonc([
                'status'  => 'success',
                'message' => 'Suggestion updated'
            ]);

        }

        public static function remoteCheckAiStatuses()
        {
            global $wpdb;

            // 1) find every locally-running AI request
            $running = $wpdb->get_results(
                "SELECT id, target_id, input, DATE_FORMAT(date_created, '%Y-%m-%d') AS run_date 
           FROM xag_ai 
          WHERE status = 'running'", ARRAY_A
            );

            if (empty($running)) {
                return;  // nothing to do
            }

            // 2) batch them by input type
            $batches = [];
            foreach ($running as $row) {
                $batches[$row['input']][] = [
                    'local_id'  => (int)$row['id'],
                    'target_id' => (int)$row['target_id'],
                    'run_date'  => $row['run_date'],
                ];
            }

            // 3) for each input, call the remote check once
            foreach ($batches as $input => $items) {
                $ids = array_column($items, 'target_id');
                // use the earliest run_date so we pull any remote updates since then
                $dates = array_column($items, 'run_date');
                $date  = min($dates);

                $remote = self::remoteCheckAiStatusByIds($ids, $input, $date);
                if (!is_array($remote)) {
                    continue;  // remote call failed or returned nothing useful
                }

                // 4) update each local row that the remote returned
                foreach ($remote as $r) {

                    $settings = json_decode($r['settings'], true);
                    unset($settings['args']);
                    unset($settings['fields']['admin_post']);
                    unset($settings['fields']['api_key']);
                    unset($settings['fields']['domain']);
                    $settings           = $settings['fields'];
                    $settings['output'] = $r['output'];
                    $settings['return'] = true;

                    // 1) instantiate a REST request
                    $request = new WP_REST_Request('POST', "/xagio/v1/ai/{$r['input']}");

                    // 2) inject all of your settings into it at once
                    foreach ($settings as $key => $value) {
                        $request->set_param($key, $value);
                    }

                    if (method_exists('XAGIO_MODEL_AI', 'xagio_ai_' . $r['input'])) {
                        call_user_func([
                            'XAGIO_MODEL_AI',
                            'xagio_ai_' . $r['input']
                        ], $request);
                    }

                }
            }

        }

        public static function remoteCheckAiStatusByIds($ids = [], $input = '', $date = '')
        {
            // nothing to do
            if (empty($ids) || empty($input)) {
                return false;
            }

            // sanitize inputs
            $ids   = array_map('intval', $ids);
            $input = sanitize_text_field($input);

            // build query params
            $params = [
                'target_ids' => implode(',', $ids),
                'input'      => $input
            ];

            if (!empty($date)) {
                $params['date'] = $date;
            }

            // call remote AI API
            $http_code = 0;
            $result    = XAGIO_API::apiRequest('ai', 'GET', $params, $http_code);

            // only accept a 200 + array response
            if ($http_code !== 200 || !is_array($result)) {
                return false;
            }

            // return the raw list of rows: each row = ['id'=>…,'target_id'=>…,'status'=>…,'output'=>…,'date_created'=>…]
            return $result;
        }

        public static function checkAiStatusByIds($ids = [], $input = '')
        {

            global $wpdb; // Ensure $wpdb is properly referenced

            if (empty($ids)) {
                return false; // Return false if no group IDs are provided
            }

            // Convert array to a comma-separated string for SQL IN clause
            $placeholders = implode(',', array_fill(0, count($ids), '%d'));
            $results      = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT `status` FROM xag_ai WHERE `input` = '$input' AND `target_id` IN ($placeholders)", ...$ids // Spread operator to pass arguments properly
                ), ARRAY_A
            );

            if (empty($results)) {
                return false; // Return false if no results are found
            }

            // Check if all statuses are "completed"
            foreach ($results as $row) {
                if ($row['status'] !== 'completed') {
                    return false;
                }
            }

            return true; // Return true only if all statuses are "completed"

        }

        public static function getAiImageEditsByAttachments($attachment_ids = [], $input = 'IMAGE_EDIT', $prompt_id = 12)
        {
            if (empty($attachment_ids)) {
                return [
                    'status'  => 'error',
                    'message' => 'No attachment IDs provided.'
                ];
            }

            $results = [];
            foreach ($attachment_ids as $attachment_id) {

                $image_url = wp_get_attachment_url($attachment_id);
                $alt_text  = get_post_meta($attachment_id, '_wp_attachment_image_alt', true);

                $http_code = 0;
                $result    = self::_sendAiRequest(
                    $input, $prompt_id, $attachment_id, [
                    $image_url,
                    $alt_text
                ], ['attachment_id' => $attachment_id], $http_code
                );

                $results[$attachment_id] = [
                    'status'  => ($http_code == 200) ? 'success' : 'error',
                    'message' => ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message']
                ];
            }

            return [
                'status'  => 'success',
                'results' => $results
            ];
        }

        public static function getAiContentByPosts($post_ids = [], $input = 'PAGE_CONTENT', $prompt_id = 2)
        {
            if (empty($post_ids)) {
                return [
                    'status'  => 'error',
                    'message' => 'No post IDs provided.'
                ];
            }

            $results = [];
            foreach ($post_ids as $post_id) {
                $title       = get_post_meta($post_id, 'XAGIO_SEO_TITLE', true);
                $description = get_post_meta($post_id, 'XAGIO_SEO_DESCRIPTION', true);
                $h1          = get_the_title($post_id);

                $http_code = 0;
                $result    = self::_sendAiRequest(
                    $input, $prompt_id, $post_id, [
                    $title,
                    $description,
                    $h1
                ], ['post_id' => $post_id], $http_code
                );

                $results[$post_id] = [
                    'status'  => ($http_code == 200) ? 'success' : 'error',
                    'message' => ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message']
                ];
            }

            return [
                'status'  => 'success',
                'results' => $results
            ];
        }

        public static function getAiContentByPostsTemplate($post_ids = [], $input = 'PAGE_CONTENT_TEMPLATE', $prompt_id = 11)
        {
            if (empty($post_ids)) {
                return [
                    'status'  => 'error',
                    'message' => 'No post IDs provided.'
                ];
            }

            $results = [];
            foreach ($post_ids as $post_id) {

                $args = self::getContentProfiles($post_id);

                $http_code = 0;
                $result    = self::_sendAiRequest(
                    $input, $prompt_id, $post_id, $args, ['post_id' => $post_id], $http_code
                );

                $results[$post_id] = [
                    'status'  => ($http_code == 200) ? 'success' : 'error',
                    'message' => ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message']
                ];
            }

            return [
                'status'  => 'success',
                'results' => $results
            ];
        }

        public static function getAiSchemaByPosts($post_ids = [], $input = 'SCHEMA', $schema_type = 'creative', $prompt_id = 3)
        {
            if (empty($post_ids)) {
                return [
                    'status'  => 'error',
                    'message' => 'No post IDs provided.'
                ];
            }

            $results = [];
            foreach ($post_ids as $post_id) {
                $title       = get_post_meta($post_id, 'XAGIO_SEO_TITLE', true);
                $description = get_post_meta($post_id, 'XAGIO_SEO_DESCRIPTION', true);
                $h1          = get_the_title($post_id);


                $args = self::getSchemaProfiles($post_id, $title, $description, $h1, $schema_type);

                if (sizeof($args) > 4) {
                    $prompt_id = 10;
                }


                $http_code = 0;
                $result    = self::_sendAiRequest(
                    $input, $prompt_id, $post_id, $args, ['post_id' => $post_id], $http_code
                );

                $results[$post_id] = [
                    'status'  => ($http_code == 200) ? 'success' : 'error',
                    'message' => ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message']
                ];
            }

            return [
                'status'  => 'success',
                'results' => $results
            ];
        }

        public static function getAiSuggestionsByGroups($group_ids = [], $input = 'SEO_SUGGESTIONS_MAIN_KW', $prompt_id = 6)
        {
            global $wpdb;

            if (empty($group_ids)) {
                return [
                    'status'  => 'error',
                    'message' => 'No group IDs provided.'
                ];
            }

            // Convert array to a comma-separated string for SQL IN clause
            $placeholders = implode(',', array_fill(0, count($group_ids), '%d'));
            $keywords     = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT * FROM xag_keywords WHERE `group_id` IN ($placeholders)", ...$group_ids
                ), ARRAY_A
            );

            if (empty($keywords)) {
                return [
                    'status'  => 'error',
                    'message' => 'No keywords found for the provided group IDs.'
                ];
            }

            $grouped_keywords = [];
            foreach ($keywords as $row) {
                $grouped_keywords[$row['group_id']][] = $row;
            }

            $results = [];
            foreach ($grouped_keywords as $group_id => $keywords) {
                $keyword_group = json_encode(self::packKeywords($keywords));
                $args          = [
                    $keyword_group,
                    ''
                ];
                $additional    = ['group_id' => $group_id];
                $http_code     = 0;
                $result        = self::_sendAiRequest($input, $prompt_id, $group_id, $args, $additional, $http_code);

                $results[$group_id] = [
                    'status'  => ($http_code == 200) ? 'success' : 'error',
                    'message' => ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message']
                ];
            }

            return [
                'status'  => 'success',
                'results' => $results
            ];
        }

        public static function getAiSuggestions()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['keyword_group']) || !isset($_POST['group_id']) || !isset($_POST['input']) || !isset($_POST['main_keyword'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $group_id      = intval(wp_unslash($_POST['group_id']));
            $input         = sanitize_text_field(wp_unslash($_POST['input']));
            $keyword_group = sanitize_text_field(wp_unslash($_POST['keyword_group']));
            $main_keyword  = sanitize_text_field(wp_unslash($_POST['main_keyword']));
            $prompt_id     = intval($_POST['prompt_id']);

            if (empty($keyword_group)) {
                xagio_jsonc([
                    'status'  => 'error',
                    'message' => 'No keywords detected for this group'
                ]);
            }

            // Check if there are results for this group
            $group_suggestions = $wpdb->get_row($wpdb->prepare('SELECT `status`, `output` FROM xag_ai WHERE `target_id` = %d AND `input` = %s', $group_id, $input), ARRAY_A);

            if (empty($_POST['regenerate'])) {
                if (isset($group_suggestions['status']) && $group_suggestions['status'] == 'completed') {
                    $group_suggestions = stripslashes(json_decode($group_suggestions['output'], TRUE));
                    xagio_jsonc([
                        'status'  => 'success',
                        'message' => 'Results are back',
                        'data'    => $group_suggestions
                    ]);
                }
            }

            if ($input === 'SEO_SUGGESTIONS') {
                $keyword_group = self::removeallslashes($keyword_group);
                $keyword_group = ltrim($keyword_group, '"');
                $keyword_group = rtrim($keyword_group, '"');
                $keyword_group = json_decode($keyword_group, TRUE);

                $keyword_list = "";
                foreach ($keyword_group as $item) {
                    $keyword_list .= $item['text'] . "(" . $item['weight'] . "), ";
                }
                $keyword_list = rtrim($keyword_list, ", ");

                $args = [
                    $keyword_list
                ];
            } else {
                $args = [
                    $keyword_group,
                    $main_keyword
                ];
            }

            $additional = [
                'group_id' => $group_id
            ];

            $http_code = 0;
            $result    = self::_sendAiRequest($input, $prompt_id, $group_id, $args, $additional, $http_code);

            if ($http_code == 406) {
                xagio_jsonc([
                    'status'  => 'upgrade',
                    'message' => 'Upgrade your account to use AI features.'
                ]);
            }

            xagio_jsonc([
                'status'  => ($http_code == 200) ? 'success' : 'error',
                'message' => $result['message']
            ]);
        }

        public static function getAiClustering()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            $project_id = intval($_POST['project_id']);
            $keywords   = join("\n", $_POST['keywords']);
            $input      = "CLUSTER";

            $http_code = 0;
            $result    = self::_sendAiRequest(
                $input, 15, $project_id, [
                $keywords
            ], ['project_id' => $project_id], $http_code
            );

            xagio_jsonc([
                'status'  => ($http_code == 200) ? 'success' : 'error',
                'message' => ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message']
            ]);
        }

        public static function getAiContentTemplate()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            $post_id   = intval($_POST['post_id']);
            $prompt_id = intval($_POST['prompt_id']);
            $input     = "PAGE_CONTENT_TEMPLATE";

            $args = self::getContentProfiles($post_id);

            if (isset($args['status'])) {
                xagio_jsonc([
                    'status'  => $args['status'],
                    'message' => $args['message']
                ]);
            }

            $http_code = 0;
            $result    = self::_sendAiRequest(
                $input, $prompt_id, $post_id, $args, ['post_id' => $post_id], $http_code
            );

            xagio_jsonc([
                'status'  => ($http_code == 200) ? 'success' : 'error',
                'message' => ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message']
            ]);

        }

        public static function undoAiContentTemplate()
        {
            global $wpdb;

            $post_id = intval($_POST['post_id']);

            $originalExists = get_post_meta($post_id, '_elementor_data_xag_original', TRUE);

            if (!empty($originalExists)) {
                $elementor_data = get_post_meta($post_id, '_elementor_data', TRUE);

                if (empty($elementor_data)) {
                    $wpdb->insert(
                        $wpdb->postmeta, [
                        'post_id'    => $post_id,
                        'meta_key'   => '_elementor_data',
                        'meta_value' => $originalExists
                    ], [
                            '%d',
                            '%s',
                            '%s'
                        ]
                    );
                } else {
                    $wpdb->update(
                        $wpdb->postmeta, [
                        'meta_value' => $originalExists
                    ], [
                        'post_id'  => $post_id,
                        'meta_key' => '_elementor_data'
                    ], ['%s'], [
                            '%d',
                            '%s'
                        ]
                    );
                }
            }

            XAGIO_MODEL_OCW::clearElementorCache();

            xagio_jsonc([
                'status'  => 'success',
                'message' => 'Undo success'
            ]);

        }

        public static function useAiContentTemplate()
        {
            global $wpdb;

            $post_id = intval($_POST['post_id']);

            $ai_results = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT `target_id`, `output` FROM xag_ai WHERE `input` = %s AND `target_id` = %d ORDER BY id DESC", 'PAGE_CONTENT_TEMPLATE', $post_id
                ), ARRAY_A
            );

            if (isset($ai_results['target_id'])) {

                XAGIO_MODEL_ELEMENTOR_BACKUP::set_change_type('ai_content_template');

                $elementorData = json_decode(get_post_meta($post_id, '_elementor_data', true), true);

                // Modify
                $modifiedData = json_decode($ai_results['output'], true);

                $mergedData = XAGIO_MODEL_OCW::combineFieldsIntoJson($elementorData, $modifiedData);
                $mergedData = json_encode($mergedData, JSON_UNESCAPED_UNICODE);
                $mergedData = preg_replace('/\\\\n/', ' \\\\\\\\n', $mergedData);

                update_post_meta($post_id, '_elementor_data', $mergedData);

                XAGIO_MODEL_OCW::clearElementorCache();

            }


            xagio_jsonc([
                'status'  => 'success',
                'message' => 'Successfully updated new template content'
            ]);

        }

        public static function getAiContent()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['post_id']) || !isset($_POST['title']) || !isset($_POST['description']) || !isset($_POST['h1']) || !isset($_POST['content_style']) || !isset($_POST['content_tone'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $post_id = intval($_POST['post_id']);

            $title       = sanitize_text_field(wp_unslash($_POST['title']));
            $description = sanitize_text_field(wp_unslash($_POST['description']));
            $h1          = sanitize_text_field(wp_unslash($_POST['h1']));
            $style       = sanitize_text_field(wp_unslash($_POST['content_style']));
            $tone        = sanitize_text_field(wp_unslash($_POST['content_tone']));
            $prompt_id   = intval($_POST['prompt_id']);
            $input       = "PAGE_CONTENT";

            // Check if AI request is already made
            if (self::getPageStatusAi($post_id, $input) === 'running') {
                xagio_jsonc([
                    'status'  => 'error',
                    'message' => 'AI request is already made for this page.'
                ]);
            }

            $http_code = 0;
            $result    = self::_sendAiRequest(
                $input, $prompt_id, $post_id, [
                $title,
                $description,
                $h1,
                $style,
                $tone
            ], ['post_id' => $post_id], $http_code
            );

            if ($http_code == 406) {
                xagio_jsonc([
                    'status'  => 'upgrade',
                    'message' => 'Upgrade your account to use AI features.'
                ]);
            }

            xagio_jsonc([
                'status'  => ($http_code == 200) ? 'success' : 'error',
                'message' => $result['message']
            ]);
        }

        public static function getAiSchema()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['post_id']) || !isset($_POST['seo_title_profiles']) || !isset($_POST['seo_desc_profiles']) || !isset($_POST['post_title_profiles']) || !isset($_POST['schema'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $post_id = intval($_POST['post_id']);

            $h1          = sanitize_text_field(wp_unslash($_POST['post_title_profiles']));
            $title       = sanitize_text_field(wp_unslash($_POST['seo_title_profiles']));
            $description = sanitize_text_field(wp_unslash($_POST['seo_desc_profiles']));
            $post_url    = sanitize_url(wp_unslash($_POST['post_url_profiles']));
            $logo        = sanitize_url(wp_unslash($_POST['logo_profiles']));
            $image       = sanitize_url(wp_unslash($_POST['image_profiles']));
            $phone       = sanitize_text_field(wp_unslash($_POST['business_phone']));
            $address     = sanitize_text_field(wp_unslash($_POST['business_address']));
            $city        = sanitize_text_field(wp_unslash($_POST['business_city']));
            $state       = sanitize_text_field(wp_unslash($_POST['business_state']));
            $country     = sanitize_text_field(wp_unslash($_POST['business_country']));
            $facebook    = sanitize_text_field(wp_unslash($_POST['facebook']));
            $youtube     = sanitize_text_field(wp_unslash($_POST['youtube']));
            $instagram   = sanitize_text_field(wp_unslash($_POST['instagram']));
            $linkedin    = sanitize_text_field(wp_unslash($_POST['linkedin']));
            $x           = sanitize_text_field(wp_unslash($_POST['x']));
            $tiktok      = sanitize_text_field(wp_unslash($_POST['tiktok']));
            $pinterest   = sanitize_text_field(wp_unslash($_POST['pinterest']));

            $schema_type = sanitize_text_field(wp_unslash($_POST['schema']));
            $prompt_id   = intval($_POST['prompt_id']);
            $input       = "SCHEMA";

            $unset_keys = [
                'post_title_profiles',
                'seo_title_profiles',
                'seo_desc_profiles',
                'post_url_profiles',
                'logo_profiles',
                'image_profiles',
                'business_phone',
                'business_address',
                'business_city',
                'business_state',
                'business_country',
                'facebook',
                'youtube',
                'instagram',
                'linkedin',
                'x',
                'tiktok',
                'pinterest',
                'schema',
                'action',
                'post_id',
                'prompt_id',
                '_xagio_nonce'
            ];

            foreach ($unset_keys as $key) {
                unset($_POST[$key]);
            }

            $profiles            = $_POST;
            $other_profiles_data = "";

            foreach ($profiles as $key => $value) {
                if (empty($value))
                    continue;
                $profile_name        = str_replace("_", " ", $key);
                $other_profiles_data .= "$profile_name: $value | ";
            }

            // Check if AI request is already made
            if (self::getPageStatusAi($post_id, $input) === 'running') {
                self::removeAiRequest($post_id, $input);
            }

            $args = [
                $title,
                $description,
                $h1,
                $schema_type
            ];

            if ($prompt_id == 10) {
                $args = [
                    $schema_type,
                    $h1,
                    $title,
                    $description,
                    $post_url,
                    $logo,
                    $image,
                    $phone,
                    $address,
                    $city,
                    $state,
                    $country,
                    $facebook,
                    $youtube,
                    $tiktok,
                    $linkedin,
                    $instagram,
                    $x,
                    $pinterest,
                    $other_profiles_data
                ];
            }

            $http_code = 0;
            $result    = self::_sendAiRequest(
                $input, $prompt_id, $post_id, $args, ['post_id' => $post_id], $http_code
            );

            if ($http_code == 406) {
                xagio_jsonc([
                    'status'  => 'upgrade',
                    'message' => 'Upgrade your account to use AI features.'
                ]);
            }

            xagio_jsonc([
                'status'  => ($http_code == 200) ? 'success' : 'error',
                'message' => $result['message']
            ]);
        }

        public static function getAiOutput()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['target_id']) || !isset($_POST['input'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            // Sanitize and validate input
            $target_id = intval($_POST['target_id']);
            $input     = sanitize_text_field(wp_unslash($_POST['input']));

            // Get the status of the AI request
            $status = self::getPageStatusAi($target_id, $input);

            global $wpdb;

            // Prepare the query to find the most recent 'running' entry
            $failed_check = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT `id`, `date_created` FROM `xag_ai` WHERE `target_id` = %d AND `input` = %s AND `status` = 'running' ORDER BY `id` DESC", $target_id, $input
                ), ARRAY_A
            );

            // Check if the request has been running for more than 30 minutes
            $timestamp           = strtotime($failed_check['date_created'] ?? '');
            $future_date_created = $timestamp + (30 * 60); // 30 minutes later
            $currentTime         = time();

            if ($currentTime > $future_date_created && $timestamp) {
                // If more than 30 minutes have passed, mark the request as failed
                $wpdb->update(
                    'xag_ai', [
                    'status' => 'failed',
                    'output' => 'Failed to generate, please try again.'
                ], [
                        'id'        => $failed_check['id'] ?? 0,
                        'target_id' => $target_id,
                        'input'     => $input,
                    ]
                );
            }

            if ($status === false) {
                // If not in the queue
                xagio_jsonc(['status' => 'none']);
            } elseif ($status === 'completed' || $status === 'failed') {
                // If the request is completed or failed, retrieve the output
                $output = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT `id`, `output` FROM `xag_ai` WHERE `target_id` = %d AND `input` = %s ORDER BY `id` DESC", $target_id, $input
                    ), ARRAY_A
                );
                $id     = $output['id'];

                switch ($input) {
                    case 'PAGE_CONTENT':
                        $output = stripslashes($output['output']);
                        break;
                    case 'SEO_SUGGESTIONS_MAIN_KW':
                    case 'SEO_SUGGESTIONS':
                        $output['output'] = str_replace('\n', "\n", $output['output']);
                        $output['output'] = stripslashes($output['output']);
                        $output           = json_decode($output['output'], true);
                        break;
                }

                xagio_jsonc([
                    'status' => 'completed',
                    'data'   => $output,
                    'id'     => $id
                ]);
            } else {
                // If the request is still running
                xagio_jsonc(['status' => 'running']);
            }
        }


        /**
         *  AI API results
         */
        public static function aiResults($request)
        {
            if (empty($request->get_param('input'))) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            // PAGE_CONTENT | SEO_SUGGESTIONS
            $input = wp_kses_post(wp_unslash($request->get_param('input')));

            if (method_exists('XAGIO_MODEL_AI', 'xagio_ai_' . $input)) {
                call_user_func([
                    'XAGIO_MODEL_AI',
                    'xagio_ai_' . $input
                ], $request);
            }
        }

        public static function xagio_ai_SCHEMA($request)
        {
            global $wpdb;

            if (empty($request->get_param('post_id')) || empty($request->get_param('output'))) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                } else {
                    return;
                }
            }

            $post_id = intval($request->get_param('post_id'));
            $output  = sanitize_text_field(wp_unslash($request->get_param('output')));

            if ($output == 'FAILED') {
                $wpdb->update('xag_ai', [
                    'status'       => 'failed',
                    'output'       => 'Failed to generate schema, please try again.',
                    'date_updated' => gmdate('Y-m-d H:i:s')
                ], [
                    'status'    => 'running',
                    'target_id' => $post_id,
                    'input'     => 'SCHEMA',
                ]);
                if ($request->get_param('return') !== true) {
                    xagio_json('error', 'Schema failed to generate.');
                } else {
                    return;
                }
            }

            // remove escape characters
            $output = trim(preg_replace('/\\\\/', '', $output));

            // if $output contains string "Content:", split by it and use the second part
            $output = str_replace('```json', '', $output);
            $output = str_replace('```', '', $output);

            // try to convert to array
            $output = self::safeJsonDecode($output, TRUE);

            if (!$output) {
                $wpdb->update('xag_ai', [
                    'status'       => 'failed',
                    'output'       => 'Failed to generate proper JSON schema, please try again.',
                    'date_updated' => gmdate('Y-m-d H:i:s')
                ], [
                    'status'    => 'running',
                    'target_id' => $post_id,
                    'input'     => 'SCHEMA',
                ]);
                if ($request->get_param('return') !== true) {
                    xagio_json('error', 'Schema failed to generate.');
                } else {
                    return;
                }
            }

            if (!isset($_SERVER['SERVER_NAME'])) {
                if ($request->get_param('return') !== true) {
                    xagio_json('error', 'General Error');
                } else {
                    return;
                }
            }

            XAGIO_API::apiRequest('schema_wizard', 'POST', [
                'domain'  => preg_replace('/^www\./', '', sanitize_text_field(wp_unslash($_SERVER['SERVER_NAME']))),
                'schema'  => serialize($output),
                'name'    => 'AI for ' . get_the_title($post_id) . ', ' . gmdate('Y-m-d H:i:s'),
                'post_id' => $post_id,
            ]);

            $wpdb->update('xag_ai', [
                'status'       => 'completed',
                'output'       => wp_json_encode($output, JSON_PRETTY_PRINT),
                'date_updated' => gmdate('Y-m-d H:i:s')
            ], [
                'status'    => 'running',
                'target_id' => $post_id,
                'input'     => 'SCHEMA',
            ]);

            if ($request->get_param('return') !== true) {
                xagio_json('success', 'Schema generated successfully.');
            } else {
                return;
            }

        }

        public static function xagio_ai_TEXT_CONTENT($request)
        {
            global $wpdb;

            if (empty($request->get_param('output')) || empty($request->get_param('post_id')) || empty($request->get_param('data_id')) || empty($request->get_param('page_type'))) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                } else {
                    return;
                }
            }

            $post_id   = intval($request->get_param('post_id'));
            $data_id   = sanitize_text_field($request->get_param('data_id'));
            $page_type = sanitize_text_field($request->get_param('page_type'));
            $output    = wp_kses_post(wp_unslash($request->get_param('output')));

            if ($output == 'FAILED') {

                $wpdb->query(
                    $wpdb->prepare(
                        "
        UPDATE xag_ai
        SET status = %s,
            output = %s,
            date_updated = %s
        WHERE status = 'running'
          AND target_id = %d
          AND input = 'TEXT_CONTENT'
          AND settings LIKE %s
    ", 'failed', 'Failed to generate content, please try again.', gmdate('Y-m-d H:i:s'), $post_id, "%$data_id%"
                    )
                );

                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Failed']);
                } else {
                    return;
                }
            }

            $result = $wpdb->query(
                $wpdb->prepare(
                    "
    UPDATE xag_ai
    SET status = %s,
        output = %s,
        date_updated = %s
    WHERE status = 'running'
      AND target_id = %d
      AND input = 'TEXT_CONTENT'
      AND settings LIKE %s
", 'completed', $output, gmdate('Y-m-d H:i:s'), $post_id, "%$data_id%"
                )
            );


            if ($page_type == 'elementor') {

                XAGIO_MODEL_ELEMENTOR_BACKUP::set_change_type('text_content');

                $elementor_data = get_post_meta($post_id, '_elementor_data', TRUE);
                if (!is_array($elementor_data)) {
                    $elementor_data = json_decode($elementor_data, true);
                }

                self::elementorReplaceTextById($elementor_data, $data_id, $output);

                $elementor_data = json_encode($elementor_data, JSON_UNESCAPED_UNICODE);
                $elementor_data = preg_replace('/\\\\n/', ' \\\\\\\\n', $elementor_data);

                update_post_meta($post_id, '_elementor_data', $elementor_data);

                XAGIO_MODEL_OCW::clearElementorCache();

            } else {
                // TODO make this work for regular text / gutenberg
            }

            if ($request->get_param('return') !== true) {
                wp_send_json(['data' => $result]);
            } else {
                return;
            }

        }

        public static function xagio_ai_CLUSTER($request)
        {
            global $wpdb;

            /* ──────────────────────────── 1. sanity check ──────────────────────────── */
            if (empty($request->get_param('project_id')) || empty($request->get_param('output'))) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                }
                return;
            }

            $project_id  = intval($request->get_param('project_id'));
            $output_raw  = wp_unslash($request->get_param('output'));
            $output_safe = wp_kses_post($output_raw);

            /* ──────────────────────────── 2. table names ──────────────────────────── */
            $project_table = 'xag_projects';
            $group_table   = 'xag_groups';
            $keyword_table = 'xag_keywords';

            /* ───────────────────── 3. clone the project (backup) ──────────────────── */
            $orig_proj = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT project_name, status, shared, date_created
             FROM {$project_table}
             WHERE id = %d", $project_id
                ), ARRAY_A
            );
            if (!$orig_proj) {
                if ($request->get_param('return') !== true) {
                    wp_die('Original project not found.', 'Backup Error', ['response' => 500]);
                }
                return;
            }
            $backup_name = $orig_proj['project_name'] . ' - AI Clustering Backup';
            $wpdb->insert($project_table, [
                'project_name' => $backup_name,
                'status'       => $orig_proj['status'],
                'shared'       => $orig_proj['shared'],
                'date_created' => $orig_proj['date_created'],
            ]);
            $backup_project_id = $wpdb->insert_id;

            /* ─────── 3a. copy every group/keyword to the backup project ─────── */
            $old_groups = $wpdb->get_results(
                $wpdb->prepare("SELECT * FROM {$group_table} WHERE project_id = %d", $project_id), ARRAY_A
            );
            foreach ($old_groups as $g) {
                $old_gid = $g['id'];
                unset($g['id']);
                $g['project_id'] = $backup_project_id;
                $wpdb->insert($group_table, $g);
                $backup_gid = $wpdb->insert_id;

                $kws = $wpdb->get_results(
                    $wpdb->prepare("SELECT * FROM {$keyword_table} WHERE group_id = %d", $old_gid), ARRAY_A
                );
                foreach ($kws as $kw) {
                    unset($kw['id']);
                    $kw['group_id'] = $backup_gid;
                    $wpdb->insert($keyword_table, $kw);
                }
            }

            /* ──────────────────────────── 4. parse AI output ───────────────────────── */
            $groups = [];
            $cur    = null;
            foreach (preg_split("/\r\n|\n|\r/", $output_raw) as $line) {
                $line = trim($line);
                if ($line === '')
                    continue;
                if (stripos($line, 'Group name:') === 0) {
                    $cur          = trim(substr($line, strlen('Group name:')));
                    $groups[$cur] = [];
                } elseif ($cur) {
                    $groups[$cur][] = $line;
                }
            }

            /* ─────────── 5. delete originals & build fresh clusters ─────────── */
            // collect original IDs so we can wipe them
            $old_ids = $wpdb->get_col(
                $wpdb->prepare("SELECT id FROM {$group_table} WHERE project_id = %d", $project_id)
            );
            $in_old  = !empty($old_ids) ? implode(',', array_map('intval', $old_ids)) : '0';

            $wpdb->query('START TRANSACTION');
            if ($in_old !== '0') {
                $wpdb->query("DELETE FROM {$keyword_table} WHERE group_id IN ({$in_old})");
                $wpdb->query("DELETE FROM {$group_table}  WHERE id       IN ({$in_old})");
            }

            /* ──────── 6. insert new groups, fetching metrics live per kw ──────── */
            foreach ($groups as $g_name => $kw_list) {
                $wpdb->insert($group_table, [
                    'project_id'   => $project_id,
                    'group_name'   => $g_name,
                    'date_created' => current_time('mysql'),
                ]);
                $new_gid = $wpdb->insert_id;

                foreach ($kw_list as $kw) {
                    // grab metrics directly from the backup project
                    $metric = $wpdb->get_row(
                        $wpdb->prepare(
                            "SELECT k.volume, k.cpc, k.inurl, k.intitle, k.rank
                     FROM {$keyword_table} k
                     INNER JOIN {$group_table} g ON k.group_id = g.id
                     WHERE g.project_id = %d AND k.keyword = %s
                     LIMIT 1", $backup_project_id, $kw
                        ), ARRAY_A
                    );
                    if (!$metric) {
                        $metric = [
                            'volume'  => '',
                            'cpc'     => '',
                            'inurl'   => '',
                            'intitle' => '',
                            'rank'    => ''
                        ];
                    }

                    $wpdb->insert($keyword_table, [
                        'group_id'     => $new_gid,
                        'keyword'      => $kw,
                        'volume'       => $metric['volume'],
                        'cpc'          => $metric['cpc'],
                        'inurl'        => $metric['inurl'],
                        'intitle'      => $metric['intitle'],
                        'date_created' => current_time('mysql'),
                        'position'     => 999,
                        'queued'       => 0,
                        'rank'         => $metric['rank'],
                    ]);
                }
            }
            $wpdb->query('COMMIT');

            /* ─────────────────────────── 7. finish AI job ─────────────────────────── */
            $wpdb->update('xag_ai', [
                'status'       => 'completed',
                'output'       => $output_safe,
                'date_updated' => gmdate('Y-m-d H:i:s'),
            ], [
                'status'    => 'running',
                'target_id' => $project_id,
                'input'     => 'CLUSTER',
            ]);

            if ($request->get_param('return') !== true) {
                wp_send_json(['data' => true]);
            }
        }


        public static function xagio_ai_PAGE_CONTENT($request)
        {
            global $wpdb;

            if (empty($request->get_param('post_id')) || empty($request->get_param('output'))) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                } else {
                    return;
                }
            }

            $post_id = intval($request->get_param('post_id'));
            $output  = wp_kses_post(wp_unslash($request->get_param('output')));

            if ($output == 'FAILED') {
                $wpdb->update('xag_ai', [
                    'status'       => 'failed',
                    'output'       => 'Failed to generate content, please try again.',
                    'date_updated' => gmdate('Y-m-d H:i:s')
                ], [
                    'status'    => 'running',
                    'target_id' => $post_id,
                    'input'     => 'PAGE_CONTENT',
                ]);
                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Failed']);
                } else {
                    return;
                }
            }

            // if $output contains string "Content:", split by it and use the second part
            if (strpos($output, 'Content:') !== false) {
                $output = explode('Content:', $output)[1];
            }

            // if you add esc_sql on output we will have problems with \n
            $output = $wpdb->update('xag_ai', [
                'status'       => 'completed',
                'output'       => $output,
                'date_updated' => gmdate('Y-m-d H:i:s')
            ], [
                'status'    => 'running',
                'target_id' => $post_id,
                'input'     => 'PAGE_CONTENT',
            ]);

            if ($request->get_param('return') !== true) {
                wp_send_json(['data' => $output]);
            } else {
                return;
            }

        }

        public static function xagio_ai_PAGE_CONTENT_TEMPLATE($request)
        {
            global $wpdb;

            if (empty($request->get_param('post_id')) || empty($request->get_param('output'))) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                } else {
                    return;
                }

            }

            $post_id = intval($request->get_param('post_id'));
            $output  = wp_kses_post(wp_unslash($request->get_param('output')));

            if ($output == 'FAILED') {
                $wpdb->update('xag_ai', [
                    'status'       => 'failed',
                    'output'       => 'Failed to generate content, please try again.',
                    'date_updated' => gmdate('Y-m-d H:i:s')
                ], [
                    'status'    => 'running',
                    'target_id' => $post_id,
                    'input'     => 'PAGE_CONTENT_TEMPLATE',
                ]);
                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Failed']);
                } else {
                    return;
                }
            }

            // if $output contains string "Content:", split by it and use the second part
            $output = str_replace('```json', '', $output);
            $output = str_replace('```', '', $output);

            // if you add esc_sql on output we will have problems with \n
            $output = $wpdb->update('xag_ai', [
                'status'       => 'completed',
                'output'       => $output,
                'date_updated' => gmdate('Y-m-d H:i:s')
            ], [
                'status'    => 'running',
                'target_id' => $post_id,
                'input'     => 'PAGE_CONTENT_TEMPLATE',
            ]);

            if ($request->get_param('return') !== true) {
                wp_send_json(['data' => $output]);
            } else {
                return;
            }

        }

        public static function xagio_ai_SEO_SUGGESTIONS($request)
        {
            global $wpdb;

            if (empty($request->get_param('group_id')) || empty($request->get_param('output'))) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                } else {
                    return;
                }

            }

            $group_id = intval($request->get_param('group_id'));

            // This is array output from AI API
            $output = sanitize_text_field(wp_unslash($request->get_param('output')));

            // remove escape characters
            $output         = trim(preg_replace('/\\\\/', '', $output));
            $decoded_output = json_decode($output, true);

            $output = $wpdb->update('xag_ai', [
                'status'       => 'completed',
                'output'       => esc_sql(wp_json_encode($decoded_output, JSON_PRETTY_PRINT)),
                'date_updated' => gmdate('Y-m-d H:i:s')
            ], [
                'status'    => 'running',
                'target_id' => $group_id,
                'input'     => 'SEO_SUGGESTIONS',
            ]);

            if ($request->get_param('return') !== true) {
                wp_send_json(['data' => $output]);
            } else {
                return;
            }

        }

        public static function xagio_ai_SEO_SUGGESTIONS_MAIN_KW($request)
        {
            global $wpdb;

            if (empty($request->get_param('group_id')) || empty($request->get_param('output'))) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                } else {
                    return;
                }

            }

            $group_id = intval($request->get_param('group_id'));

            // This is array output from AI API
            $output = sanitize_text_field(wp_unslash($request->get_param('output')));

            // remove escape characters
            $output         = trim(preg_replace('/\\\\/', '', $output));
            $decoded_output = json_decode($output, true);

            $output = $wpdb->update('xag_ai', [
                'status'       => 'completed',
                'output'       => esc_sql(wp_json_encode($decoded_output, JSON_PRETTY_PRINT)),
                'date_updated' => gmdate('Y-m-d H:i:s')
            ], [
                'status'    => 'running',
                'target_id' => $group_id,
                'input'     => 'SEO_SUGGESTIONS_MAIN_KW',
            ]);

            if ($request->get_param('return') !== true) {
                wp_send_json(['data' => $output]);
            } else {
                return;
            }

        }

        public static function xagio_ai_IMAGE_GEN($request)
        {
            global $wpdb;

            // 0) Load WP media/image APIs
            if (!function_exists('wp_generate_attachment_metadata')) {
                require_once ABSPATH . 'wp-admin/includes/image.php';
                require_once ABSPATH . 'wp-admin/includes/file.php';
                require_once ABSPATH . 'wp-admin/includes/media.php';
            }

            $post_id       = intval($request->get_param('target_id'));
            $attachment_id = intval($request->get_param('attachment_id'));
            $output        = sanitize_url(wp_unslash($request->get_param('output')));

            // Mark AI Job as completed
            self::markAiImageJobAsComplete('IMAGE_GEN', $post_id, $attachment_id, $output);

            // ——— 1) NEW-ATTACHMENT + PAGE-ONLY REPLACEMENT ———
            if ($post_id > 0) {
                if (empty($output)) {
                    if ($request->get_param('return') !== true) {
                        wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                    }
                    return;
                }

                // 1a) Sideload remote AI image into the Media Library, attached to $post_id
                $new_id = media_sideload_image($output, $post_id, null, 'id');
                if (is_wp_error($new_id)) {
                    if ($request->get_param('return') !== true) {
                        wp_send_json(['error' => 'Failed to sideload generated image.'], 500);
                    }
                    return;
                }
                $new_url      = wp_get_attachment_url($new_id);
                $original_url = $output;

                // 1c) Replace only on that page/post
                if (class_exists('\Elementor\Plugin') && did_action('elementor/loaded')) {
                    XAGIO_MODEL_ELEMENTOR_BACKUP::set_change_type('image_gen');

                    $elementor_data = get_post_meta($post_id, '_elementor_data', TRUE);
                    if (!is_array($elementor_data)) {
                        $elementor_data = json_decode($elementor_data, true);
                    }

                    self::elementorReplaceImageByUrl($elementor_data, $attachment_id, $new_id);

                    $elementor_data = json_encode($elementor_data, JSON_UNESCAPED_UNICODE);
                    $elementor_data = preg_replace('/\\\\n/', ' \\\\\\\\n', $elementor_data);

                    update_post_meta($post_id, '_elementor_data', $elementor_data);

                    XAGIO_MODEL_OCW::clearElementorCache();
                } else {
                    // Fallback: simple post_content swap
                    $post = get_post($post_id);
                    if ($post && strpos($post->post_content, $original_url) !== false) {
                        $new_content = str_replace($original_url, $new_url, $post->post_content);
                        wp_update_post([
                            'ID'           => $post_id,
                            'post_content' => $new_content
                        ]);
                    }
                }

                // 1d) Return the newly-attached URL & ID
                if ($request->get_param('return') !== true) {
                    wp_send_json([
                        'data' => [
                            'url'           => $new_url,
                            'attachment_id' => $new_id,
                        ],
                    ]);
                }
                return;
            }

            // ——— 3) FALLBACK: global “new AI image” insertion ———
            if (empty($output)) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                }
                return;
            }

            $tmp_file = download_url($output);
            if (is_wp_error($tmp_file)) {
                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Failed to download generated image.'], 500);
                }
                return;
            }

            $filetype = wp_check_filetype($tmp_file);
            $mime     = $filetype['type'];
            $ext      = $filetype['ext'] ?: 'png';
            $upload   = wp_upload_bits("ai-image-gen-" . uniqid() . ".{$ext}", null, file_get_contents($tmp_file));
            wp_delete_file($tmp_file);

            if (!empty($upload['error'])) {
                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Could not save file to media library.'], 500);
                }
                return;
            }

            $attachment_data = [
                'post_mime_type' => $mime,
                'post_title'     => 'AI Generated Image',
                'post_content'   => '',
                'post_status'    => 'inherit',
            ];
            $new_id          = wp_insert_attachment($attachment_data, $upload['file']);
            if (is_wp_error($new_id)) {
                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Could not insert attachment.'], 500);
                }
                return;
            }

            $meta = wp_generate_attachment_metadata($new_id, $upload['file']);
            wp_update_attachment_metadata($new_id, $meta);
            $url = wp_get_attachment_url($new_id);

            if ($request->get_param('return') !== true) {
                wp_send_json([
                    'data' => [
                        'url'           => $url,
                        'attachment_id' => $new_id,
                    ],
                ]);
            }
        }

        public static function xagio_ai_IMAGE_EDIT($request)
        {
            global $wpdb;

            // 0. Load WP media/image APIs
            if (!function_exists('wp_generate_attachment_metadata')) {
                require_once ABSPATH . 'wp-admin/includes/image.php';
                require_once ABSPATH . 'wp-admin/includes/file.php';
                require_once ABSPATH . 'wp-admin/includes/media.php';
            }

            // Params validation
            $post_id       = intval($request->get_param('target_id'));
            $attachment_id = intval($request->get_param('attachment_id'));
            $output        = sanitize_url(wp_unslash($request->get_param('output')));

            if (!$attachment_id || !$output) {
                if ($request->get_param('return') !== true) {
                    wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
                }
                return;
            }

            // Original attachment URL
            $original_url = wp_get_attachment_url($attachment_id);

            // Mark AI job completed
            self::markAiImageJobAsComplete('IMAGE_EDIT', $post_id, $attachment_id, $output);

            //
            // === NEW-ATTACHMENT BRANCH ===
            //
            if ($post_id > 0) {
                $new_attachment_id = media_sideload_image($output, $post_id, null, 'id');
                if (is_wp_error($new_attachment_id)) {
                    if ($request->get_param('return') !== true) {
                        wp_send_json(['error' => 'Failed to sideload image.'], 500);
                    }
                    return;
                }
                $new_url = wp_get_attachment_url($new_attachment_id);

                if (class_exists('\Elementor\Plugin') && did_action('elementor/loaded')) {
                    XAGIO_MODEL_ELEMENTOR_BACKUP::set_change_type('image_edit');

                    $elementor_data = get_post_meta($post_id, '_elementor_data', TRUE);
                    if (!is_array($elementor_data)) {
                        $elementor_data = json_decode($elementor_data, true);
                    }

                    self::elementorReplaceImageByUrl($elementor_data, $attachment_id, $new_attachment_id);

                    $elementor_data = json_encode($elementor_data, JSON_UNESCAPED_UNICODE);
                    $elementor_data = preg_replace('/\\\\n/', ' \\\\\\\\n', $elementor_data);

                    update_post_meta($post_id, '_elementor_data', $elementor_data);

                    XAGIO_MODEL_OCW::clearElementorCache();
                } else {
                    $post = get_post($post_id);
                    if ($post && strpos($post->post_content, $original_url) !== false) {
                        $new_content = str_replace($original_url, $new_url, $post->post_content);
                        wp_update_post([
                            'ID'           => $post_id,
                            'post_content' => $new_content
                        ]);
                    }
                }

                if ($request->get_param('return') !== true) {
                    wp_send_json(['data' => $new_url]);
                }
                return;
            }

            //
            // === OVERWRITE-ATTACHMENT BRANCH (no resizing) ===
            //
            $tmp_file = download_url($output);
            if (is_wp_error($tmp_file)) {
                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Failed to download edited image.'], 500);
                }
                return;
            }

            // Backup original file
            $file_path   = get_attached_file($attachment_id);
            $path_parts  = pathinfo($file_path);
            $backup_path = "{$path_parts['dirname']}/{$path_parts['filename']}-xag-backup.{$path_parts['extension']}";
            if (!file_exists($backup_path)) {
                copy($file_path, $backup_path);
            }

            // Replace file directly
            copy($tmp_file, $file_path);
            wp_delete_file($tmp_file);

            // Regenerate metadata & clear Elementor cache
            $new_meta = wp_generate_attachment_metadata($attachment_id, $file_path);
            if (is_wp_error($new_meta)) {
                if ($request->get_param('return') !== true) {
                    wp_send_json(['error' => 'Failed to regenerate attachment metadata.'], 500);
                }
                return;
            }
            wp_update_attachment_metadata($attachment_id, $new_meta);

            if (class_exists('\Elementor\Plugin') && did_action('elementor/loaded')) {
                try {
                    \Elementor\Plugin::instance()->files_manager->clear_cache();
                } catch (\Throwable $e) {
                    error_log('Failed to clear Elementor cache after AI edit: ' . $e->getMessage());
                }
            }

            // Return new URL
            $new_url = wp_get_attachment_url($attachment_id);
            if ($request->get_param('return') !== true) {
                wp_send_json(['data' => $new_url]);
            }
        }

        /**
         *  AI Helper functions
         */

        public static function markAiImageJobAsComplete($input, $post_id, $attachment_id, $output)
        {
            global $wpdb;

            $table   = 'xag_ai';
            $results = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT `id`, `settings` FROM {$table} WHERE `input` = %s AND `status` = 'running' AND `target_id` = %d", $input, $post_id
                ), ARRAY_A
            );

            if (empty($results)) {
                return false;
            }

            $updated = false;

            foreach ($results as $row) {
                $row_id   = intval($row['id']);
                $settings = json_decode($row['settings'], true);

                if (is_array($settings) && isset($settings['attachment_id']) && intval($settings['attachment_id']) === intval($attachment_id)) {
                    $wpdb->update(
                        $table, [
                        'status' => 'completed',
                        'output' => $output
                    ], [
                            'id' => $row_id
                        ]
                    );
                    $updated = true;
                }
            }

            return $updated;
        }

        public static function elementorReplaceTextById(&$elements, $targetId, $newText)
        {
            foreach ($elements as &$element) {
                // Found the matching element?
                if (isset($element['id']) && $element['id'] === $targetId) {
                    // Replace whichever text field exists
                    if (isset($element['settings']['editor'])) {
                        $element['settings']['editor'] = $newText;
                    } elseif (isset($element['settings']['title'])) {
                        $element['settings']['title'] = $newText;
                    } elseif (isset($element['settings']['text'])) {
                        $element['settings']['text'] = $newText;
                    }
                    return true;
                }
                // Recurse into child elements
                if (!empty($element['elements']) && is_array($element['elements'])) {
                    if (self::elementorReplaceTextById($element['elements'], $targetId, $newText)) {
                        return true;
                    }
                }
            }
            return false;
        }

        public static function elementorReplaceMapAddress(&$data, $newAddress)
        {
            if (is_array($data)) {
                foreach ($data as $key => &$value) {
                    // If key matches one of our target fields, replace its value using the current index.
                    if (is_string($key) && in_array($key, ['address'])) {
                        $value = $newAddress;
                    }

                    // If the value is an array, recurse.
                    if (is_array($value)) {
                        self::elementorReplaceMapAddress($value, $newAddress);
                    }
                }
            }

            return $data;
        }

        public static function elementorReplaceImageByUrl(&$elements, $oldUrlId, $newUrlId)
        {
            $oldUrl = wp_get_attachment_url($oldUrlId);
            $newUrl = wp_get_attachment_url($newUrlId);

            foreach ($elements as &$element) {

                // Replace image
                if (isset($element['settings']['image'])) {
                    if ($element['settings']['image']['url'] == $oldUrl || $element['settings']['image']['id'] == $oldUrlId) {
                        $element['settings']['image']['url'] = $newUrl;
                        $element['settings']['image']['id']  = $newUrlId;
                        return true;
                    }
                }

                // Replace background image
                if (isset($element['settings']['background_image'])) {
                    if ($element['settings']['background_image']['url'] == $oldUrl || $element['settings']['background_image']['id'] == $oldUrlId) {
                        $element['settings']['background_image']['url'] = $newUrl;
                        $element['settings']['background_image']['id']  = $newUrlId;
                        return true;
                    }
                }

                // Recurse into child elements
                if (!empty($element['elements']) && is_array($element['elements'])) {
                    if (self::elementorReplaceImageByUrl($element['elements'], $oldUrlId, $newUrlId)) {
                        return true;
                    }
                }
            }
            return false;
        }

        public static function getAiFrontEndOutput()
        {

            global $wpdb;

            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['ids'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $ids = sanitize_text_field(wp_unslash($_POST['ids']));
            $ids = array_filter(explode(',', $ids), 'intval');

            // Build placeholders and args
            $placeholders = implode(',', array_fill(0, count($ids), '%d'));

            $ai_statuses = $wpdb->get_results(
                $wpdb->prepare(
                    "
    SELECT *
    FROM `xag_ai`
    WHERE `id` IN ( {$placeholders} )
      AND `status` = 'completed'
", $ids
                ), ARRAY_A
            );

            if (is_array($ai_statuses)) {

                $output = [];
                foreach ($ai_statuses as $ai_status) {

                    $output[] = [
                        'id'     => absint($ai_status['id']),
                        'input'  => $ai_status['input'],
                        'output' => $ai_status['output']
                    ];

                }

                $ai_statuses = $output;

            }

            xagio_json(
                'success', 'Retrieved outputs.', $ai_statuses
            );

        }

        public static function processTextEdit()
        {
            global $wpdb;

            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['post_id'], $_POST['data_id'], $_POST['content'], $_POST['page_type'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $post_id           = intval(wp_unslash($_POST['post_id']));
            $data_id           = sanitize_text_field(wp_unslash($_POST['data_id']));
            $content           = sanitize_text_field(wp_unslash($_POST['content']));
            $page_type         = sanitize_text_field(wp_unslash($_POST['page_type']));
            $additional_prompt = sanitize_text_field(wp_unslash($_POST['additional_prompt']));

            $http_code = 0;
            $result    = self::_sendAiRequest(
                'TEXT_CONTENT', 14, $post_id, [
                $content,
                $additional_prompt
            ], [
                'post_id'   => $post_id,
                'data_id'   => $data_id,
                'page_type' => $page_type
            ], $http_code
            );

            $ID = 0;
            if ($http_code == 200) {
                $ID = $wpdb->insert_id;
            }

            xagio_json(
                ($http_code == 200) ? 'success' : 'error', ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message'], $ID
            );
        }

        public static function checkTextStatus()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['data_id'], $_POST['post_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $data_id = intval(wp_unslash($_POST['data_id']));
            $post_id = intval(wp_unslash($_POST['post_id']));


            if (!$data_id) {
                xagio_json('error', 'Failed to find data id!');
            } else {

                // Prepare and execute the query using wpdb::prepare
                $ai_status = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT `status` FROM `xag_ai` WHERE `target_id` = %d AND `input` = %s AND `status` != 'completed' AND `settings` LIKE %s ORDER BY `id` DESC", $post_id, 'TEXT_CONTENT', "%$data_id%"
                    ), ARRAY_A
                );

                xagio_json('success', 'Text Status retrieved!', isset($ai_status['status']) ? $ai_status['status'] : false);

            }

        }

        public static function processImageEditByAttachmentID()
        {

            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['attachment_id'], $_POST['action_type'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $attachment_id     = intval(wp_unslash($_POST['attachment_id']));
            $action_type       = sanitize_text_field(wp_unslash($_POST['action_type']));
            $additional_prompt = sanitize_text_field(wp_unslash($_POST['additional_prompt']));

            $post_id = 0;
            if (isset($_POST['post_id'])) {
                $post_id = intval(wp_unslash($_POST['post_id']));
            }

            $image_url = wp_get_attachment_url($attachment_id);

            $input        = 'IMAGE_EDIT';
            $input_prompt = 12;

            $args = [
                $image_url,
                $additional_prompt
            ];

            if ($action_type == 'generate') {
                $input        = 'IMAGE_GEN';
                $input_prompt = 13;

                $args = [
                    $additional_prompt
                ];
            }

            $http_code = 0;
            $result    = self::_sendAiRequest(
                $input, $input_prompt, $post_id, $args, ['attachment_id' => $attachment_id], $http_code
            );

            $ID = 0;
            if ($http_code == 200) {
                $ID = $wpdb->insert_id;
            }

            xagio_json(
                ($http_code == 200) ? 'success' : 'error', ($http_code == 406) ? 'Upgrade your account to use AI features.' : $result['message'], $ID
            );
        }

        public static function checkClusterStatus()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['project_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $project_id = intval(wp_unslash($_POST['project_id']));

            if (!$project_id) {
                xagio_json('error', 'Failed to find project!');
            } else {

                // Prepare and execute the query using wpdb::prepare
                $ai_status = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT `status` FROM `xag_ai` WHERE `target_id` = %d AND `input` = 'CLUSTER' AND `status` != 'completed' ORDER BY `id` DESC", $project_id
                    ), ARRAY_A
                );

                xagio_json('success', 'Cluster status retrieved!', isset($ai_status['status']) ? $ai_status['status'] : false);

            }

        }

        public static function checkImageStatus()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['attachment_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $attachment_id = intval(wp_unslash($_POST['attachment_id']));


            if (!$attachment_id) {
                xagio_json('error', 'Failed to find attachment!');
            } else {

                // Prepare and execute the query using wpdb::prepare
                $ai_status = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT `status` FROM `xag_ai` WHERE `target_id` = %d AND (`input` = 'IMAGE_EDIT' OR `input` = 'IMAGE_GEN') AND `status` != 'completed' ORDER BY `id` DESC", $attachment_id
                    ), ARRAY_A
                );

                xagio_json('success', 'Image Status retrieved!', isset($ai_status['status']) ? $ai_status['status'] : false);

            }

        }

        public static function getAttachmentIdByUrl()
        {

            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['image_url'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $image_url = sanitize_url(wp_unslash($_POST['image_url']));

            $attachment_id = self::getAttachmentIdByUrlFunction($image_url);

            if (!$attachment_id) {
                xagio_json('error', 'Failed to find attachment!');
            } else {
                xagio_json('success', 'Found attachment!', [
                    'image_url' => wp_get_attachment_url($attachment_id),
                    'id'        => $attachment_id
                ]);
            }

        }

        public static function getAttachmentIdByUrlFunction($image_url)
        {
            global $wpdb;

            // normalize upload base URL (ensure trailing slash)
            $upload_dir = wp_upload_dir();
            $base_url   = trailingslashit(untrailingslashit($upload_dir['baseurl']));

            // only proceed if this URL is in our uploads folder
            if (strpos($image_url, $base_url) === false) {
                return false;
            }

            // strip off any query-string or fragment
            $image_url = preg_split('/[#\?]/', $image_url)[0];

            // 0) Try WP's built-in helper first
            if (function_exists('attachment_url_to_postid')) {
                $attachment_id = attachment_url_to_postid($image_url);
                if ($attachment_id) {
                    return (int)$attachment_id;
                }
            }

            // get the path relative to uploads/
            $relative_path = ltrim(str_replace($base_url, '', $image_url), '/');

            // 1) Exact match on _wp_attached_file
            $attachment_id = $wpdb->get_var(
                $wpdb->prepare(
                    "
        SELECT post_id
          FROM {$wpdb->postmeta}
         WHERE meta_key   = '_wp_attached_file'
           AND meta_value = %s
         LIMIT 1
        ", $relative_path
                )
            );
            if ($attachment_id) {
                return (int)$attachment_id;
            }

            // parse info
            $info      = pathinfo($relative_path);
            $dir       = isset($info['dirname']) ? $info['dirname'] : '';
            $filename  = isset($info['filename']) ? $info['filename'] : '';
            $extension = isset($info['extension']) ? $info['extension'] : '';

            // 2a) Handle WP resized images: strip -WxH suffix (e.g., image-300x200.jpg)
            $clean_wpname = preg_replace('/-\d+x\d+$/', '', $filename);
            if ($clean_wpname !== $filename) {
                $orig_path_same_dir = ltrim("{$dir}/{$clean_wpname}.{$extension}", '/');
                $attachment_id      = $wpdb->get_var(
                    $wpdb->prepare(
                        "
            SELECT post_id
              FROM {$wpdb->postmeta}
             WHERE meta_key   = '_wp_attached_file'
               AND meta_value = %s
             LIMIT 1
            ", $orig_path_same_dir
                    )
                );
                if ($attachment_id) {
                    return (int)$attachment_id;
                }
            }

            // 2b) Handle Elementor-style hash suffixes: strip trailing -<hash> where hash is alphanumeric (length >=8)
            $clean_elementor_name = preg_replace('/-[a-z0-9]{8,}$/i', '', $clean_wpname);
            $original_basename    = "{$clean_elementor_name}.{$extension}";

            // 3) Fallback: loose match by basename anywhere in uploads (e.g., 2025/07/Who-We-Are.png)
            $like          = '%' . $wpdb->esc_like($original_basename) . '%';
            $attachment_id = $wpdb->get_var(
                $wpdb->prepare(
                    "
        SELECT post_id
          FROM {$wpdb->postmeta}
         WHERE meta_key   = '_wp_attached_file'
           AND meta_value LIKE %s
         ORDER BY post_id DESC
         LIMIT 1
        ", $like
                )
            );

            return $attachment_id ? (int)$attachment_id : false;
        }


        // GET Status AI - this is only for Pages and Posts
        public static function getPageStatusAi($target_id, $input)
        {
            global $wpdb;

            // Prepare and execute the query using wpdb::prepare
            $ai_status = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT `status` FROM `xag_ai` WHERE `target_id` = %d AND `input` = %s ORDER BY `id` DESC", $target_id, $input
                ), ARRAY_A
            );

            return $ai_status['status'] ?? false;
        }

        public static function removeAiRequest($target_id, $input)
        {
            global $wpdb;

            $wpdb->delete('xag_ai', [
                'target_id' => $target_id,
                'input'     => $input
            ]);

            return $wpdb->rows_affected > 0;
        }

        // UPDATE Status AI - this is only for Pages and Posts
        public static function updatePageStatusAi($target_id, $input, $status)
        {
            global $wpdb;

            $wpdb->update(
                'xag_ai', ['status' => $status], [
                    'target_id' => $target_id,
                    'input'     => $input,
                ]
            );
        }

        public static function removeallslashes($string)
        {
            $string = implode("", explode("\\", $string));
            return stripslashes(trim($string));
        }

        public static function safeJsonDecode($json, $assoc = true)
        {
            // Remove trailing commas before closing braces/brackets
            $cleaned = preg_replace('/,\s*([\]}])/', '$1', $json);

            // Decode cleaned JSON
            $decoded = json_decode($cleaned, $assoc);

            // Check for errors
            if (json_last_error() !== JSON_ERROR_NONE) {
                return NULL;
            }

            return $decoded;
        }

        public static function _sendAiRequest($input = 'PAGE_CONTENT', $prompt_id = 0, $target_id = 0, $args = [], $additional = [], &$http_code = 0)
        {
            global $wpdb;

            // Prepare request parameters
            $request_params = [
                'input'      => $input,
                'api_key'    => XAGIO_API::getAPIKey(),
                'admin_post' => XAGIO_MODEL_SETTINGS::getApiUrl(),
                'args'       => $args,
                'prompt_id'  => $prompt_id,
                'target_id'  => $target_id
            ];

            if (!empty($additional)) {
                $request_params = array_merge($request_params, $additional);
            }

            // Check if there's already a running AI request
            $status = self::getPageStatusAi($target_id, $input);

            if ($status === 'running' && $input !== 'TEXT_CONTENT') {
                self::removeAiRequest($target_id, $input);
            }

            // Initialize the output and http_code
            $http_code = 0;
            $output    = XAGIO_API::apiRequest('ai', 'POST', $request_params, $http_code);

            // If the request was successful (HTTP 200)
            if ($http_code == 200) {
                // Check if the table exists
                if (empty($wpdb->get_results("SHOW TABLES LIKE '%xag_ai%'", ARRAY_A))) {
                    self::createTable();
                }

                // Prepare the row data to be inserted
                $row = [
                    'target_id'    => $target_id,
                    'status'       => 'running',
                    'input'        => $input,
                    'settings'     => wp_json_encode($request_params, JSON_UNESCAPED_SLASHES),
                    'date_created' => gmdate('Y-m-d H:i:s')
                ];

                // Insert the data into the table
                $wpdb->insert('xag_ai', $row);
            }

            return $output;
        }

        public static function getSchemaProfiles($post_id, $title, $description, $h1, $schema_type)
        {
            $seo_profiles = get_option('XAGIO_SEO_PROFILES');

            $args = [];

            if ($seo_profiles) {
                // Create a flattened string of any non-empty values from the option
                $flattened_data = [];
                foreach ($seo_profiles as $group => $profiles) {
                    if (is_array($profiles)) {
                        foreach ($profiles as $key => $value) {
                            if (!empty($value)) {
                                // Replace underscores with spaces in the profile key
                                $flattened_data[] = str_replace('_', ' ', $key) . ': ' . $value;
                            }
                        }
                    }
                }
                $other_profiles_data = implode(' | ', $flattened_data);

                // Extract contact details if available
                $contact_details = $seo_profiles['contact_details'] ?? [];
                $phone           = isset($contact_details['business_phone']) ? sanitize_text_field($contact_details['business_phone']) : '';
                $address         = isset($contact_details['business_address']) ? sanitize_text_field($contact_details['business_address']) : '';
                $city            = isset($contact_details['business_city']) ? sanitize_text_field($contact_details['business_city']) : '';
                $state           = isset($contact_details['business_state']) ? sanitize_text_field($contact_details['business_state']) : '';
                $country         = isset($contact_details['business_country']) ? sanitize_text_field($contact_details['business_country']) : '';

                // Extract social media details if available
                $social_media = $seo_profiles['social_media'] ?? [];
                $facebook     = isset($social_media['facebook']) ? sanitize_text_field($social_media['facebook']) : '';
                $youtube      = isset($social_media['youtube']) ? sanitize_text_field($social_media['youtube']) : '';
                $instagram    = isset($social_media['instagram']) ? sanitize_text_field($social_media['instagram']) : '';
                $linkedin     = isset($social_media['linkedin']) ? sanitize_text_field($social_media['linkedin']) : '';
                $x            = isset($social_media['x']) ? sanitize_text_field($social_media['x']) : '';
                $tiktok       = isset($social_media['tiktok']) ? sanitize_text_field($social_media['tiktok']) : '';
                $pinterest    = isset($social_media['pinterest']) ? sanitize_text_field($social_media['pinterest']) : '';


                $post_url = get_permalink($post_id); // Add PAGE ID WHEN CREATED
                $logo     = get_site_icon_url();
                $image    = '';

                // Build the $args array in the same order as before
                $args = [
                    $schema_type,
                    $h1,
                    $title,
                    $description,
                    $post_url,
                    $logo,
                    $image,
                    $phone,
                    $address,
                    $city,
                    $state,
                    $country,
                    $facebook,
                    $youtube,
                    $tiktok,
                    $linkedin,
                    $instagram,
                    $x,
                    $pinterest,
                    $other_profiles_data
                ];
            } else {
                $args = [
                    $title,
                    $description,
                    $h1,
                    $schema_type
                ];
            }

            return $args;
        }


        public static function getContentProfiles($post_id)
        {
            global $wpdb;

            $title       = get_post_meta($post_id, 'XAGIO_SEO_TITLE', true);
            $description = get_post_meta($post_id, 'XAGIO_SEO_DESCRIPTION', true);

            $group = $wpdb->get_row($wpdb->prepare("SELECT * FROM xag_groups WHERE id_page_post = %d", $post_id), ARRAY_A);


            if (isset($group['h1'])) {
                $h1 = $group['h1'];
            } else {
                $h1 = get_the_title($post_id);
            }

            $list = json_encode(XAGIO_MODEL_OCW::extractFieldsFromJson(json_decode(get_post_meta($post_id, '_elementor_data', true), true)), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            if (empty($title)) {
                return [
                    'status'  => 'error',
                    'message' => 'Title is missing',
                ];
            }

            if (empty($description)) {
                return [
                    'status'  => 'error',
                    'message' => 'Description is missing',
                ];
            }

            if (empty($h1)) {
                return [
                    'status'  => 'error',
                    'message' => 'H1 is missing',
                ];
            }

            $seo_profiles = get_option('XAGIO_SEO_PROFILES');


            $keywords = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT k.keyword 
         FROM xag_keywords AS k
         INNER JOIN xag_groups AS g ON k.group_id = g.id
         WHERE g.id_page_post = %d", $post_id
                ), ARRAY_A
            );


            if (empty($keywords)) {
                $keywords = '';
            } else {
                $keywords = join(", ", array_column($keywords, 'keyword'));
            }

            $args = [
                'Based on Title, Description, H1',
                $title,
                $description,
                $h1,
                $keywords,
                $list,
                'professional',
                'pleasing'
            ];

            if ($seo_profiles) {
                // Create a flattened string of any non-empty values from the option
                $flattened_data = [];
                foreach ($seo_profiles as $group => $profiles) {
                    if (is_array($profiles)) {
                        foreach ($profiles as $key => $value) {
                            if (!empty($value)) {
                                // Replace underscores with spaces in the profile key
                                $flattened_data[] = str_replace('_', ' ', $key) . ': ' . $value;
                            }
                        }
                    }
                }
                $other_profiles_data = implode(' | ', $flattened_data);

                // Extract contact details if available
                $contact_details = $seo_profiles['contact_details'] ?? [];
                $phone           = isset($contact_details['business_phone']) ? sanitize_text_field($contact_details['business_phone']) : '';
                $address         = isset($contact_details['business_address']) ? sanitize_text_field($contact_details['business_address']) : '';
                $city            = isset($contact_details['business_city']) ? sanitize_text_field($contact_details['business_city']) : '';
                $state           = isset($contact_details['business_state']) ? sanitize_text_field($contact_details['business_state']) : '';
                $country         = isset($contact_details['business_country']) ? sanitize_text_field($contact_details['business_country']) : '';

                // Extract social media details if available
                $social_media = $seo_profiles['social_media'] ?? [];
                $facebook     = isset($social_media['facebook']) ? sanitize_text_field($social_media['facebook']) : '';
                $youtube      = isset($social_media['youtube']) ? sanitize_text_field($social_media['youtube']) : '';
                $instagram    = isset($social_media['instagram']) ? sanitize_text_field($social_media['instagram']) : '';
                $linkedin     = isset($social_media['linkedin']) ? sanitize_text_field($social_media['linkedin']) : '';
                $x            = isset($social_media['x']) ? sanitize_text_field($social_media['x']) : '';
                $tiktok       = isset($social_media['tiktok']) ? sanitize_text_field($social_media['tiktok']) : '';
                $pinterest    = isset($social_media['pinterest']) ? sanitize_text_field($social_media['pinterest']) : '';


                $post_url = get_permalink($post_id); // Add PAGE ID WHEN CREATED
                $logo     = get_site_icon_url();
                $image    = '';


                $profiles = "Post URL: $post_url, Logo: $logo, Image: $image, Phone: $phone, Address: $address, City: $city, 
                State: $state, Country: $country, Facebook: $facebook, YouTube: $youtube, Tiktok: $tiktok, LinkedIn: $linkedin, Instagram: $instagram, X: $x, Pintrest: $pinterest, Others Profiles: $other_profiles_data";

                // Build the $args array in the same order as before
                $args = [
                    $profiles,
                    $title,
                    $description,
                    $h1,
                    $keywords,
                    $list,
                    'professional',
                    'pleasing'
                ];
            }

            return $args;
        }

        public static function packKeywords($keywords)
        {
            $data = [];
            foreach ($keywords as $row) {
                $kw     = [
                    'keyword'       => $row['keyword'],
                    'search_volume' => $row['volume'],
                    'in_title'      => $row['intitle'],
                    'in_url'        => $row['inurl']
                ];
                $data[] = $kw;
            }
            return $data;
        }

        public static function createTable()
        {
            global $wpdb;
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

            $charset_collate = $wpdb->get_charset_collate();
            $creation_query  = "CREATE TABLE xag_ai (
                        id int UNSIGNED NOT NULL AUTO_INCREMENT,
                        target_id int UNSIGNED NOT NULL,
                        status enum('queued', 'running', 'completed', 'failed') NOT NULL DEFAULT 'queued',
                        input varchar(65) NOT NULL DEFAULT 'n/a',
                        settings longtext NULL,
                        output longtext NULL,
                        date_created datetime NOT NULL,
                        date_updated datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (id),
                        INDEX xagio_ai_tar_sta (target_id, status)
                    ) ENGINE=InnoDB {$charset_collate};";

            @dbDelta($creation_query);
        }

        public static function removeTable()
        {
            global $wpdb;
            // Execute the query
            $wpdb->query('DROP TABLE IF EXISTS xag_ai');
        }

    }
}