<?php
if (!defined('ABSPATH'))
    exit; // Exit if accessed directly

if (!class_exists('XAGIO_MODEL_OCW')) {

    class XAGIO_MODEL_OCW
    {
        public static $timeout = 7200;

        public static function initialize()
        {
            add_action('xagio_run_ocw_wizard', [
                'XAGIO_MODEL_OCW',
                'runWizard'
            ]);

            // Add custom cron interval
            add_filter('cron_schedules', function ($schedules) {
                $schedules['minute']      = [
                    'interval' => 60,
                    // 1 minute
                    'display'  => 'Every Minute'
                ];
                $schedules['ten_minutes'] = [
                    'interval' => 600,
                    'display'  => 'Every 10 Minutes'
                ];
                return $schedules;
            });

            if (!wp_next_scheduled('xagio_run_ocw_wizard')) {
                wp_schedule_event(time(), 'ten_minutes', 'xagio_run_ocw_wizard');
            }

            if (!XAGIO_HAS_ADMIN_PERMISSIONS)
                return;

            add_action('admin_post_xagio_ocw_step', [
                'XAGIO_MODEL_OCW',
                'saveStep'
            ]);

            add_action('admin_post_xagio_ocw_get_steps', [
                'XAGIO_MODEL_OCW',
                'getSteps'
            ]);

            add_action('admin_post_xagio_ocw_check_statuses', [
                'XAGIO_MODEL_OCW',
                'checkStatuses'
            ]);

            add_action('admin_post_xagio_ocw_save_project_id', [
                'XAGIO_MODEL_OCW',
                'saveProjectId'
            ]);

            add_action('admin_post_xagio_ocw_get_post_titles', [
                'XAGIO_MODEL_OCW',
                'getPostTitles'
            ]);

            add_action('admin_post_xagio_ocw_set_homepage', [
                'XAGIO_MODEL_OCW',
                'setHomepage'
            ]);

            add_action('admin_post_xagio_ocw_reset_wizard', [
                'XAGIO_MODEL_OCW',
                'resetWizard'
            ]);

            add_action('admin_post_xagio_ocw_get_templates', [
                'XAGIO_MODEL_OCW',
                'getTemplates'
            ]);

            add_action('admin_post_xagio_ocw_get_template', [
                'XAGIO_MODEL_OCW',
                'getTemplate'
            ]);

            add_action('admin_post_xagio_ocw_claim_template', [
                'XAGIO_MODEL_OCW',
                'claimTemplate'
            ]);
            add_action('admin_post_xagio_ocw_update_group_name', [
                'XAGIO_MODEL_OCW',
                'updateGroupLabel'
            ]);

            add_action('admin_post_xagio_ocw_install_elementor', [
                'XAGIO_MODEL_OCW',
                'installElementor'
            ]);
        }

        public static function resetWizard()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            update_option('XAGIO_OCW', [
                'step' => 'not_running',
                'data' => []
            ]);
        }

        public static function getTemplates()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            // Send API request.
            $http_code = 0;
            $templates = XAGIO_API::apiRequest('templates', 'GET', [], $http_code);

            xagio_json('success', 'Templates successfully retrieved!', $templates);
        }


        public static function getTemplate()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['template_key'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $elementor = self::checkElementorStatus();
            if (!$elementor) {
                xagio_json('error', 'Elementor is not installed on this website!');
            }

            // Sanitize the input
            $template_key = sanitize_text_field(wp_unslash($_POST['template_key']));
            $template     = XAGIO_PATH . '/templates/' . $template_key . '.zip';
            if (file_exists($template) && !XAGIO_DEV_MODE) {
                xagio_json('success', 'Template already downloaded.', XAGIO_URL . 'templates/' . $template_key . '.zip');
            } else {
                $http_code = 0;
                $result    = XAGIO_API::apiRequest('templates', 'GET', [
                    'key'  => $template_key,
                    'type' => $elementor
                ], $http_code, false, $template);

                if ($http_code == 200) {
                    xagio_json('success', 'Template downloaded.', XAGIO_URL . 'templates/' . $template_key . '.zip?ver=' . md5(microtime()));
                } else {
                    xagio_json('error', $result['message']);
                }
            }
        }

        public static function claimTemplate()
        {

            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['template_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            // Sanitize the input
            $template_id = intval($_POST['template_id']);

            // Claim template first
            // Send API request.
            $http_code = 0;
            $result    = XAGIO_API::apiRequest('templates', 'POST', [
                'id' => $template_id
            ], $http_code);

            if ($http_code == 200) {
                xagio_json('success', 'Template claimed successfully!');
            } else {
                xagio_json('error', $result['message']);
            }

        }


        public static function runWizard()
        {
            global $wpdb;
            $ocw_steps = get_option('XAGIO_OCW', [
                'step' => 'not_running',
                'data' => []
            ]);

            // Only run if wizard is active.
            if ($ocw_steps['step'] !== 'running_wizard') {
                return;
            }

            // Only run if there are no errors
            if (isset($ocw_steps['data']['error']) && !empty($ocw_steps['data']['error'])) {
                return;
            }

            // -----------------------------------------------------------
            // Competition Data (Progress 1)
            // -----------------------------------------------------------
            if ($ocw_steps['data']['progress'] == 1) {
                if (isset($ocw_steps['data']['batch_id'])) {
                    // Check if batch ID has been processed.
                    $batch_id = $ocw_steps['data']['batch_id'];
                    // Use the column "id" (as per your current schema) to check.
                    $batch = $wpdb->get_row($wpdb->prepare("SELECT `id` FROM xag_batches WHERE `batch_id` = %d", $batch_id), ARRAY_A);

                    // If waiting is active, check for timeout.
                    if (!empty($ocw_steps['data']['wait']) && $ocw_steps['data']['wait'] === true) {
                        if (!isset($ocw_steps['data']['wait_started'])) {
                            $ocw_steps['data']['wait_started'] = time();
                            update_option('XAGIO_OCW', $ocw_steps);
                        } else {
                            if (time() - $ocw_steps['data']['wait_started'] >= self::$timeout) {
                                $ocw_steps['data']['error'] = 'Timeout waiting for Competition Data processing.';
                                update_option('XAGIO_OCW', $ocw_steps);
                                return;
                            }
                        }
                    }
                    // If the batch no longer exists, competition processing is complete.
                    if (!$batch || !isset($batch['id'])) {
                        $ocw_steps['data']['progress'] = 2;
                        $ocw_steps['data']['wait']     = false;
                        unset($ocw_steps['data']['wait_started']);
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        // If the batch exists and wait flag is not set, start waiting.
                        if (empty($ocw_steps['data']['wait'])) {
                            $ocw_steps['data']['wait']         = true;
                            $ocw_steps['data']['wait_started'] = time();
                            update_option('XAGIO_OCW', $ocw_steps);
                        }
                    }
                } else {
                    // Push keywords to check competition and get Batch_ID.
                    $project_id = $ocw_steps['data']['project_id'] ?? 0;
                    $language   = $ocw_steps['data']['competition_language'] ?? '';
                    $location   = $ocw_steps['data']['competition_location'] ?? '';

                    // Rank Tracker
                    $rank_tracker_search_engine   = $ocw_steps['data']['rank_tracker']['search_engines'];
                    $rank_tracker_search_country  = $ocw_steps['data']['rank_tracker']['search_country'];
                    $rank_tracker_search_location = $ocw_steps['data']['rank_tracker']['search_location'];
                    $rank_tracker_search_locname  = $ocw_steps['data']['rank_tracker']['search_location_name'];

                    // If Location ID is present use it instead of country ID, otherwise use Country ID
                    if (!empty($rank_tracker_search_location) && $rank_tracker_search_location !== 'null') {
                        $rank_tracker_search_country = $rank_tracker_search_location;
                    }

                    // Get group IDs
                    $groups    = $wpdb->get_results($wpdb->prepare("SELECT id FROM xag_groups WHERE project_id = %d", $project_id), ARRAY_A);
                    $group_ids = array_column($groups, 'id');

                    if (empty($group_ids)) {
                        $ocw_steps['data']['error'] = 'No groups found for project.';
                        update_option('XAGIO_OCW', $ocw_steps);
                        return;
                    }

                    // Get keywords
                    $placeholders    = implode(',', array_fill(0, count($group_ids), '%d'));
                    $keywords_result = $wpdb->get_results(
                        $wpdb->prepare(
                            "SELECT id, keyword, intitle, inurl FROM xag_keywords WHERE group_id IN ($placeholders)", ...$group_ids
                        ), ARRAY_A
                    );

                    $keyword_text             = [];
                    $keyword_ids              = [];
                    $incomplete_keyword_ids   = [];
                    $incomplete_keyword_texts = [];

                    if ($keywords_result) {
                        foreach ($keywords_result as $keyword) {
                            $keyword_text[] = $keyword['keyword'];
                            $keyword_ids[]  = $keyword['id'];

                            $has_valid_metrics = ($keyword['intitle'] !== null && is_numeric($keyword['intitle']) && $keyword['inurl'] !== null && is_numeric($keyword['inurl']));

                            if (!$has_valid_metrics) {
                                $incomplete_keyword_ids[]   = $keyword['id'];
                                $incomplete_keyword_texts[] = $keyword['keyword'];
                            }
                        }
                    }

                    if (empty($keyword_text) || empty($keyword_ids)) {
                        $ocw_steps['data']['error'] = 'You must send at least one keyword to analysis.';
                        update_option('XAGIO_OCW', $ocw_steps);
                        return;
                    }

                    // Always send to Rank Tracker (all keywords)
                    $r_http_code = 0;
                    $result      = XAGIO_API::apiRequest('rank_tracker', 'POST', [
                        'url'             => site_url(),
                        'keywords'        => $keyword_text,
                        'search_engines'  => $rank_tracker_search_engine,
                        'search_location' => $rank_tracker_search_country,
                        'location_name'   => $rank_tracker_search_locname
                    ], $r_http_code);

                    if ($r_http_code != 200) {
                        XAGIO_API::apiRequest('log', 'POST', [
                            'action' => 'Agent X - Rank Tracker ERROR',
                            'data'   => json_encode($result, JSON_PRETTY_PRINT)
                        ]);
                    }

                    // Skip Competition step if nothing is incomplete
                    if (empty($incomplete_keyword_ids)) {
                        $ocw_steps['data']['progress'] = 2;
                        update_option('XAGIO_OCW', $ocw_steps);
                        return;
                    }

                    // Else send only incomplete keywords to Competition API
                    $http_code = 0;
                    $result    = XAGIO_API::apiRequest('keywords', 'POST', [
                        'keywords' => $incomplete_keyword_texts,
                        'ids'      => $incomplete_keyword_ids,
                        'language' => $language,
                        'location' => $location,
                    ], $http_code);

                    if ($http_code == 200 && isset($result['message'])) {
                        // Mark all these as queued and with -1 to signal "processing"
                        foreach ($incomplete_keyword_ids as $id) {
                            $wpdb->update('xag_keywords', [
                                'queued'  => 1,
                                'inurl'   => -1,
                                'intitle' => -1,
                            ], ['id' => $id]);
                        }

                        $BATCH_ID = $result['message'];
                        $wpdb->insert('xag_batches', [
                            'batch_id'     => $BATCH_ID,
                            'date_created' => gmdate('Y-m-d H:i:s'),
                        ]);

                        $ocw_steps['data']['batch_id']     = $BATCH_ID;
                        $ocw_steps['data']['wait']         = true;
                        $ocw_steps['data']['wait_started'] = time();
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        $ocw_steps['data']['error'] = isset($result['message']) ? $result['message'] : 'API request failed.';
                        update_option('XAGIO_OCW', $ocw_steps);
                    }
                }
            }

            // -----------------------------------------------------------
            // SEO Suggestions (Progress 2)
            // -----------------------------------------------------------
            if ($ocw_steps['data']['progress'] == 2) {
                $project_id = $ocw_steps['data']['project_id'] ?? 0;
                $groups     = $wpdb->get_results($wpdb->prepare("SELECT id FROM xag_groups WHERE project_id = %d", $project_id), ARRAY_A);
                $group_ids  = [];
                foreach ($groups as $group) {
                    $group_ids[] = $group['id'];
                }
                if (empty($group_ids)) {
                    $ocw_steps['data']['error'] = 'No groups found for project.';
                    update_option('XAGIO_OCW', $ocw_steps);
                    return;
                }

                // Check if waiting for AI response.
                if (!empty($ocw_steps['data']['wait']) && $ocw_steps['data']['wait'] === true) {
                    // Initialize wait_started if not set.
                    if (!isset($ocw_steps['data']['wait_started'])) {
                        $ocw_steps['data']['wait_started'] = time();
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        // Check if more than 30 minutes have passed.
                        if (time() - $ocw_steps['data']['wait_started'] >= self::$timeout) {
                            $ocw_steps['data']['error'] = 'Timeout waiting for SEO Suggestions.';
                            update_option('XAGIO_OCW', $ocw_steps);
                        }
                    }
                    if (XAGIO_MODEL_AI::checkAiStatusByIds($group_ids, 'SEO_SUGGESTIONS_MAIN_KW')) {
                        $ocw_steps['data']['progress'] = 3;
                        $ocw_steps['data']['wait']     = false;
                        unset($ocw_steps['data']['wait_started']);
                        update_option('XAGIO_OCW', $ocw_steps);
                    }
                } else {

                    // Truncate all "completed" AI Requests
                    $wpdb->query("DELETE FROM xag_ai WHERE `status` = 'completed'");

                    $output = XAGIO_MODEL_AI::getAiSuggestionsByGroups($group_ids);
                    if (isset($output['status']) && $output['status'] == 'error') {
                        $ocw_steps['data']['error'] = $output['message'];
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        $ocw_steps['data']['wait'] = true;
                        // Set the wait_started timer.
                        $ocw_steps['data']['wait_started'] = time();
                        update_option('XAGIO_OCW', $ocw_steps);
                    }
                }
            }

            // -----------------------------------------------------------
            // Create Posts (Progress 3)
            // -----------------------------------------------------------
            if ($ocw_steps['data']['progress'] == 3) {
                $project_id = $ocw_steps['data']['project_id'] ?? 0;
                $groups     = $wpdb->get_results($wpdb->prepare("SELECT id FROM xag_groups WHERE project_id = %d", $project_id), ARRAY_A);
                $group_ids  = [];
                foreach ($groups as $group) {
                    $group_ids[] = $group['id'];
                }
                if (empty($group_ids)) {
                    $ocw_steps['data']['error'] = 'No groups found for project.';
                    update_option('XAGIO_OCW', $ocw_steps);
                    return;
                }
                $input        = 'SEO_SUGGESTIONS_MAIN_KW';
                $placeholders = implode(',', array_fill(0, count($group_ids), '%d'));

                $ai_results = $wpdb->get_results(
                    $wpdb->prepare(
                        "
    SELECT x1.output, x1.target_id
    FROM xag_ai x1
    INNER JOIN (
        SELECT MAX(id) AS max_id
        FROM xag_ai
        WHERE input = %s AND target_id IN ($placeholders)
        GROUP BY target_id
    ) x2 ON x1.id = x2.max_id
", $input, ...$group_ids
                    ), ARRAY_A
                );

                if (!$ai_results) {
                    $ocw_steps['data']['error'] = 'No AI results found.';
                    update_option('XAGIO_OCW', $ocw_steps);
                    return;
                }

                $posts = [];

                foreach ($ai_results as $ai_result) {
                    $group_id  = $ai_result['target_id'];
                    $ai_result = $ai_result['output'];
                    $ai_result = str_replace("\\n", "\n", $ai_result);
                    $ai_result = stripslashes_deep($ai_result);
                    $data      = json_decode($ai_result, true);
                    if (!$data || !isset($data[0])) {
                        // Skip invalid JSON or unexpected structure.
                        continue;
                    }
                    $data_item = $data[0];
                    if (!isset($data_item['h1'], $data_item['title'], $data_item['description'])) {
                        continue;
                    }

                    $post_id = 0;

                    $page_type = 'Service';
                    if (isset($ocw_steps['data']['homepage_group'])) {
                        if ($ocw_steps['data']['homepage_group'] == $group_id) {
                            $page_type = 'Home';
                        }
                    }

                    $template_page = self::getTemplatePage($page_type);

                    if ((!empty($ocw_steps['data']['templates']) && $ocw_steps['data']['templates'] == 1) || $template_page !== false) {

                        // Duplicate the "Service" page
                        $post_data = [
                            'post_title'   => $data_item['h1'],
                            'post_content' => $template_page->post_content,
                            'post_status'  => 'publish',
                            'post_type'    => 'page',
                        ];
                        $post_id   = wp_insert_post($post_data);

                        if ($post_id && !is_wp_error($post_id)) {
                            // Copy all post meta
                            $meta = get_post_meta($template_page->ID);
                            foreach ($meta as $key => $values) {
                                foreach ($values as $value) {
                                    if ('_elementor_data' === $key) {
                                        $wpdb->insert(
                                            $wpdb->postmeta, [
                                            'post_id'    => $post_id,
                                            'meta_key'   => '_elementor_data',
                                            'meta_value' => $value
                                        ], [
                                                '%d',
                                                '%s',
                                                '%s'
                                            ]
                                        );
                                    } else {
                                        add_post_meta($post_id, $key, maybe_unserialize($value));
                                    }
                                }
                            }

                            // Custom SEO fields
                            update_post_meta($post_id, 'XAGIO_SEO_TITLE', $data_item['title']);
                            update_post_meta($post_id, 'XAGIO_SEO_DESCRIPTION', $data_item['description']);
                        } else {
                            $ocw_steps['data']['error'] = 'Failed to create Elementor page with H1: ' . $data_item['h1'];
                            update_option('XAGIO_OCW', $ocw_steps);
                            return;
                        }

                    } else {
                        // Default logic
                        $post_id = xagio_create_post($data_item['h1'], $data_item['title'], $data_item['description']);
                    }

                    if ($post_id && !is_wp_error($post_id)) {

                        if (isset($ocw_steps['data']['homepage_group'])) {
                            if ($ocw_steps['data']['homepage_group'] == $group_id) {
                                $ocw_steps['data']['homepage_post_id'] = $post_id;

                                // Set WordPress to show a static page on the front
                                update_option('show_on_front', 'page');

                                // Set the front page to the specified post ID
                                update_option('page_on_front', $post_id);
                            }
                        }

                        $posts[] = $post_id;
                        $wpdb->update('xag_groups', [
                            'id_page_post' => $post_id,
                            'title'        => $data_item['title'],
                            'description'  => $data_item['description'],
                            'h1'           => $data_item['h1'],
                        ], [
                            'id' => $group_id
                        ]);
                    }
                }

                self::clearElementorCache();

                if (empty($posts)) {
                    $ocw_steps['data']['error'] = 'No posts were created.';
                    update_option('XAGIO_OCW', $ocw_steps);
                    return;
                }

                // Add new pages to menu under 'Service', excluding homepage
                $menu = wp_get_nav_menu_object('main-menu');
                if (!$menu) {
                    $menu = wp_get_nav_menu_object('primary');
                }
                $menu_id = $menu ? $menu->term_id : 0;

                $menu_items      = wp_get_nav_menu_items($menu_id);
                $service_menu_id = 0;

                if ($menu_items) {
                    foreach ($menu_items as $item) {
                        if (strcasecmp($item->title, 'Service') === 0) {
                            $service_menu_id = $item->ID;
                            break;
                        }
                    }
                }

                if ($menu_id && $service_menu_id && !empty($posts)) {
                    foreach ($posts as $post_id) {
                        if (!isset($ocw_steps['data']['homepage_post_id']) || $post_id != $ocw_steps['data']['homepage_post_id']) {
                            wp_update_nav_menu_item($menu_id, 0, [
                                'menu-item-title'     => get_the_title($post_id),
                                'menu-item-object'    => 'page',
                                'menu-item-object-id' => $post_id,
                                'menu-item-type'      => 'post_type',
                                'menu-item-status'    => 'publish',
                                'menu-item-parent-id' => $service_menu_id,
                            ]);
                        }
                    }
                }

                $ocw_steps['data']['posts']    = $posts;
                $ocw_steps['data']['progress'] = 4;
                update_option('XAGIO_OCW', $ocw_steps);
            }

            // -----------------------------------------------------------
            // Create Content (Progress 4)
            // -----------------------------------------------------------
            if ($ocw_steps['data']['progress'] == 4) {
                $posts = $ocw_steps['data']['posts'] ?? [];
                if (empty($posts)) {
                    $ocw_steps['data']['error'] = 'No posts available for content creation.';
                    update_option('XAGIO_OCW', $ocw_steps);
                    return;
                }
                $input = 'PAGE_CONTENT';
                if ((!empty($ocw_steps['data']['templates']) && $ocw_steps['data']['templates'] == 1) || self::getTemplatePage() !== false) {
                    $input = 'PAGE_CONTENT_TEMPLATE';
                }

                if (!empty($ocw_steps['data']['wait']) && $ocw_steps['data']['wait'] === true) {
                    // Initialize or check wait timer.
                    if (!isset($ocw_steps['data']['wait_started'])) {
                        $ocw_steps['data']['wait_started'] = time();
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        if (time() - $ocw_steps['data']['wait_started'] >= self::$timeout) {
                            $ocw_steps['data']['error'] = 'Timeout waiting for Page Content generation.';
                            update_option('XAGIO_OCW', $ocw_steps);
                            return;
                        }
                    }
                    if (XAGIO_MODEL_AI::checkAiStatusByIds($posts, $input)) {
                        $ocw_steps['data']['progress'] = 5;
                        $ocw_steps['data']['wait']     = false;
                        unset($ocw_steps['data']['wait_started']);
                        update_option('XAGIO_OCW', $ocw_steps);

                        // Retrieve generated content.
                        $placeholders = implode(',', array_fill(0, count($posts), '%d'));
                        $ai_results   = $wpdb->get_results(
                            $wpdb->prepare(
                                "SELECT `target_id`, `output` FROM xag_ai WHERE `input` = %s AND `target_id` IN ($placeholders)", $input, ...$posts
                            ), ARRAY_A
                        );

                        foreach ($ai_results as $result) {
                            $post_id = $result['target_id'];

                            if ((!empty($ocw_steps['data']['templates']) && $ocw_steps['data']['templates'] == 1) || self::getTemplatePage() !== false) {

                                $elementorData = json_decode(get_post_meta($post_id, '_elementor_data', true), true);

                                // Modify
                                $modifiedData = json_decode($result['output'], true);

                                // Merge
                                $mergedData = self::combineFieldsIntoJson($elementorData, $modifiedData);
                                $mergedData = wp_json_encode($mergedData);

                                // Update
                                update_post_meta($post_id, '_elementor_data', wp_slash($mergedData));

                                self::clearElementorCache();

                            } else {
                                $post_content = $result['output'];
                                // Update the post with the generated content.
                                $post_data = [
                                    'ID'           => $post_id,
                                    'post_content' => $post_content,
                                ];
                                wp_update_post($post_data);
                            }
                        }
                    }
                } else {

                    $output = null;

                    if ((!empty($ocw_steps['data']['templates']) && $ocw_steps['data']['templates'] == 1) || self::getTemplatePage() !== false) {
                        $output = XAGIO_MODEL_AI::getAiContentByPostsTemplate($posts);
                    } else {
                        $output = XAGIO_MODEL_AI::getAiContentByPosts($posts);
                    }

                    if (isset($output['status']) && $output['status'] == 'error') {
                        $ocw_steps['data']['error'] = $output['message'];
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        $ocw_steps['data']['wait']         = true;
                        $ocw_steps['data']['wait_started'] = time();
                        update_option('XAGIO_OCW', $ocw_steps);
                    }
                }
            }

            // -----------------------------------------------------------
            // SEO Suggestions for Schema (Progress 5)
            // -----------------------------------------------------------
            if ($ocw_steps['data']['progress'] == 5) {
                $posts = $ocw_steps['data']['posts'] ?? [];
                if (empty($posts)) {
                    $ocw_steps['data']['error'] = 'No posts available for schema generation.';
                    update_option('XAGIO_OCW', $ocw_steps);
                    return;
                }
                if (!empty($ocw_steps['data']['wait']) && $ocw_steps['data']['wait'] === true) {
                    if (!isset($ocw_steps['data']['wait_started'])) {
                        $ocw_steps['data']['wait_started'] = time();
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        if (time() - $ocw_steps['data']['wait_started'] >= self::$timeout) {
                            $ocw_steps['data']['error'] = 'Timeout waiting for Schema generation.';
                            update_option('XAGIO_OCW', $ocw_steps);
                            return;
                        }
                    }
                    if (XAGIO_MODEL_AI::checkAiStatusByIds($posts, 'SCHEMA')) {
                        $ocw_steps['data']['progress'] = 6;
                        $ocw_steps['data']['wait']     = false;
                        unset($ocw_steps['data']['wait_started']);
                        update_option('XAGIO_OCW', $ocw_steps);
                    }
                } else {
                    $output = XAGIO_MODEL_AI::getAiSchemaByPosts($posts);
                    if (isset($output['status']) && $output['status'] == 'error') {
                        $ocw_steps['data']['error'] = $output['message'];
                        update_option('XAGIO_OCW', $ocw_steps);
                    } else {
                        $ocw_steps['data']['wait']         = true;
                        $ocw_steps['data']['wait_started'] = time();
                        update_option('XAGIO_OCW', $ocw_steps);
                    }
                }
            }

            // -----------------------------------------------------------
            // Finish
            // -----------------------------------------------------------
            if ($ocw_steps['data']['progress'] == 6) {

                // Define email recipient.
                // You can replace this with a dynamic email address (e.g., the project owner's email) if needed.
                $to = get_option('admin_email');

                // Construct the email subject and message.
                $subject  = 'Xagio Agent X Completed Successfully';
                $site_url = get_site_url();
                $message  = 'Hello,<br><br>' . "Your Agent X has successfully completed all steps on $site_url. " . 'You can now review the generated posts and content on your site.<br><br>' . 'Best regards,<br>Xagio Plugin';

                // Set email headers for HTML email.
                $headers = ['Content-Type: text/html; charset=UTF-8'];

                // Log
                XAGIO_API::apiRequest(
                    'log', 'POST', [
                        'action' => 'Agent X - Finished',
                        'data'   => json_encode($ocw_steps, JSON_PRETTY_PRINT)
                    ]
                );

                $ocw_steps['step'] = 'wizard_finished';

                // Send the email.
                if (!wp_mail($to, $subject, $message, $headers)) {
                    $ocw_steps['data']['error'] = 'Failed to send completion email.';
                }

                if ((!empty($ocw_steps['data']['templates']) && $ocw_steps['data']['templates'] == 1)) {
                    $pages = ['Service'];
                    if (isset($ocw_steps['data']['homepage_group'])) {
                        $pages[] = 'Home';
                    }
                    self::convertToDrafts($pages);
                    self::injectContactUsAddress();
                }

                update_option('XAGIO_OCW', $ocw_steps);
            }
        }

        public static function setHomepage()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['group_id'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $ocw = get_option('XAGIO_OCW', [
                'step' => 'not_running',
                'data' => []
            ]);

            // Sanitize the input
            $group_id = absint(wp_unslash($_POST['group_id']));

            if ($group_id !== 0) {

                if (isset($ocw['data']['homepage_group'])) {
                    if ($ocw['data']['homepage_group'] == $group_id) {
                        unset($ocw['data']['homepage_group']);
                        $group_id = 0;
                    } else {
                        $ocw['data']['homepage_group'] = $group_id;
                    }
                } else {
                    $ocw['data']['homepage_group'] = $group_id;
                }

                update_option('XAGIO_OCW', $ocw);

                xagio_json('success', 'Homepage updated successfully!', $group_id);
            } else {

                xagio_json('error', 'Failed to set Homepage');

            }
        }


        public static function getPostTitles()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (!isset($_POST['post_ids'])) {
                wp_die('Required parameters are missing.', 'Missing Parameters', ['response' => 400]);
            }

            $post_ids = sanitize_text_field(wp_unslash($_POST['post_ids']));
            $post_ids = explode(',', $post_ids);

            // Query only pages with those IDs (since we want page titles)
            $args = array(
                'post_type'   => 'page',
                'post__in'    => $post_ids,
                'orderby'     => 'post__in',
                // Keep them in the same order as the IDs
                'numberposts' => -1
                // Get them all
            );

            $pages  = get_posts($args);
            $output = [];
            foreach ($pages as $page) {
                $output[] = [
                    'id'    => $page->ID,
                    'title' => $page->post_title
                ];
            }


            xagio_json('success', 'Post data retrieved!', $output);
        }

        public static function saveProjectId()
        {

            $project_id = absint(wp_unslash($_POST['project_id']));

            update_option('XAGIO_OCW', [
                'step' => 'project_created',
                'data' => [
                    'project_id' => $project_id
                ]
            ]);
        }

        public static function getSteps()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            if (isset($_POST['run_wizard'])) {
                XAGIO_MODEL_OCW::runWizard();
                xagio_json('success', 'Wizard ran successfully.', get_option('XAGIO_OCW'));
            } else {
                xagio_json('success', 'Steps data retrieved!', get_option('XAGIO_OCW'));
            }

        }

        public static function checkStatuses()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            XAGIO_MODEL_AI::remoteCheckAiStatuses();

            xagio_json('success', 'No completed statuses.');
        }

        public static function saveStep()
        {
            global $wpdb;
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            $current_step = sanitize_text_field(wp_unslash($_POST['step']));

            $ocw_steps = get_option('XAGIO_OCW', [
                'step' => $current_step,
                'data' => []
            ]);

            $ocw_steps['step'] = $current_step;

            if ($current_step == 'keyword_research') {

                $remove_pages = absint(wp_unslash($_POST['remove_pages']));

                if ($remove_pages) {
                    global $wpdb;

                    $pages = get_posts([
                        'post_type'      => 'page',
                        'posts_per_page' => -1,
                        'post_status'    => 'publish',
                        'fields'         => 'ids',
                    ]);

                    foreach ($pages as $page_id) {
                        wp_delete_post($page_id, true); // true = force delete, skip trash
                        $wpdb->query($wpdb->prepare("UPDATE xag_groups SET id_page_post = 0 WHERE id_page_post = %d", $page_id));
                    }

                }

                $ocw_steps['data']['templates'] = absint(wp_unslash($_POST['templates']));

            } else if ($current_step == 'project_created') {

                $project_id                      = absint(wp_unslash($_POST['project_id']));
                $ocw_steps['data']['project_id'] = $project_id;

            } else if ($current_step == 'project_created') {

                $project_id                      = absint(wp_unslash($_POST['project_id']));
                $ocw_steps['data']['project_id'] = $project_id;

            } else if ($current_step == 'running_wizard') {

                $language = sanitize_text_field(wp_unslash($_POST['language']));
                $location = sanitize_text_field(wp_unslash($_POST['location']));

                // Rank Tracker
                $rank_tracker_search_engine   = map_deep(wp_unslash($_POST['rank_tracker_search_engine']), 'absint');
                $rank_tracker_search_country  = sanitize_text_field(wp_unslash($_POST['rank_tracker_search_country']));
                $rank_tracker_search_location = sanitize_text_field(wp_unslash($_POST['rank_tracker_search_location']));
                $rank_tracker_search_locname  = sanitize_text_field(wp_unslash($_POST['locname']));


                $progress                                  = absint(wp_unslash($_POST['progress']));
                $ocw_steps['data']['progress']             = $progress;
                $ocw_steps['data']['competition_language'] = $language;
                $ocw_steps['data']['competition_location'] = $location;
                $ocw_steps['data']['rank_tracker']         = [
                    'search_engines'       => $rank_tracker_search_engine,
                    'search_country'       => $rank_tracker_search_country,
                    'search_location'      => $rank_tracker_search_location,
                    'search_location_name' => $rank_tracker_search_locname,
                ];
                $project_id                                = $ocw_steps['data']['project_id'];

                $group_ids_delete = [];
                if (!empty($_POST['delete_groups'])) {
                    $group_ids_delete = array_map('intval', explode(',', sanitize_text_field(wp_unslash($_POST['delete_groups']))));
                }

                if (!empty($group_ids_delete)) {
                    $placeholders = implode(',', array_fill(0, count($group_ids_delete), '%d'));
                    $wpdb->query($wpdb->prepare("DELETE FROM xag_groups WHERE project_id = %d AND id IN ($placeholders)", $project_id, ...$group_ids_delete));

                    foreach ($group_ids_delete as $group_id) {
                        $wpdb->query($wpdb->prepare("DELETE FROM xag_keywords WHERE group_id = %d", $group_id));
                    }
                }

                // Log
                XAGIO_API::apiRequest(
                    'log', 'POST', [
                        'action' => 'Agent X - Start',
                        'data'   => json_encode($ocw_steps, JSON_PRETTY_PRINT)
                    ]
                );
            }

            update_option('XAGIO_OCW', $ocw_steps);
        }

        public static function updateGroupLabel()
        {
            check_ajax_referer('xagio_nonce', '_xagio_nonce');

            global $wpdb;

            if (!isset($_POST['project_id']) || !isset($_POST['group_id'])) {
                xagio_json('error', 'Please check the group id');
            }

            $project_id = intval($_POST['project_id']);
            $group_id   = intval($_POST['group_id']);

            $update_data = [
                'group_name' => isset($_POST['group_name']) ? sanitize_text_field(wp_unslash($_POST['group_name'])) : ''
            ];


            $wpdb->update('xag_groups', $update_data, [
                'id'         => $group_id,
                'project_id' => $project_id
            ]);

            xagio_json('success', 'Group Name successfully saved!');

        }

        public static function checkElementorStatus()
        {
            if (is_plugin_active('elementor/elementor.php')) {
                if (is_plugin_active('elementor-pro/elementor-pro.php')) {
                    return 'pro';
                }
                return 'free';
            }
            return false;
        }

        public static function installElementor()
        {
            // Ensure the current user can install plugins.
            if (!current_user_can('install_plugins')) {
                xagio_json('error', 'Insufficient permissions to install plugins.');
            }

            // Include necessary upgrade files.
            require_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';
            require_once ABSPATH . 'wp-admin/includes/plugin-install.php';
            require_once ABSPATH . 'wp-admin/includes/file.php';
            require_once ABSPATH . 'wp-admin/includes/misc.php';
            require_once ABSPATH . 'wp-admin/includes/plugin.php';

            self::clearElementorCache();

            // First install Elementor Free
            if (!defined('ELEMENTOR_VERSION')) {
                $plugin_slug = 'elementor';

                $api = plugins_api('plugin_information', array(
                    'slug'   => $plugin_slug,
                    'fields' => array('sections' => false),
                ));

                if (is_wp_error($api)) {
                    xagio_json('error', 'Failed to retrieve Elementor plugin info.');
                }

                $skin     = new Xagio_Silent_Upgrader_Skin();
                $upgrader = new Plugin_Upgrader($skin);
                $result   = $upgrader->install($api->download_link);

                if (is_wp_error($result)) {
                    xagio_json('error', 'Elementor free installation failed: ' . $result->get_error_message());
                }

                $activate = activate_plugin('elementor/elementor.php');
                if (is_wp_error($activate)) {
                    xagio_json('error', 'Elementor free activation failed: ' . $activate->get_error_message());
                }
            }

            // Then install Elementor Pro if not present
            if (!defined('ELEMENTOR_PRO_VERSION')) {
                $pro_url = 'https://cdn.xagio.net/elementor-pro.zip';

                $skin     = new Xagio_Silent_Upgrader_Skin();
                $upgrader = new Plugin_Upgrader($skin);
                $result   = $upgrader->install($pro_url);

                if (is_wp_error($result)) {
                    xagio_json('error', 'Elementor Pro installation failed: ' . $result->get_error_message());
                }

                $activate = activate_plugin('elementor-pro/elementor-pro.php');

                if (is_wp_error($activate)) {
                    xagio_json('error', 'Elementor Pro activation failed: ' . $activate->get_error_message());
                }
            }

            // Ensure Hello Elementor theme is installed and activated
            self::ensureHelloElementorTheme();

            // Return success response
            $status = self::checkElementorStatus();

            xagio_json('success', 'Elementor Free and Pro installed and activated successfully.', [
                'installed' => true,
                'version'   => $status,
            ]);
        }

        private static function ensureHelloElementorTheme()
        {
            $theme_slug = 'hello-elementor';

            // Check if theme is already installed
            if (!wp_get_theme($theme_slug)->exists()) {
                include_once ABSPATH . 'wp-admin/includes/theme-install.php';
                include_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';

                $api = themes_api('theme_information', [
                    'slug'   => $theme_slug,
                    'fields' => ['sections' => false]
                ]);
                if (is_wp_error($api)) {
                    xagio_json('error', 'Failed to retrieve Hello Elementor theme info.');
                }

                $skin     = new Xagio_Silent_Upgrader_Skin();
                $upgrader = new Theme_Upgrader($skin);
                $result   = $upgrader->install($api->download_link);

                if (is_wp_error($result)) {
                    xagio_json('error', 'Hello Elementor theme installation failed: ' . $result->get_error_message());
                }
            }

            // Activate theme if not already active
            if (get_stylesheet() !== $theme_slug) {
                switch_theme($theme_slug);
            }
        }

        // Function to extract specified fields into a flat array.
        public static function extractFieldsFromJson($data)
        {
            $result = [];
            self::recursiveExtract($data, $result);
            return $result;
        }

        // Helper recursive function to extract values.
        public static function recursiveExtract($data, &$result)
        {
            // List of fields to extract.
            $fields = array(
                'title',
                'title_text',
                'description',
                'description_text',
                'text',
                'editor',
                'tab_title',
                'tab_content',
                'testimonial_content',
                'item_title'
            );

            if (is_array($data)) {
                foreach ($data as $key => $value) {

                    // If the current key is one of our target fields, store its value.
                    if (is_string($key) && in_array($key, $fields)) {

                        // If the value is a string and appears to contain HTML,
                        // remove all attributes from the HTML tags.
                        if (is_string($value) && strpos($value, '<') !== false) {
                            $value = self::stripHtmlAttributes($value);
                        }

                        if (!empty($value)) {
                            if (isset($data['header_size']) && $data['header_size'] === 'h1') {
                                $value = '[Heading1] ' . $value;
                            }

                            $value    = trim($value);
                            $result[] = $value;
                        }
                    }
                    // If the value itself is an array, search inside it.
                    if (is_array($value)) {
                        self::recursiveExtract($value, $result);
                    }
                }
            }
        }

        public static function stripHtmlAttributes($html)
        {
            // Decode HTML entities so &lt;/p&gt; becomes a real </p>
            $html = html_entity_decode($html);

            $doc = new DOMDocument();
            // Suppress errors due to invalid HTML fragments.
            libxml_use_internal_errors(true);

            // Load the HTML. Using mb_convert_encoding to ensure proper handling of UTF-8.
            $doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
            libxml_clear_errors();

            // Remove attributes from every element.
            $nodes = $doc->getElementsByTagName('*');
            foreach ($nodes as $node) {
                while ($node->attributes->length) {
                    $node->removeAttribute($node->attributes->item(0)->nodeName);
                }
            }

            // Get the content of the <body> tag to avoid including <html> and <body> wrappers.
            $body      = $doc->getElementsByTagName('body')->item(0);
            $cleanHtml = '';
            foreach ($body->childNodes as $child) {
                $cleanHtml .= $doc->saveHTML($child);
            }
            return $cleanHtml;
        }

        // Function to replace extracted field values with new ones and return updated JSON.
        public static function combineFieldsIntoJson($data, $fieldsArray)
        {
            // Start with index 0 for the fields array.
            $index = 0;
            self::recursiveReplace($data, $fieldsArray, $index);
            // Return the re-encoded JSON (pretty printed for clarity).
            return $data;
        }

        // Helper recursive function to replace field values.
        public static function recursiveReplace(&$data, $fieldsArray, &$index)
        {
            $fields = array(
                'title',
                'title_text',
                'description',
                'description_text',
                'text',
                'editor',
                'tab_title',
                'tab_content',
                'testimonial_content',
                'item_title'
            );

            if (is_array($data)) {
                foreach ($data as $key => &$value) {
                    // If key matches one of our target fields, replace its value using the current index.
                    if (is_string($key) && in_array($key, $fields)) {

                        if (empty($value))
                            continue;

                        $value = trim($value);

                        if (isset($fieldsArray[$index])) {
                            $value = $fieldsArray[$index];
                            $index++;
                        }
                    }
                    // If the value is an array, recurse.
                    if (is_array($value)) {
                        self::recursiveReplace($value, $fieldsArray, $index);
                    }
                }
                unset($value); // break the reference.
            }
        }

        public static function clearElementorCache()
        {
            $wp_upload_dir = wp_upload_dir(null, false);
            $path          = $wp_upload_dir['basedir'] . '/elementor/css/*';

            foreach (glob($path) as $file_path) {
                wp_delete_file($file_path);
            }

            delete_post_meta_by_key('_elementor_css');
            delete_post_meta_by_key('_elementor_element_cache');
            delete_post_meta_by_key('_elementor_page_assets');

            delete_option('elementor-custom-breakpoints-files');

            delete_option('_elementor_assets_data');

            do_action('elementor/core/files/clear_cache');

        }

        public static function getTemplatePage($templateName = 'Service')
        {
            $statuses = [
                'publish',
                'draft'
            ]; // Include both statuses

            // First try with the original name
            $query = new WP_Query([
                'post_type'      => 'page',
                'title'          => $templateName,
                'posts_per_page' => 1,
                'post_status'    => $statuses,
            ]);

            if (!$query->have_posts()) {
                // If not found, try with the prefixed version
                wp_reset_postdata();
                $query = new WP_Query([
                    'post_type'      => 'page',
                    'title'          => 'Agent X - ' . $templateName,
                    'posts_per_page' => 1,
                    'post_status'    => $statuses,
                ]);
            }

            if ($query->have_posts()) {
                $query->the_post();
                $template_page = get_post(get_the_ID());
                wp_reset_postdata();

                if ($template_page) {
                    return $template_page;
                }
            }

            return false;
        }

        public static function injectContactUsAddress()
        {
            $contact_us = XAGIO_MODEL_OCW::getTemplatePage("Contact");

            if (!isset($contact_us->ID)) {
                $contact_us = XAGIO_MODEL_OCW::getTemplatePage("Contact Us");
            }

            if (isset($contact_us->ID)) {
                $seo_profiles = get_option('XAGIO_SEO_PROFILES');

                if ($seo_profiles) {
                    $contact_details = $seo_profiles['contact_details'] ?? [];

                    if ($contact_details) {
                        $addressParts = [];

                        if (!empty($contact_details['business_city'])) {
                            $addressParts[] = sanitize_text_field($contact_details['business_city']);
                        }

                        if (!empty($contact_details['business_state'])) {
                            $addressParts[] = sanitize_text_field($contact_details['business_state']);
                        }

                        if (!empty($contact_details['business_country'])) {
                            $addressParts[] = sanitize_text_field($contact_details['business_country']);
                        }

                        if (!empty($addressParts)) {
                            // Change value of the elementor address field
                            $value = implode(', ', $addressParts);

                            $elementor_data = get_post_meta($contact_us->ID, '_elementor_data', TRUE);
                            if (!is_array($elementor_data)) {
                                $elementor_data = json_decode($elementor_data, true);
                            }

                            $data           = XAGIO_MODEL_AI::elementorReplaceMapAddress($elementor_data, $value);
                            $elementor_data = json_encode($data);

                            update_post_meta($contact_us->ID, '_elementor_data', wp_slash($elementor_data));

                            XAGIO_MODEL_OCW::clearElementorCache();
                        }
                    }
                }
            }
        }

        public static function convertToDrafts($titles = ['Service'])
        {
            foreach ($titles as $title) {
                $query = new WP_Query([
                    'post_type'      => 'page',
                    'title'          => $title,
                    'post_status'    => [
                        'publish',
                        'private',
                        'pending'
                    ],
                    // exclude 'draft' to avoid re-processing
                    'posts_per_page' => 1,
                ]);

                if ($query->have_posts()) {
                    $query->the_post();
                    $page_id = get_the_ID();

                    // Convert to draft
                    wp_update_post([
                        'ID'          => $page_id,
                        'post_status' => 'draft',
                        'post_title'  => 'Agent X - ' . $title
                    ]);

                    wp_reset_postdata();
                }
            }
        }


        public static function createTable()
        {
            global $wpdb;
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

            $charset_collate = $wpdb->get_charset_collate();
            $creation_query  = 'CREATE TABLE xag_batches (
			        `id` int(11) NOT NULL AUTO_INCREMENT,
			        `batch_id` int(11),
			        `status` varchar(255) default "pending",		  
			        `date_created` datetime,			        
			        PRIMARY KEY  (`id`)
			    ) ' . $charset_collate . ';';
            @dbDelta($creation_query);
        }

        public static function removeTable()
        {
            global $wpdb;
            $wpdb->query('DROP TABLE IF EXISTS xag_batches;');
        }

    }

}