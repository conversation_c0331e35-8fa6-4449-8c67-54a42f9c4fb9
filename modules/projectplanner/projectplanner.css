body { padding-right: 0 !important }

:root {
    --xagio-gap-sides: 40px;
}

.wrap.prs {
    margin: 0;
}

.top-section {
    margin-inline: -40px;
}

.xag-main-header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: white;
    padding-block: 15px;
}

.wrap.prs {
    margin-top: 130px !important;
}

.xag-top-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding-inline: 40px;
}

.creating {
    text-align: center;
    font-weight: bold;
}

.creating_please_wait {
    text-align: center;
    font-size: 27px;
}

.creating_gears {
    text-align: center;
    min-height: 80px;
}

.creating_gears .one {
    position: relative;
    top: 15px;
}

.creating_gears .two {
    position: relative;
    right: 14px;
    top: -9px;
}

.creating_gears .three {
    position: relative;
    right: 34px;
    top: 22px;
}

.tr_check {
    background: #f0ffe8 !important;
}

.tr_danger {
    background: #ffe8e9 !important;
}

.postMultiButtons {
    margin-top: 23px;
}

.term_type.nth {
    background-position: -0px -66px;
    width: 20px;
    height: 20px;
}

.term_type.organic_term {
    background-position: -0px -204px;
    width: 15px;
    height: 15px;
}

.term_type.na {
    background-position: -0px -46px;
    width: 20px;
    height: 20px;
}

.term_type.local_organic {
    background-position: -0px -159px;
    width: 15px;
    height: 15px;
}

.term_type.youtube {
    background-position: -0px -219px;
    width: 15px;
    height: 15px;
}

.term_type.map_term {
    background-position: -0px -174px;
    width: 15px;
    height: 15px;
}

.map_rank.no_let {
    background-position: -18px -60px;
    width: 18px;
    height: 20px;
}

.term_type.pm {
    background-position: -0px -125px;
    width: 19px;
    height: 19px;
}

.term_type.local_finder {
    background-position: -0px -144px;
    width: 15px;
    height: 15px;
}

.text-center {
    text-align: center;
}

div#keywords_tagsinput.tagsinput {
    width: 97% !important;
}

input[type=checkbox].big_checkbox:checked:before {
    margin: 6px 0 0 1px !important;
    font-size: 21px !important;
}

input[type=checkbox].big_checkbox {
    width: 25px !important;
    height: 25px !important;
    margin-top: 0;
}

.check_box_label {
    padding-right: 5px;
}

#pr_country, #pr_city, #pr_language {
    width: 100%;
}

#rankTrackingModal #rankTrackingForm li.select2-selection__choice {
    border: none !important;
    -moz-border-radius: 2px !important;
    -webkit-border-radius: 2px !important;
    padding: 6px 15px !important;
    text-decoration: none !important;
    background: #1e4674 !important;
    color: white !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    margin: 0 !important;
    display: flex;
    gap: 5px;
}

.footerButtons {
    margin-top: 20px;
}

input.groupInput[name="group_name"] {
    margin: 0;
    height: 36px;
    font-weight: 600;
    font-size: 17px;
    padding-left: 10px;
    padding-right: 10px;
}

.keywords tbody.keywords-data tr.selected {
    background: #cee1ff !important;
}

.keywords-data tr {
    cursor: pointer;
    background: white;
}

.project-groups .uk-panel-box {
    position: relative;
    padding: 0;
}

td.url-container {
    background: white;
}

input.urlInput {
    border: 1px solid #e5e5e5;
    height: 32px;
    width: 100px;
    text-align: center;
    display: inline !important;
}

label.pre-url {
    font-size: 14px;
    margin-top: 0px;
    display: inline-block;
    color: #757575;
}

label.post-url {
    font-size: 14px;
    margin-top: -5px;
    display: inline-block;
    color: #757575;
}

.ui-sortable-helper {
    display: table;
}

/*.url-edit {*/
/*    display: inline-block;*/
/*    font-size: 15px;*/
/*    min-width: 20px;*/
/*    text-align: center;*/
/*    border-bottom: 1px solid;*/
/*    margin-left: -2px;*/
/*}*/

/*.url-container-inner {*/
/*    margin-top: 9px;*/
/*}*/

p.pagePostResultsMessage {
    text-align: center;
    font-weight: 600;
}

.pagePostResultsMessage i {
    color: orange;
}

p.edit_page_post_link {
    text-align: center;
    font-size: 15px;
    text-decoration: underline;
}

.post-container {
    padding: 5px 0;
    font-size: 18px;
}

.post-container .loading {
    text-align: center;
    font-weight: 600;
}

.post-item {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    padding: 15px;
    margin-bottom: 5px;
}

.post-item h4.post-title {
    display: inline-block;
    font-weight: 600;
    padding-left: 20px;
    margin: 0 !important;
}


.term_type {
    display: inline-block;
    background: url('../img/term_type_new.png') no-repeat;
    overflow: hidden;
    text-indent: -9999px;
    text-align: left;
    vertical-align: middle;
}

.projects-table {
    font-size: 16px;
}

.select_post_type {
    font-size: 14px;
    font-weight: bold;
    color: #23303a;
}

[data-queued="1"] .fa-sync {
    color: #FFB000 !important;
}

/*[data-target="tr"],*/
/*[data-target="ur"],*/
/*[data-target="volume"],*/
/*[data-target="cpc"],*/
/*[data-target="intitle"],*/
/*[data-target="inurl"] {*/
/*    font-family: sans-serif;*/
/*}*/

li.uk-nav-header {
    font-size: 14px;
    color: #1e4674 !important;
    font-weight: 600 !important;
    border-bottom: 1px solid #ebebeb;
    padding-bottom: 12px;
    margin-top: 5px;
}

textarea.groupInput {
    padding-top: 6px;
}

.attach-types {
    padding-left: 8px;
}

/*input.groupSelect {*/
/*    height: 24px !important;*/
/*    width: 24px !important;*/
/*    border-radius: 5px;*/
/*    margin-top: 0;*/
/*}*/


ul.groupDelete {
    list-style: circle;
    margin-top: 10px;
}

div.jqcloud {
    overflow: inherit;
    top: 0;
    right: 0;
    z-index: 100;
    border-radius: 10px;
}

div.jqcloud span {
    z-index: 2;
    cursor: pointer;
}

.cloud {
    height: 250px;
    background: #214674;
}

.highlightCloud {
    background-color: #ffff00;
    font-weight: bold;
}

.cloud.template.seen.jqcloud span.jqcloud-word.highlightWordInCloud {
    color: #feff00 !important;
}

.uk-form-warning {
    border-color: #d9ab69 !important;
    background: #fff7f8 !important;
    color: #d99025 !important;
}

#max_keywords {
    font-size: 17px;
    font-weight: bold;
}


.prs-serp {
    background: #fff;
    border: 0;
    border-radius: 10px;
    padding: 10px 20px;
    box-sizing: border-box;
    font-weight: normal;
    color: #444;
}

/*.prs-editor {*/
/*    margin-bottom: 2px;*/
/*    min-height: 21px;*/
/*    border-radius: 10px;*/
/*}*/

/*.prs-editor label.host-url {*/
/*    padding-left: 0;*/
/*}*/

/*.prs-editor.prs-title {*/
/*    color: #1a0dab;*/
/*    font-size: 20px;*/
/*    line-height: 1.3;*/
/*    word-break: break-word;*/
/*    font-family: arial, sans-serif;*/
/*    margin-top: 4px;*/
/*    margin-bottom: 6px;*/
/*}*/

.url-container .prs-editor {
    font-size: 14px;
    display: flex;
    gap: 4px;
    align-items: center;
    margin-bottom: 10px;
}

/*.prs-editor.prs-description {*/
/*    font-size: 14px;*/
/*    line-height: 1.4;*/
/*    min-height: 39px;*/
/*    max-height: 39px;*/
/*    overflow: auto;*/
/*    word-break: break-word;*/
/*    font-family: arial, sans-serif;*/
/*}*/

.prs-editor[contenteditable=true]:empty:before, .prs-h1tag[contenteditable=true]:empty:before {
    content: attr(placeholder);
    display: block; /* For Firefox */
    color: #a5a5a5;
}


.prs-editor[contenteditable=true]:hover:empty:before {
    content: "Click and type to edit...";
    color: gray;
    display: block; /* For Firefox */
}

.prs-title:hover, .prs-description:hover, .url-container:hover, .groupInput:hover {
    background: #f7f7f7;
    border-radius: 0;
    cursor: text;
}

.ps-label-block {
    display: inline-block;
    width: 100%;
}

label.ps-label {
    font-weight: 600;
    border-bottom: 1px solid #bbbbbb;
    color: #404040;
}

.ps-label i.xagio-icon.xagio-icon-info {
    color: #404f9e;
    font-size: 11px;
}

.ps-metric i.xagio-icon.xagio-icon-desktop {
    font-size: 10px;
}

.ps-metric i.xagio-icon.xagio-icon-mobile {
    font-size: 14px;
}

small.ps-metric {
    color: #333333;
    font-size: 13px;
    font-weight: 500;
    font-family: sans-serif;
    margin-bottom: 30px;
    display: block;
    background: white;
    width: auto;
}

.count-seo-container-left {
    float: left;
    margin-left: 3px;
}

.count-seo-container-right {
    float: right;
    margin-right: 3px;
}

.uk-modal-dialog.uk-modal-no-padding {
    padding: 0;
}

.uk-modal-no-padding .uk-modal-header {
    margin: 0;
}

.uk-modal-dialog.uk-modal-no-padding > .uk-close:first-child {
    margin: 10px;
}

.uk-modal-no-padding .uk-modal-footer {
    margin: 0;
}

/* UGABAGABA **/
.posts-actions2, .taxonomies-actions2, .posts-actions, .taxonomies-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}
.posts-actions2 .xagio-input-select,
.taxonomies-actions2 .xagio-input-select {
    flex-basis: 20%;
}

.posts-actions2, .taxonomies-actions2, .posts-actions-bottom2 {
    padding-bottom: 20px;
}

a.edit, a.view {
    color: #2c37e3;
}

a.attach-to-page-post, a.attach-to-taxonomy {
    color: #1dbf6a;
}
.xagio-table-modal tbody td:last-child, .xagio-table-modal tbody td:last-child abbr {
    color: #545454;
    text-decoration: none;
    border: none;
}

/*.table-actions-input {*/
/*    height: 33px;*/
/*    padding: 5px;*/
/*    width: auto !important;*/
/*    display: inline-block;*/
/*}*/

/*span.post-id {*/
/*    display: block;*/
/*    font-weight: 600;*/
/*    background: #4196ff;*/
/*    text-align: center;*/
/*    color: white;*/
/*    font-family: sans-serif;*/
/*    border-radius: 5px;*/
/*}*/

/*b.post-title {*/
/*    font-size: 16px;*/
/*    font-family: sans-serif;*/
/*}*/
.hidden {
    display: none !important;
}

tr.attached-pt {
    background: #f8f8f8;
}

table.dataTable {
    border: 0 !important;
}

.uk-accordion-title {
    border: 1px solid #ffffff !important;
    border-radius: 0 !important;
    box-shadow: 0 2px 2px #dddddd !important;
    background: #ffffff !important;
}

/*.project-groups .group.hasAttachedPost::before {*/
/*content: attr(data-post-type);*/
/*position: absolute;*/
/*top: -19px;*/
/*right: 0;*/
/*padding: 0 10px;*/
/*border: 1px solid #e5e5e5;*/
/*font-size: 13px;*/
/*text-transform: capitalize;*/
/*font-weight: 600;*/
/*border-top-left-radius: 10px;*/
/*border-top-right-radius: 10px;*/
/*background: #2f2cea;*/
/*color: white;*/
/*}*/

span.fat-warning {
    font-size: 16px;
    display: block;
    font-family: sans-serif;
    margin-top: 10px;
}

div.tagsinput span.tag {
    background: #1984f7 !important;
    color: white !important;
    border: 1px solid #2a93ff !important;
    border-radius: 6px !important;
}

div.tagsinput span.tag a {
    color: #dadada !important;
    font-size: 14px !important;
    font-family: sans-serif !important;
    font-weight: 500 !important;
    display: inline-block !important;
    margin-left: 3px !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white !important;
    margin-right: 6px !important;
}

.select2-container--default .select2-selection--single .select2-selection__clear {
    font-size: 24px;
    font-weight: 500;
}

span.select2-selection.select2-selection--multiple {
    border-color: #cccccc !important;
}

span.select2-dropdown.select2-dropdown--below,
span.select2-dropdown.select2-dropdown--above {
    border: 0;
    background: #f8f8f8;
    padding: 10px 20px;
}
span.select2-dropdown.select2-dropdown--below {
    border-radius: 0 0 10px 10px;
}
span.select2-dropdown.select2-dropdown--above {
    border-radius: 10px 10px 0 0;
}

li.select2-results__option.select2-results__message {
    margin: 0 !important;
    font-size: 13px !important;
}

li.select2-search.select2-search--inline {
    margin-bottom: 0 !important;
}

input.select2-search__field {
    padding-left: 6px !important;
    padding-top: 5px !important;
}

.uk-form input[type=search]:focus {
    background: none !important;
}

input.select2-search__field {
    margin-top: 0 !important;
}

label.uk-form-label {
    border-bottom: 0 !important;
    font-size: 13px !important;
    margin-bottom: 0px !important;
}

div#keywords_tagsinput {
    border: 1px solid #cccccc !important;
    border-radius: 5px !important;
}

.keywordInput img {
    max-width: 18px;
}

.small-separator {
    margin-bottom: 10px;
    margin-top: 15px;
}


input[name="disable_cache"][type=checkbox]:checked:before {
    margin: -4px 0 0 -4px !important;
    border-radius: 100%;
    width: 11px;
    height: 11px;
}

.silo {
    width: 5000px;
    height: 5000px;
    background: white;
}

.silo-container {
    width: 100%;
    border: 1px solid #e2e2e2;
    overflow: auto;
    background: repeating-linear-gradient(45deg, #eee, #eee 10px, #e5e5e5 10px, #e5e5e5 20px);
}

.silo-zoom {
    overflow: auto;
}

.silo_block {
    padding-bottom: 5px;
    box-shadow: 0 0 4px 2px rgba(22, 33, 74, 0.08);
    background: white;
    position: relative;
}

.silo-group-name {
    padding: 20px 35px 20px 20px;
    border-bottom: 1px solid #ececec;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.silo-page-name {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    padding: 10px 20px 5px 20px;
}

.silo-group-name, .silo-page-name {
    display: block;
    font-family: "Raleway", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.silo_move {
    position: absolute;
    font-size: 20px;
    right: 20px;
    top: 30px;
    cursor: grab;
}

.silo-pages .silo_block {
    width: 100%;
    margin-bottom: 15px;
}

.silo-container .silo_block {
    width: 300px;
    position: absolute;
}
.silo-actions {
    position: absolute;
    width: 20px;
    padding: 3px 0;
    vertical-align: middle !important;
    text-align: center;
    border: 1px solid #e2e2e2;
    border-radius: 5px;
    color: #797979;
    cursor: pointer;
}

.silo-actions:hover {
    background: whitesmoke;
    border-color: #b1b1b1;
}

.silo-remove {
    top: 5px;
    right: 5px;
}

.silo-connect {
    top: 30px;
    right: 5px;
}

.silo-view-page {
    top: 70px;
    right: 5px;
}

body > .silo_block, .silo-pages .silo_block {
    transform: none !important;
}

.silo-pages .silo-actions {
    display: none !important;
}

i.xagio-icon.xagio-icon-link-off.silo-actions.silo-connect.connecting {
    background: #00b100;
    color: white;
    border-color: #058400;
}

.silo_block.gu-transit {
    position: absolute;
}

.silo_block.swing {
    -moz-animation-name: swing;
    -webkit-animation-name: swing;
    animation-name: swing;
    -moz-animation-duration: 0.4s;
    -webkit-animation-duration: 0.4s;
    animation-duration: 0.4s;
    -moz-animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -moz-animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -moz-animation-direction: alternate;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
}

@-moz-keyframes swing {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.6;
    }
}

@-webkit-keyframes swing {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.6;
    }
}

@keyframes swing {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.6;
    }
}

.silo_block.template {
    display: none;
}

.draggable-row {
    cursor: grab !important;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.draggable_operator {
    cursor: grab;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-weight: 600;
    padding: 10px 18px 10px 18px;
    border-radius: 100vh;
    display: flex;
    align-items: center;
    gap: 12px;
}

.silo-heading {
    font-family: "Raleway", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-weight: 600;
    font-size: 25px;
    border: 1px solid gainsboro;
    margin-bottom: 10px;
    position: relative;
    padding: 10px 10px 10px 49px;
}

.paddingbox {
    width: 38px;
    height: 38px;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    padding-top: 8px;
    box-sizing: border-box;
}

.silo-heading.tag .paddingbox {
    background: #9afb7445 !important;
}

.silo-heading.category .paddingbox {
    background: #cf74fb45 !important;
}

.operator-page .flowchart-operator-title {
    background: #fafafa !important;
    /*background: #74c2fb45 !important;*/
}

.operator-post .flowchart-operator-title {
    background: #fafafa !important;
}

/*.operator-tag .flowchart-operator-title {*/
/*    background: #9afb7445;*/
/*}*/

/*.operator-category .flowchart-operator-title {*/
/*    background: #cf74fb45;*/
/*}*/

div#silo-tabs {
    margin: 0;
    border: 1px solid #e5e5e5;
}


.project-silo input.uk-form-small[type="search"] {
    width: 100%;
    margin: 0 0 10px 0;
}

.project-silo div.dataTables_wrapper div.dataTables_info {
    width: 100%;
    display: block;
    text-align: center;
    margin-bottom: 15px;
}


tr.draggable-row:hover {
    background: #cbecf7 !important;
}

.silo {
    -webkit-backface-visibility: initial !important;
    -webkit-transform-origin: 50% 50%;
    background-size: 20px 20px;
    background-image: linear-gradient(to right, #f3f3fa 1px, transparent 1px), linear-gradient(to bottom, #f3f3fa 1px, transparent 1px);
    border: 1px solid #f3f3fa;
}

.silo-categories:after, .silo-tags:after {
    content: "";
    display: table;
    clear: both;
}

.silo-tags .draggable_operator {
    background: #e1fbd6;
}

.silo-categories .hide-all-categories .add-category .draggable_operator {
    background: #cde8ff;
}

.draggable_operator {
    position: relative;
}

.selected .flowchart-operator-connector-set {
    background: #2e8acb;
}

.navigation-arrow {
    position: absolute;
    z-index: 99;
    font-size: 15px;
    opacity: 0.08;
    background: #559acc;
    color: white;
    transition: all 0.3s;
    cursor: pointer;
    border-radius: 10px;
}

.navigation-arrow:hover {
    opacity: 1;
}

.navigation-arrow[data-type="up"] {
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
}

.navigation-arrow[data-type="down"] {
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
}

.navigation-arrow[data-type="left"] {
    top: 50%;
    left: 0;
    transform: translate(0, -50%);
}

.navigation-arrow[data-type="right"] {
    top: 50%;
    right: 0;
    transform: translate(0, -50%);
}

.navigation-arrow[data-type="up"], .navigation-arrow[data-type="down"] {
    padding: 3px 20px;
}

.navigation-arrow[data-type="left"], .navigation-arrow[data-type="right"] {
    padding: 20px 3px;
}

.navigation-controls {
    position: absolute;
    height: 59px;
    width: 124px;
    margin: 10px;
}

i.flowchart-operator-remove-tag, i.flowchart-operator-remove-category {
    position: absolute;
    top: -12px;
    right: -4px;
    cursor: pointer;
    text-align: center;
    background: #fd1f36;
    color: white;
    border: none;
    border-radius: 100%;
    padding: 6px 7px;
}

.button-separator {
    float: right;
    display: block;
    font-size: 27px;
    margin: 5px 13px;
}

.width-button {
    width: 39px;
    transition: all 0.4s;
    overflow: hidden;
}

.width-button i {
    padding-right: 20px;
    transition: all 0.4s;
}

.width-button-72:hover {
    width: 72px;
}

.width-button-138:hover {
    width: 138px;
}

.width-button:hover i {
    padding-right: 0 !important;
}

.silo-permalink-input {
    width: 250px;
}

.form-table td {
    margin-bottom: 9px;
    padding: 0;
    line-height: 1.8;
    vertical-align: middle;
}

.permalink-code {
    font-size: 11px;
    overflow: hidden;
}

.wp-core-ui .button, .wp-core-ui .button-secondary {
    color: #0071a1;
    border-color: #0071a1;
    background: #f3f5f6;
    vertical-align: top;
    width: 120px;
}

.uk-form input[type=radio]:checked:before {
    content: '';
    width: 8px;
    height: 8px;
    margin: 2px;
    border-radius: 50%;
    background: #00a8e6;
}

.wp-core-ui .button-primary {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
    text-decoration: none;
    text-shadow: none;
}

.border-right-silo {
    border-right: 1px solid #e5e5e5;
}

code.permalink-code {
    display: block;
    padding: 5px 10px;
    margin-bottom: 10px;
}

.permalink-structure input[type="radio"] {
    position: absolute;
    margin: 0;
    top: 9px;
    left: 2px;
}

.permalink-structure label {
    font-size: 14px;
    font-weight: 600;
    padding-left: 20px;
}

.permalink-structure td {
    position: relative;
}

.uk-tab > li p {
    margin: 0;
}

span.flowchart-operator-title-value, span.flowchart-operator-subtitle {
    display: block;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
}

b.post-title {
    display: block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #545454;
}

.flowchart-operator.operator-external {
    background: #2b2b2b;
}

i.flowchart-operator-icon {
    position: absolute;
    z-index: 1;
    bottom: -1px;
    right: -1px;
    background: white;
    padding: 4px;
    border: 1px solid lightgrey;
    border-bottom-right-radius: 10px;
    font-size: 8px;
}

.silo-tabs .uk-tab li a:hover > i.remove-silo-name {
    display: inline-block;
}

h3.silo-links-select-name {
    display: inline-block;
    width: 430px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}

span.subtitle-404 {
    font-size: 16px;
    color: #af0b0b;
}

.operator-404 {
    background: #af0b0b !important;
}

label.phrase_keyword {
    display: block;
    margin-bottom: 5px;
}


.phraseMatchingKeywords {
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #194476;
    overflow: auto;
    max-height: 200px;
    border-radius: 10px;
}

label.include_prepositions {
    display: block;
    margin-top: 6px;
    margin-left: 1px;
}


.upload-drop {
    font-size: 16px;
    border: 1px solid #8ca2b9;
    border-radius: 10px;
    background: #fbfbfb;
    padding: 40px;
    margin: 0;
}
.upload-drop > i {
    display: block;
    font-size: 52px;
    color: #1a4674;
    margin-bottom: 13px;
}

.upload-drop .uk-form-file {
    font-size: 16px;
    margin-top: -4px !important;
    color: #1a4674;
    text-decoration: underline;
}

.csv-options {
    flex: 1;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #8ca2b9;
    text-align: center;
    cursor: pointer;
}

.max-width {
    width: 100%;
}

div#wpwrap {
    background: #f5f7fb;
}

.csv-options img {
    display: block;
    max-height: 36px;
    margin: 10px auto auto auto;
}

.pTable button.uk-button {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 1px 20px !important;
    border: none;
    margin-bottom: 3px;
}

.pTable button.uk-button:hover {
    background: #0c355d;
}

.settings-button {
    /*margin-top: 0 !important;*/
}

.projects-table select.uk-form-small {
    padding: 1px 13px !important;
    border: 1px solid #f3f3fa !important;
    border-radius: 5px !important;
    box-shadow: 0 1px 1px #dddddd !important;
    height: 40px !important;
    font-size: 15px;
    width: 52px !important;
}

.projects-table input.uk-form-small {
    padding: 15px !important;
    border: 1px solid #f3f3fa !important;
    border-radius: 5px !important;
    box-shadow: 0 1px 1px #dddddd !important;
    height: 40px !important;
}

.uk-pagination > li > a, .uk-pagination > .uk-active > span {
    padding: 5px 12px;
    height: 26px;
    box-shadow: 0 1px 1px 1px rgb(115 121 130 / 13%);
    border: none;
    border-radius: 4px !important;
    line-height: 26px;
}

.uk-pagination > .uk-disabled > span {
    padding: 5px 23px;
    height: 26px;
    box-shadow: 0 1px 1px 1px rgb(115 121 130 / 13%);
    border: none;
    border-radius: 5px !important;
    line-height: 26px;
}

.pTable tbody td {
    padding: 10px 22px;
    vertical-align: middle;
}

.projects-table .top {
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    gap: var(--xagio-gap-medium);
    justify-content: space-between;
}

.projects-table .bottom {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px 0 20px;
    flex-wrap: wrap;
    gap: var(--xagio-gap-medium);
}

.projects-table .bottom ul.uk-pagination.uk-pagination-right {
    display: flex;
    flex-wrap: wrap;
}

.pTable tfoot th {
    border: none !important;
}


.uk-modal button.uk-button, .uk-modal a.uk-button {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 4px 20px !important;
    border: none;
}

.project-dashboard button.uk-button {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 7px 20px !important;
    border: none;
}

.project-dashboard button.uk-button:hover {
    background: #2f2cea;
}

.project-dashboard button.uk-button.uk-button-mini {
    padding: 0 6px !important;
    box-shadow: 0 1px 1px #dddddd !important;
}

.action-buttons-holder .uk-button.uk-button-mini {
    min-width: 32px;
    height: 24px;
}

.project-actions .uk-button.uk-form-select {
    /*border-radius: 5px !important;*/
    /*height: 46px !important;*/
    /*line-height: 46px !important;*/
}

.posts-actions2 input[type="search"].uk-form-small, .posts-actions input[type="search"].uk-form-small {
    padding: 22px 15px !important;
    border: 1px solid #f3f3fa !important;
    border-radius: 5px !important;
    box-shadow: 0 1px 1px #dddddd !important;
    height: 40px !important
}

#addGroupFromExistingTaxonomyModal .uk-modal-dialog.uk-modal-no-padding,
#addGroupFromExistingModal .uk-modal-dialog.uk-modal-no-padding,
#attachToPagePost .uk-modal-dialog.uk-modal-no-padding,
#attachToTaxonomy .uk-modal-dialog.uk-modal-no-padding {
    width: 50%;
}

.widefat thead td, .widefat thead th {
    border-bottom: 1px solid #e4e4e4;
}

.logo-title-center {
    max-width: 100%;
    padding: 20px 40px;
}

.tr_green {
    color: #54cd8d !important;
    background: none !important;
    font-weight: bold !important;
}

.tr_red {
    color: #fb5566 !important;
    background: none !important;
    font-weight: bold !important;
}

.tr_yellow {
    color: #FFB000 !important;
    background: none !important;
    font-weight: bold !important;
}

table.keywords {
    font-size: 13px !important;
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}

table.groupSettings tr > td:first-child {
    border: none;
    width: 30px !important;
}

table.groupSettings tr > td:first-child + td {
    border: none;
}

/*.h-1-holder {*/
/*    border: 1px solid #f2f2f2;*/
/*    border-radius: 5px;*/
/*    display: inline;*/
/*    padding: 4px 7px;*/
/*    box-shadow: 0 1px 1px #dddddd;*/
/*}*/

/*.action-buttons-holder {*/
/*    position: absolute;*/
/*    right: 5px;*/
/*    top: 5px;*/
/*}*/

.updateGroup input[name="group_name"] {
    position: absolute;
    top: -34px;
    width: 30%;
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    border: 1px solid #e5e5e5 !important;
    font-size: 13px;
    height: 33px !important;
    min-height: 1px !important;
    background: #082440;
    color: #ffffff;
}

.updateGroup input[name="group_name"]:hover {
    background: #dceeff;
    color: black;
}

.data > div[data-name] {
    width: calc(50% - 20px);
    margin-bottom: 40px;
}

.data > div[data-name]:nth-child(even) {
    right: 0;
    left: unset !important;
}

/*@media (max-width:1350px) {*/
/*    .data > div[data-name] {*/
/*        width: 100%;*/
/*    }*/
/*}*/

.keywords-data tr {
    cursor: move;
}

.keywords-data tr > td:first-child {
    width: 30px;
}

.keywordInput {
    display: inline;
    cursor: pointer;
}

.drag-cursor {
    display: inline;
}

/*.attached {*/
/*    position: absolute;*/
/*    top: -25px;*/
/*    right: 0;*/
/*    padding: 0 10px;*/
/*    border: 1px solid #e5e5e5;*/
/*    font-size: 13px;*/
/*    text-transform: capitalize;*/
/*    font-weight: 600;*/
/*    border-top-left-radius: 10px;*/
/*    border-top-right-radius: 10px;*/
/*    background: #e5e5e5;*/
/*    height: 23px;*/
/*    line-height: 23px;*/
/*}*/

/*.attached a {*/
/*    color: #272727;*/
/*    text-decoration: none;*/
/*}*/

/*th.select-all {*/
/*    background: #2f2cea !important;*/
/*    font-size: 12px;*/
/*    cursor: pointer;*/
/*}*/

.uk-subnav-pill > .uk-active > * {
    background: #2f2cea !important;
}

.pTable a.uk-button.uk-button-success {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 7px 20px !important;
    border: none;
}

.pTable a.uk-button.uk-button-success:hover {
    background: #0c355d;
}

.auditWebsite_rankHolder span.select2-selection.select2-selection--multiple {
    border-radius: 5px;
    box-shadow: 0 1px 1px 1px rgb(115 121 130 / 13%);
    border: 1px solid #ececec !important;
}

select#getVolAndCpc_languageCode, select#getVolAndCpc_locationCode, select#getCompetition_languageCode, select#getCompetition_locationCode {
    width: 100%;
}

.muted-bg {
    background: #e5e5e5 !important;
}

input[type=checkbox].keyword-selection:checked::before {
    content: "";
    background-image: url(/wp-content/plugins/xagio-seo/assets/css/icons/check.svg);
    background-size: 100%;
    background-repeat: no-repeat;
    width: 100% !important;
    height: 100%;
    background-color: #1e4674;
    border-radius: 5px;
    margin: 0 !important;
}

form.updateGroup {
    z-index: 1;
    position: relative;
}

/*input[type=checkbox].groupSelect:checked::before {*/
/*    margin: -6px -8px !important;*/
/*    height: 21 !important;*/
/*    width: 35px !important;*/
/*}*/

/*.prs-h1tag {*/
/*    padding: 8px;*/
/*    font-size: 14px;*/
/*    color: #2c3338;*/
/*}*/

.ai-block {
    margin-block: 30px;
    border: 1px solid #8ca2b9;
    padding: 20px 0;
    position: relative;
    border-radius: 10px;
}
.ai-block h3 {
    position: absolute;
    top: 0;
    background: #1a4674;
    min-width: 37%;
    padding: 6px 20px;
    border-radius: 10px;
    color: white;
    transform: translateY(-50%);
    margin-bottom: 0;
}

.ai-block > h3 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.ai-content ul li:last-child {
    margin-bottom: 0;
}

.ai-content input[type="radio"] {
    border-radius: 50% !important;
    width: 20px;
    height: 20px;
    margin: 6px;
    display: grid;
    place-items: center;
    border-color: #193860;
}

.ai-content input[type="radio"]:before {
    width: 10px;
    height: 10px;
    float: none;
    background-color: #1e4674;
    margin: 0;
    border-radius: 100%;
    line-height: 0;
}

.xagio-ai-suggestion-accordion .xagio-accordion-title {
    font-size: 16px;
    background: #1a4674;
    color: white;
    border-radius: 10px;
    padding-block: 15px;
    border: 1px solid;
    position: relative;
}

.xagio-ai-suggestion-accordion .xagio-accordion-title i:last-child {
    color: white;
}

.xagio-ai-suggestion-accordion .xagio-accordion-title i:last-child {
    color: white;
}

.xagio-ai-suggestion-accordion .xagio-accordion-title::before {
    content: "";
    position: absolute;
    inset: -2px;
    border: 1px solid #8ca2b9;
    border-radius: 10px;
}

.xagio-ai-suggestion-accordion.xagio-accordion-opened .xagio-accordion-title::before {
    border-bottom: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.xagio-ai-suggestion-accordion .xagio-accordion-panel {
    padding: 0 var(--xagio-gap-medium) var(--xagio-gap-medium) var(--xagio-gap-medium);
}

.xagio-ai-suggestion-accordion.xagio-accordion-opened .xagio-accordion-content::before {
    content: "";
    position: absolute;
    inset: -1px;
    border: 1px solid #8ca2b9;
    border-top: 0;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    z-index: -1;
}

.xagio-ai-suggestion-accordion.xagio-accordion-opened .xagio-accordion-content {
    position: relative;
}

.ai-content ul {
    margin: 0 10px 0 33px;
    padding: 0;
}

.ai-content ul li {
    position: relative;
    padding: 0;
    margin: 10px 0;
    display: grid;
    grid-template-columns: max-content 1fr max-content;
    align-items: center;
}

.ai-content label {
    flex-grow: 1;
    padding: 6px 0 6px 6px;
}

.ai-content label:focus {
    outline: 1px solid #dddddd;
    border-radius: 4px;
}

button.xagio-button.modify-suggestion {
    opacity: 0;
    transition: opacity 150ms ease-in;
}

.xagio-button.swap-words {
    padding: 7px 10px;
    border-radius: 8px;
}

.ai-content ul li:focus-within .modify-suggestion {
    opacity: 1;
}

.ai-block.grad .ai-content li {
    user-select: none;
}

.ai-block.grad .ai-content li:before {
    content: "";
    inset: 0;
    -webkit-backdrop-filter: blur(29px);
    backdrop-filter: blur(29px);
    position: absolute;
    border-radius: 6px;
    overflow: hidden;
}

p.logo-paragraph {
    display: flex;
    margin-block: 30px;
    justify-content: space-between;
    align-items: center;
}

.uk-modal-header p.help {
    padding: 0;
    margin-top: 5px;
}

.ai-accordion-title {
    cursor: pointer;
    background: #f7f7f7;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 50px;
    padding: 0 15px;
    color: #5f6368;
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 0;
}

.uk-modal-header {
    background: #ffffff;
    border-bottom: none;
    margin-bottom: 0;
}

.grad {
    animation-duration: 1.2s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: placeHolderShimmer;
    animation-timing-function: cubic-bezier(.14, .75, .2, 1.01);
    background: linear-gradient(to right, #ffffff 8%, #e1e1e1 38%, #e1e1e1 38%, #ffffff 54%);
    background-size: 1000px 640px;
}

.xag-ai-tools.xag-ai-complete {
    background: #1dbf6a !important;
}

.xag-ai-failed {
    background: #7e0000 !important;
}

@keyframes placeHolderShimmer {
    0% {
        background-position: -468px 0
    }
    100% {
        background-position: 468px 0
    }
}


.sw-theme-dots > .nav .nav-link > .num {
    right: -1px;
    top: -29px;
}

.sw > .tab-content > .tab-pane {
    padding: 0;
}

.sw > .tab-content {
    overflow: visible;
}

#aiwizard {
    z-index: 100000;
    background: linear-gradient(90deg, rgb(25, 68, 118) 0%, rgb(11, 35, 63) 100%);
}
#aiwizard .uk-modal-dialog.uk-modal-dialog-blank {
    background: transparent;
}

.uk-width-max500 {
    max-width: 500px;
    margin-top: 10vh;
    margin-bottom: 10vh;
}

.xagio-width-max700 {
    position: relative;
    max-width: 835px;
    margin-top: 20px;
    padding: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
}

[class*=xagio-width] {
    box-sizing: border-box;
    width: 100%;
}

.xagio-width-max900 {
    max-width: 900px;
    margin-top: 10vh;
    margin-bottom: 50vh;
    background: white;
    padding: 50px;
    border-radius: 10px;
}

.welcome-block, .aiwizard, .aiwizard-old {
    font-family: 'Outfit', sans-serif !important;
    height: 100vh;
    min-height: 600px;
}
#aiwizard .aiwizard-wizard {
    font-family: 'Outfit', sans-serif !important;
}

.bold-middle {
    font-size: 18px;
    vertical-align: text-bottom;
    font-weight: 600;
}

p.help {
    color: #939393;
    font-size: 11px;
    margin-top: 10px;
}

b.with-underscore {
    text-decoration: underline;
    text-decoration-thickness: 2px;
}

label.uk-label-big {
    display: block;
    margin-bottom: 10px;
    font-size: 18px;
}

.creator {
    border-bottom: 1px solid #f1f1f1;
    margin-bottom: 20px;
    padding-bottom: 8px;
}

.creator input.uk-input {
    margin-bottom: 12px;
    border: 1px solid darkgrey;
    border-radius: 4px !important;
    font-size: 13px;
    display: block;
}

.creator .actions {
    margin: 5px 0 20px 0;
}

.creator > :last-child {
    margin-bottom: 20px;
}

input#search_themes, input#search_plugins {
    border: 1px solid darkgrey;
    border-radius: 4px;
    width: 100%;
}

.install {
    background: #f9f9f9;
    padding: 15px;
    border: 1px solid #e5e5e5;
    position: relative;
}

div#search_plugins_dropdown, div#search_themes_dropdown {
    position: absolute;
    background: white;
    top: 95px;
    right: 0;
    left: 0;
    overflow: auto;
    border: 1px solid #d7d7d7;
    padding: 15px;
    max-height: 315px;
}

.search-result-title small {
    display: block;
    font-size: 10px;
}

.added-install {
    background: #e9e9e9;
    padding: 20px 20px;
    margin-top: 10px;
}

.added-install img.icon {
    max-width: 39px;
}

a.stop-aiwizard {
    color: white;
    font-size: 26px;
    position: absolute;
    top: 0;
    right: 0;
}

.option-picker {
    display: flex;
    gap: 25px;
    background: white;
    padding: 40px 2rem;
    border-radius: 10px;
    transition: all 0.5s;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    min-width: 320px;
}

.option-picker:hover {
    transform: translateY(-10px);
}

.option-picker img {
    max-width: 135px;
    max-height: 78px;
}

.option-picker u {
    font-family: 'Outfit', sans-serif;
    font-weight: 600;
    text-decoration: underline;
    text-underline-offset: 10px;
    text-decoration-color: #0c2644;
    text-decoration-thickness: 2px;
}

.option-picker h3 {
    margin: 0;
    font-family: "Outfit", sans-serif;
    font-size: 35px;
    font-weight: 500;
    color: #1a4577;
}

.option-picker div {
    font-family: 'Outfit', sans-serif;
    font-size: 15px;
    color: #1a4577;
    text-align: center;
}

.ai-image-column i {
    font-size: 75px;
}

.rank_math_logo {
    background: url("../img/rank_math.webp");
}

.yoast_logo {
    background: url("../img/yoast.webp");
}

.aio_logo {
    background: url("../img/aio_logo.webp");
}

.rank_math_logo, .yoast_logo, .aio_logo {
    position: absolute;
    width: 50%;
    top: 0;
    right: 20px;
    opacity: 0.3;
    height: 100%;
    background-size: 130px;
    background-position: right;
    background-repeat: no-repeat;
}

iframe.support-iframe {
    width: 100%;
    height: 1650px;
}

.announcement {
    margin-bottom: 30px;
    border-top: 1px solid #f3f3fa;
}

.announcement:last-child {
    margin-bottom: 10px;
}

.announcement .announcement-heading {
    position: relative;
    font-family: sans-serif;
    padding: 15px 25px;
    background: #0824400a;
}

.announcement .announcement-heading span.announcement-title {
    font-size: 16px;
    font-weight: 600;
    color: #3c4366;
}

.announcement .announcement-heading span.announcement-date {
    color: rgba(60, 67, 102, 0.81);
    display: block;
    font-size: 11px;
}

.announcement .announcement-body {
    padding: 15px 25px;
    background: #fbfbfb;
    border: none !important;
    border-radius: 5px;
    box-shadow: 0 15px 40px -30px #565656;
}

p.xagio-text-center.xag-loading-plugins {
    font-size: 15px;
}

.found_plugin {
    text-align: right;
    position: absolute;
    display: inline;
    right: 3px;
    padding: 4px 8px;
    background: #38a509;
    color: white;
    border-radius: 5px;
    top: 0;
}

.rank-loading-holder {
    position: absolute;
    right: -11px;
    bottom: 0;
}

.yoast-loading-holder {
    position: absolute;
    right: 25px;
    bottom: 0;
}

.rank-loading-holder .xag-loading-rank-math {
    font-size: 13px;
    width: 149px;
}

.yoast-loading-holder .xag-loading-yoast {

    font-size: 13px;
    margin-top: -13px;
    width: 100%;
}

.aio-loading-holder {
    position: absolute;
    right: 26px;
    bottom: -13px;
}

.aio-loading-holder .xag-loading-aio {
    font-size: 13px;
    margin-top: -3px;
    width: 100%;
}

.uk-alert-heading {
    font-size: 17px;
    font-weight: 600;
}

.uk-alert.uk-alert-icon {
    padding-left: 90px;
    position: relative;
}

.uk-alert-icon > i {
    position: absolute;
    top: 20px;
    left: 23px;
    font-size: 47px;
}

ul.seo-setup li p {
    margin: 10px 0 0 0;
}

.uk-alert a {
    padding: 2px 10px;
    background: var(--primary-color-green);
    color: white !important;
    border-radius: 8px;
}

input.uk-radio.plugin-select-radio {
    border-radius: 50% !important;
    width: 20px;
    height: 20px;
    position: absolute;
    left: 5px;
    top: 9px;
}

input[type=radio].plugin-select-radio:checked::before {
    width: 14px;
    height: 14px;
    margin: 2px;
}

.aiwizard-old-wizard .uk-block-xagio:hover {
    background: #f3f3f3;
    cursor: pointer;
}

div .xagio-text-center.generating-project-loading, div .xagio-text-center.finish-aiwizard {
    font-size: 15px;
}

a.uk-button[disabled="disabled"] {
    pointer-events: none;
    cursor: default !important;
    background: #5f6e7c;
}

.uk-panel-borderless + .uk-panel-borderless {
    margin-top: 25px;
}

button.uk-button.uk-button-success.support-button {
    margin-bottom: 20px;
    width: 100%;
    font-size: 17px;
    padding: 12px;
}

.top-ten-results {
    margin-bottom: 3rem;
}

.top-ten-result {
    margin-bottom: 1.8rem;
    position: relative;
    padding: 1rem 2rem;
    font-family: 'Outfit', sans-serif;
    border: 1px solid #19447638;
    border-radius: 10px;
}

.top-ten-result > p {
    margin: 0;
}

.g-url {
    color: #202124;
    padding-bottom: 2px;
    padding-top: 1px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 80%;
    display: inline-block;
}

.g-title {
    display: block;
    font-size: 18px;
    color: #194476;
    font-weight: 600;
    padding-top: 5px;
    line-height: 1.3;
    margin-bottom: 3px;
}

p.g-desc {
    color: #202124;
    width: 85%;
}

.top-ten-search {
    display: flex;
}

.search-top-ten {
    display: flex;
    justify-content: center;
    align-items: center;
}

.search-top-ten i {
    margin-right: 9px;
}

span.website-position {
    position: absolute;
    right: 50px;
    background: #194476;
    color: white;
    padding: 5px 9px 11px 9px;
    clip-path: polygon(0 0, 100% 0%, 100% 100%, 50% 85%, 0 100%);
    border-radius: 3px;
    font-size: 17px;
    text-align: center;
    top: -3px;
}

input.select-website {
    display: grid;
    place-content: center;
    position: absolute;
    right: 35px;
    margin: 0;
    height: 23px;
    width: 23px;
    top: 50px;
}

input.select-website:before {
    width: 17px !important;
    height: 17px !important;
    margin: 2px !important;
}

input[type=radio]:checked::before {
    background-color: #194476;
}


.uk-notify {
    z-index: 100009 !important;
}

.xagio-feature {
    font-size: 17px;
    margin-bottom: 27px;
}

.xag-block.xag-first {
    border: 1px solid #ebebeb91;
    border-radius: 4px;
    background: white;
    box-shadow: 1px 1px 10px -6px #b7b7b7;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.xag-block {
    min-height: 50vh;
    padding: 2.5rem;
}

.xag-block h3 {
    margin-bottom: 30px;
}

.xag-welcome-img {
    position: absolute;
    z-index: 0;
    right: 0;
    object-fit: cover;
    mask-image: linear-gradient(to bottom, rgb(0 0 0 / 0%) 20%, rgb(0 0 0 / 86%) 100%);
    -webkit-mask-image: linear-gradient(to bottom, rgb(0 0 0 / 0%) 20%, rgb(0 0 0 / 86%) 100%);
    min-height: 47rem;
    bottom: 0;
    height: 80%;
}

.uk-block.uk-block-muted.uk-block-xagio.uk-margin-bottom {
    overflow: hidden;
}

.uk-grid.uk-grid-large {
    position: relative;
}

.xag-block-backdrop {
    background: linear-gradient(180deg, white, transparent);
    -webkit-backdrop-filter: blur(1px);
    backdrop-filter: blur(1px);
}

.xag-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 6rem 3rem;
}

.xag-block-review {
    padding: 4rem 2rem 1rem 2rem;
    border: 1px solid #ebebeb91;
    border-radius: 4px;
    box-shadow: 1px 1px 10px -6px #b7b7b7;
    position: relative;
}

.xag-block-review img {
    position: absolute;
    width: 90px;
    border-radius: 50%;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    border: 2px solid #4b4a5cb3;
    padding: 1px;
}

.xag-block-review .name span {
    font-size: 14px;
    font-weight: bold;
    color: rgb(47, 42, 234);
}

.xag-review-h2 {
    margin-bottom: 7rem;
    text-align: center;
}

img.xag-loading-cogs {
    max-width: 100px;
}

.keyword-example {
    font-size: 23px;
    font-weight: bold;
}

.keyword-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.h2-subtext {
    display: block;
    font-size: 14px;
}


/*input[type=radio]:checked::before {*/
/*    background-color: #3728ec !important;*/
/*}*/

.top-ten-result.recommended:before, .top-ten-result.not-recommended:before {
    position: absolute;
    right: 30px;
    top: -10px;
    font-size: 14px;
    font-weight: bold;
    background: white;

    padding: 4px 15px 4px 8px;
    clip-path: polygon(
            0 0,       /* Top left */
            100% 0,     /* Near top right */
            95% 50%,  /* Far top right corner, extending to middle */
            100% 100%,  /* Near bottom right */
            0 100%,    /* Bottom left */
            0% 100%    /* Mid point back to left */
    )

}

.top-ten-result.recommended:before {
    content: "RECOMMENDED";
    color: white;
    background: #194476;
}
.top-ten-result.not-recommended:before {
    color: #194476;
    background: #efefef;
    content: "NOT RECOMMENDED";
}

.top-ten-result.not-recommended .g-url, .top-ten-result.not-recommended .g-title, .top-ten-result.not-recommended .g-desc {
    color: #bbbbbb;
}

.connect-account {
    position: relative;
}

a.connect-account abbr {
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    border-bottom: 0;
}


.uk-flex {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex
}

.uk-flex-inline {
    display: -ms-inline-flexbox;
    display: -webkit-inline-flex;
    display: inline-flex
}

.uk-flex-inline > *, .uk-flex > * {
    -ms-flex-negative: 1
}

.uk-flex-top {
    -ms-flex-align: start;
    -webkit-align-items: flex-start;
    align-items: flex-start
}

.uk-flex-middle {
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center
}

.uk-flex-bottom {
    -ms-flex-align: end;
    -webkit-align-items: flex-end;
    align-items: flex-end
}

.uk-flex-center {
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center
}

.uk-flex-right {
    -ms-flex-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end
}

.uk-flex-space-between {
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.uk-flex-space-around {
    -ms-flex-pack: distribute;
    -webkit-justify-content: space-around;
    justify-content: space-around
}

.uk-flex-row-reverse {
    -ms-flex-direction: row-reverse;
    -webkit-flex-direction: row-reverse;
    flex-direction: row-reverse
}

.uk-flex-column {
    -ms-flex-direction: column;
    -webkit-flex-direction: column;
    flex-direction: column
}

.uk-flex-column-reverse {
    -ms-flex-direction: column-reverse;
    -webkit-flex-direction: column-reverse;
    flex-direction: column-reverse
}

.uk-flex-nowrap {
    -ms-flex-wrap: nowrap;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap
}

.uk-flex-wrap {
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap
}

.uk-flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse;
    -webkit-flex-wrap: wrap-reverse;
    flex-wrap: wrap-reverse
}

.uk-flex-wrap-top {
    -ms-flex-line-pack: start;
    -webkit-align-content: flex-start;
    align-content: flex-start
}

.uk-flex-wrap-middle {
    -ms-flex-line-pack: center;
    -webkit-align-content: center;
    align-content: center
}

.uk-flex-wrap-bottom {
    -ms-flex-line-pack: end;
    -webkit-align-content: flex-end;
    align-content: flex-end
}

.uk-flex-wrap-space-between {
    -ms-flex-line-pack: justify;
    -webkit-align-content: space-between;
    align-content: space-between
}

.uk-flex-wrap-space-around {
    -ms-flex-line-pack: distribute;
    -webkit-align-content: space-around;
    align-content: space-around
}

.uk-flex-order-first {
    -ms-flex-order: -1;
    -webkit-order: -1;
    order: -1
}

.uk-flex-order-last {
    -ms-flex-order: 99;
    -webkit-order: 99;
    order: 99
}

@media (min-width: 480px) {
    .uk-flex-order-first-small {
        -ms-flex-order: -1;
        -webkit-order: -1;
        order: -1
    }

    .uk-flex-order-last-small {
        -ms-flex-order: 99;
        -webkit-order: 99;
        order: 99
    }
}

@media (min-width: 768px) {
    .uk-flex-order-first-medium {
        -ms-flex-order: -1;
        -webkit-order: -1;
        order: -1
    }

    .uk-flex-order-last-medium {
        -ms-flex-order: 99;
        -webkit-order: 99;
        order: 99
    }
}

@media (min-width: 960px) {
    .uk-flex-order-first-large {
        -ms-flex-order: -1;
        -webkit-order: -1;
        order: -1
    }

    .uk-flex-order-last-large {
        -ms-flex-order: 99;
        -webkit-order: 99;
        order: 99
    }
}

@media (min-width: 1220px) {
    .uk-flex-order-first-xlarge {
        -ms-flex-order: -1;
        -webkit-order: -1;
        order: -1
    }

    .uk-flex-order-last-xlarge {
        -ms-flex-order: 99;
        -webkit-order: 99;
        order: 99
    }
}

.uk-flex-item-none {
    -ms-flex: none;
    -webkit-flex: none;
    flex: none
}

.uk-flex-item-auto {
    -ms-flex: auto;
    -webkit-flex: auto;
    flex: auto;
    -ms-flex-negative: 1
}

.uk-flex-item-1 {
    -ms-flex: 1;
    -webkit-flex: 1;
    flex: 1
}

img.heading-image {
    max-width: 320px;
}

.aiwizard-breadcrumb span.name {
    font-size: 21px;
    display: inline-block;
    margin-right: 10px;
}

.aiwizard-breadcrumb span.brand {
    font-size: 14px;
    opacity: 0.8;
}

.aiwizard-breadcrumb span.brand img.heading-image {
    max-width: 16px;
}

h2 b {
    color: var(--primary-color-green);
}

abbr {
    display: inline-block;
    color: #939393;
    font-size: 11px;
    border-bottom: 1px dashed #b9b9b9;
}

.uk-button.uk-button-custom {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 7px 20px !important;
    border: none;
    color: #fff;
    text-shadow: none;
}

.uk-button.uk-button-custom:hover {
    background: #2f2cea;
}

.uk-alert.uk-alert-custom {
    padding: 20px 30px !important;
    border-radius: 2px;
    background: #7b8dff0f;
    color: black;
}

.lds-facebook {
    display: block;
    margin: auto;
    position: relative;
    width: 80px;
    height: 80px;
}

.lds-facebook div {
    display: inline-block;
    position: absolute;
    left: 8px;
    width: 16px;
    background: #194476;
    animation: lds-facebook 1.2s cubic-bezier(0, 0.5, 0.5, 1) infinite;
}

.lds-facebook div:nth-child(1) {
    left: 8px;
    animation-delay: -0.24s;
}

.lds-facebook div:nth-child(2) {
    left: 32px;
    animation-delay: -0.12s;
}

.lds-facebook div:nth-child(3) {
    left: 56px;
    animation-delay: 0s;
}

#aiPrice span.ai-credits,
#aiPrice span.average-price {
    font-weight: bold;
    font-size: 42px;
    text-align: center;
    margin-top: 20px;
    color: #1a4674;
}

#aiPrice .input-name {
    text-decoration: underline;
}

.uk-modal .uk-button:hover {
    opacity: 0.8;
}

p.ai-heading {
    font-size: 16px;
    margin-block: 0;
}

@keyframes lds-facebook {
    0% {
        top: 8px;
        height: 64px;
    }
    50%, 100% {
        top: 24px;
        height: 32px;
    }
}

#wp-admin-bar-ai-allowance.get-ai div {
    cursor: pointer;
}

.top-ten-pagination {
    display: flex;
    /*justify-content: flex-end;*/
}

.top-ten-pagination .show-page {
    font-size: 14px;
    font-weight: bold;
    margin-inline: 2px;
    color: white;
    background: #a7b0ba;
    transition: 200ms ease-in;
    border-radius: 100vh;
    width: 32px;
    height: 32px;
    display: grid;
    place-items: center;
    border: none;
    cursor: pointer;
}

.top-ten-pagination .show-page:hover {
    background: #112d4f;
}

.top-ten-pagination .show-page.active {
    background: #173e6c;
    color: white;
}

.page-2,
.page-3,
.page-4,
.page-5,
.page-6,
.page-7,
.page-8,
.page-9,
.page-10 {
    display: none;
}

tr.drop-placeholder {
    border: 1px dashed;
    background: #f1f1bf !important;
}

.top-ten-options small {
    display: block;
}

span.select2-container.select2-container--default.select2-container--open {
    z-index: 100001;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.keyword_contain_option {
    display: flex;
    align-items: center;
}

.keyword_contain_option .main_keyword_contain {
    height: 28px !important;
    font-size: 13px;
    min-height: 28px !important;
    padding: 0 10px !important;
    flex-grow: 1;
}

.top-ten-options .prs-slider-frame {
    height: 30px !important;
}

.top-ten-options .slider-container, #auditWebsiteForm .slider-container {
    height: auto;
}

.top-ten-options p.slider-label, #auditWebsiteForm p.slider-label {
    font-size: 15px !important;
    margin-bottom: 8px !important;
    font-family: 'Outfit', sans-serif;
}

.top-ten-options .slider-container .prs-slider-frame, #auditWebsiteForm .slider-container .prs-slider-frame {
    float: none;
    width: 103px;
}

.aiwizard-type {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr 1fr;
}

.add-empty-group {
    font-size: 20px;
}

.phrase_match_exclude {
    font-size: 11px;
    color: #1a661a;
}

@media screen and (max-width: 1272px) {
    .projects-table td:nth-child(1), .projects-table th:nth-child(1) {
        display: none;
    }
}

@media screen and (max-width: 800px) {
    .projects-table td:nth-child(3), .projects-table th:nth-child(3) {
        display: none;
    }

    .hide-small {
        display: none;
    }
}

@media screen and (max-width: 578px) {
    h2.logo-title {
        font-size: 14px !important;
    }
}

ul#groupSort {
    padding: 0;
    margin: 0;
}

/*ul#groupSort li {*/
/*    background: #082440;*/
/*    border-radius: 5px !important;*/
/*    box-shadow: 0 2px 2px #dddddd;*/
/*    font-weight: normal;*/
/*    padding: 7px 20px !important;*/
/*    border: none;*/
/*    margin: 0;*/
/*    cursor: pointer;*/
/*    font-size: 20px;*/
/*    display: grid;*/
/*}*/

ul#groupSort li a {
    color: white;
    text-shadow: none;
    background: none !important;
    padding: 0;
}

/*ul#groupSort li:hover {*/
/*    background: #2f2cea !important;*/
/*}*/

li#wp-admin-bar-ai-allowance,
li#wp-admin-bar-keyword-scraping-allowance,
li#wp-admin-bar-keyword-volume-scraping-allowance,
li#wp-admin-bar-audits-allowance {
    background: #2f2cea !important;
    margin-right: 2px !important;
}

.group-to-project{
    margin-top: 10px;
    padding-bottom: 8px;
    text-align: center;
}

.failed-suggestions {
    padding: 40px 0;
    text-align: center;
}
span.keyword_volume_cost, span.keyword_competition_cost {
    font-weight: bold;
    text-decoration: underline;
}
#VolumeAndCPCForm .uk-alert p {
    margin: 0;
}
.groupSettings-button .uk-dropdown a i {
    color: #0b2440;
}
.uk-form-row .select2.select2-container {
    width: 100% !important;
}
.credits-flex {
    display: flex;
    align-items: center;
    gap: 16px;
}
.progress {
    display: flex;
    align-items: center;
    overflow: hidden;
    font-size: 0.75rem;
    background-color: #cecfd3;
    border-radius: 100vh;
    position: relative;
    color: black;
    font-weight: bold;
    height: 30px;
}
.progress-value {
    padding-inline: 20px;
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
}
div.tagsinput span.tag a {
    display: none !important;
}

.progress-bar.progress-keywords-volume, .progress-bar.progress-keywords-scrape {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    font-size: 11px;
    color: black;
    padding-inline: 20px;
    text-shadow: none;
    overflow: visible;
    font-weight: bold;
    height: 30px;
    border-radius: 100vh;
}
.uk-modal-dialog.uk-modal-lg {
    width: 750px;
}
.cluster-preview {
    column-count: 3;
    column-gap: 10px;
    border-top: 2px solid #c4cfdb;
    padding-top: 20px;
    margin-top: 20px;
    margin-bottom: 10px !important;
}
.cluster_group {
    width: 100%;
    display: inline-block;
    position: relative;
}
.cluster_group_name {
    background: #1a4674;
    color: white;
    padding: 9px 16px;
    font-size: 13px;
    width: 75%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-radius: 100vh;
    position: absolute;
    z-index: 10;
}

.cluster_group ul.cluster_group_keywords {
    font-size: 13px;
    margin-bottom: 15px;
    overflow: hidden;
    font-weight: 500;
    border: 1px solid #8ca2b9;
    border-radius: 10px;
    list-style: none;
    margin-top: 17px;
    padding-top: 19px;
    
}

.cluster_group_keywords > li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 4px 16px;
    color: #404040;
    cursor: move;
}
.cluster_group_keywords > li:last-child {
    padding-bottom: 11px;
}
.cluster_group_keywords > li.drop-placeholder {
    border: 1px dashed;
    background: #f1f1bf !important;
}
.cluster_group_keywords > li.selected {
    background: #cee1ff !important;
}

.cluster-preview.loading-cluster {
    text-align: center;
    column-count: 1;
    min-width: 130ch;
}

#moveToProjectForm .select2-container .select2-selection--single {
    padding: 4px 4px;
    height: 38px;
}
#moveToProjectForm .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 50px;
}
#moveToProjectForm .select2-container {
    width: 100% !important;
}
#aiSuggestionOptions .uk-modal-dialog.uk-modal-dialog-small {
    width: 600px;
}
.ai-suggestion-keywords-table, .ai-keyword-cloud-table {
    width: 100% !important;
    border-collapse: collapse;
}
.ai-suggestion-keywords-table td label, .ai-keyword-cloud-table td label {
    margin: 0 0 0 10px !important;
    cursor: pointer;
}
.ai-suggestion-keywords-table td, .ai-keyword-cloud-table td {
    font-size: 14px;
    color: #545454;
}
.ai-suggestion-keywords-table input[type="radio"] {
    margin: 0 7px 0 0;
    vertical-align: middle;
    border-radius: 50% !important;
}
.ai-avg-price-box {
    border: 1px solid #8ca2b9;
    border-radius: 10px;
    display: grid;
    place-content: center;
    padding: 40px 0;
    color: #545454;
    font-size: 14px;
}
.ai-suggestion-keywords .bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    font-size: 12px;
}
.ai-suggestion-keywords .uk-pagination-right {
    margin: 0;
}
.ai-suggestion-keywords ul.pagination {
    margin: 0;
    padding: 0;
}
.icon-warning {
    padding: 6px 20px;
}
label.ai-suggestion-focus-keyword-label {
    font-size: 12px;
    margin-left: 3px;
    font-weight: 500;
}
.ai-suggestion-keywords-table.table.dataTable tbody td {
    padding: 10px 0 10px 0 !important;
    vertical-align: middle;
}
.ai-suggestion-keywords-table.table > thead:first-child > tr:first-child > td, .ai-keyword-cloud-table > thead:first-child > tr:first-child > td {
    padding: 0 0 10px 0 !important;
    border-bottom: 1px solid #c4cfdb;
    font-size: 16px;
    color: black;
}
.mini-table .xagio-table-bottom {
    padding-block: 10px;
}
.word-highlight:hover {
    cursor: pointer;
    background: #0c355deb;
}

.ai-keyword-cloud {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}
.word-highlight {
    padding: 8px 29px;
    border-radius: 10px;
    background: #1a4674;
    color: #f1f1f1;
    transition: background 150ms ease-in;
    font-size: 14px;
}
span.ai-cluster-word.highlightCloud {
    color: black;
}
.ai-keyword-cloud-table {
    font-size: 13px;
}

label.mini-cluster-info {
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 20px;
    display: block;
}
td.table_hightligh_also:hover {
    cursor: pointer;
    font-weight: 500;
}

.content-width {
    overflow-y: scroll;
    height: 100vh;
}

/*.content-width::-webkit-scrollbar,*/
/*.phraseMatchingKeywords::-webkit-scrollbar{*/
/*    width: 10px; !* for vertical scrollbars *!*/
/*    height: 10px; !* for horizontal scrollbars *!*/
/*}*/

/*.content-width::-webkit-scrollbar-track,*/
/*.phraseMatchingKeywords::-webkit-scrollbar-track {*/
/*    background: #ecf0f1;*/
/*}*/

/*.content-width::-webkit-scrollbar-thumb,*/
/*.phraseMatchingKeywords::-webkit-scrollbar-thumb{*/
/*    background: #082440;*/
/*    -webkit-border-radius: 2px;*/
/*    -moz-border-radius: 2px;*/
/*    border-radius: 2px;*/
/*}*/
.wrap.prs h1.project-name {
    font-size: 24px;
}
h1.project-name .xagio-icon {
    font-size: 30px;
    height: 1.1em;
}
label.include_prepositions input[name="include_prepositions"]:before {
    width: 34px !important;
}
label.include_prepositions input {
    width: 30px !important;
    height: 30px !important;
    border-radius: 5px !important;
}
#phraseMatchModal.uk-modal input[type="text"], #phraseMatchModal.uk-modal input[type="number"] {
    padding: 0 14px;
    height: 46px;
    border: 1px solid #f3f3fa;
    border-radius: 5px !important;
    box-shadow: 0 1px 1px #dddddd !important;
}
.uk-width-medium-2-4.uk-text-right.phrase_new_project {
    margin-top: 8px;
}
.uk-form-row.clustering-keywords {
    margin-top: 15px;
}
h3.cluster-accordion-title {
    border: 1px solid #dbdbdb;
    padding: 12px 0 13px 14px;
    border-radius: 5px;
    margin: 20px 0 0;
    transition: background 150ms;
}
h3.cluster-accordion-title:hover {
    cursor: pointer;
    background: hsl(0 0% 97% / 1);
}
form#phraseMatchForm hr {
    border-color: #e5e5e5;
    background-color: #e5e5e5;
    opacity: 1;
    height: 0;
}
#shared_project_link .uk-modal-body {
    text-align: center;
    padding-top: 3rem;
}
#shared_project_link .share-modal-link {
    margin-block: 2rem;
}

#move-to-top {
    display: none;
    place-items: center;
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #214674;
    width: 50px;
    height: 50px;
    border-radius: 100%;
    color: white;
    box-shadow: 0 0 8px hsl(0deg 0% 0% / 20%);
    cursor: pointer;
    transition: transform 100ms ease-in, box-shadow 100ms ease-in;
    z-index: 1;
}
span#move-to-top-value {
    font-size: 21px;
}
#move-to-top:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 10px 0 hsl(0deg 0% 0% / 50%);
}

.switch {
    position: relative;
    display: inline-block;
    width: 70px;
    height: 26px;
}

.switch input {display:none;}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 10px;
    right: 0;
    bottom: 0;
    background-color: #a8a9ad;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    top: 3px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
    background-color: #173d64;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(32px);
    -ms-transform: translateX(32px);
    transform: translateX(32px);
}

input:checked+ .slider .on
{display: block;}

input:checked + .slider .off{
    display: none;
}


.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.share_btn_cell {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;
}

p.logo-paragraph.uk-block-xagio .auditWebsiteMigration {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 14px 20px !important;
    border: none;
    font-size: 14px;
}

.keyword_contain_option, .audit_keyword_contain_option {
    display: flex;
    align-items: center;
}

.keyword_contain_option .main_keyword_contain, .audit_keyword_contain_option .audit_main_keyword_contain {
    height: 28px !important;
    font-size: 13px;
    min-height: 28px !important;
    padding: 0 10px !important;
    flex-grow: 1;
}

.rename_project {
    margin-left: 12px;
}
.rename_project:hover {
    cursor: pointer;
    color: black;
}

.group-action-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap-reverse;
    gap: 10px;
}

.group-name {
    display: flex;
    align-items: center;
    flex-basis: 50%;
    gap: 10px;
    background: #1a4674;
    padding: 5px 20px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.group-seo {
    display: grid;
    grid-template-columns: 1fr 29ch;
    gap: 10px;
    background: #1a4674;
    border-top-right-radius: 10px;
    padding: 10px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    z-index: 1;
    position: relative;
}

.group-h1 {
    display: flex;
    background: white;
    padding: 10px;
    border-radius: 10px;
    gap: var(--xagio-gap-medium);
    overflow: hidden;
}

.group-google {
    background: white;
    border-radius: 10px;
    padding: 26px 24px;
    overflow: hidden;
}

.group-keywords {
    padding: 20px 10px 10px 10px;
    border-style: solid;
    border-width: 1px;
    border-top: none;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    margin-top: 0;
    background: white;
    border-color: #a3a3a33b;
    z-index: 0;
    position: relative;
    overflow-x: unset;
}

@media (max-width:992px) {
    .group-keywords {
        overflow-x: auto;
    }
}

.group-google-metrics-holder {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}
.group-metrics {
    display: grid;
    place-content: center;
    padding: 10px;
    background: white;
    border-radius: 10px;
}

.title-length-holder, .description-length-holder {
    display: flex;
    gap: 10px;
}

.title-length-holder > div, .description-length-holder > div {
    background: #1e4674;
    color: white;
    padding: 10px;
    border-radius: 10px;
    text-wrap: nowrap;
    width: 100%;
    text-align: center;
}

.group-metrics p {
    margin-bottom: 5px;
    color: #545454;
    margin-top: 9px;
}

.title-length-holder > i, .description-length-holder > i {
    margin-right: 20px;
}

.title-length-holder i, .description-length-holder i {
    margin-right: 10px;
}

input.groupInput[name="group_name"] {
    background: #1a4674;
    color: white;
}
input.groupSelect {
    height: 25px !important;
    width: 30px !important;
    border-radius: 6px !important;
    margin: 0;
}
input[type=checkbox].groupSelect:checked::before {
    content: "";
    background-image: url(/wp-content/plugins/xagio-seo/assets/css/icons/check.svg);
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0;
    width: 100%;
    height: 100%;
    background-color: #1e4674;
    border-radius: 5px;
}
.h-1-holder {
    border-radius: 5px;
    padding: 4px 7px;
    background: #1a4674;
    color: white;
    display: grid;
    place-content: center;
    font-size: 17px;
}

.prs-h1tag {
    padding: 8px;
    font-size: 16px;
    color: #545454;
    font-weight: bold;
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
button.attach-group {
    background: #ebebeb;
    color: #90a2ac;
    border: 0;
    border-radius: 10px;
    width: 100%;
    height: 100%;
    font-size: 18px;
    font-weight: bold;
    padding: 0 20px;
    text-wrap: nowrap;
}
button.attach-group:hover {
    cursor: pointer;
    background: white;
    color: #1e4674;
}
.action-buttons-holder {
    display: flex;
    gap: 5px;
    align-items: center;
}

button.xagio-group-button {
    background: #18406b;
    color: white;
    border: none;
    border-radius: 10px;
    height: 38px;
    width: 38px;
    padding: 0;
    font-size: 13px;
    display: inline;
    place-items: center;
}
button.xagio-group-button:hover {
    cursor: pointer;
    background: #153659;
}
button.xagio-group-button i, .xagio-action-button i {
    font-size: 18px;
}
table.keywords th {
    background: #1e4674;
    color: white;
    font-size: 14px;
    padding: 14px 10px !important;
    text-align: left;
}

table.keywords th:first-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

table.keywords th:last-child {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

input.keyword-selection {
    border-radius: 5px !important;
    width: 20px;
    height: 20px;
    border-color: #1a4674;
}

input.keyword-selection::before {
    margin: 0 !important;
    width: 15px !important;
}
/*.updateKeywords {*/
/*    overflow-x: auto;*/
/*}*/

.updateKeywords .keywords-data td {
    border: none !important;
    padding: 10px !important;
    line-height: 21px;
    border-bottom: none !important;
    color: #3e3e3e;
}
.select-all i {
    cursor: pointer;
    font-size: 14px;
    mask-size: 100%;
    mask-position: 0;
}

table.keywords tbody tr:nth-of-type(odd) {
    background: var(--color-xagio-white-secondary);
}
table.keywords tbody tr:hover {
    background: #EEE !important;
}


.group-seo.page-attached .group-connect-group {
    display: none;
}

.group-seo.page-attached .group-h1 {
    grid-column: 1 / 3;
}

input.groupInput[name="group_name"]::placeholder {
    color: #ffffff7a;
}

.project-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 32px;
    z-index: 101;
    background: #f5f7fb;
}

.attached a {
    padding: 10px 20px;
    color: white;
    line-height: 1;
    font-weight: bold;
    text-wrap: nowrap;
    transition-duration: 0ms;
    text-transform: uppercase;
}

.attached {
    background: #1e4674;
    text-align: center;
    border-radius: 100vh;
    flex-basis: 23ch;
    font-size: 16px;
    display: grid;
    transition-duration: 0ms;
}

.attached a:hover {
    cursor: pointer;
    text-decoration: none;
    background: #173556;
    border-radius: 100vh;
}
.project-actions {
    margin: 17px 0;
    display: flex;
    flex-wrap: wrap;
    gap: var(--xagio-gap-small);;
}
.prs-editor label.host-url {
    padding-left: 0;
    font-size: 12px;
    color: #545454;
}

.prs-editor.prs-title {
    color: #1a4674;
    font-size: 20px;
    line-height: 1.3;
    word-break: break-word;
    margin-top: 4px;
    margin-bottom: 6px;
    font-weight: bold;
}
.prs-editor.prs-description {
    font-size: 14px;
    line-height: 1.4;
    min-height: 39px;
    max-height: 43px;
    overflow: auto;
    word-break: break-word;
}
.url-edit {
    display: inline-block;
    font-size: 12px;
    min-width: 20px;
    text-align: center;
    border-bottom: 1px solid;
    margin-left: -2px;
    color: #545454;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
ul#groupSort li {
    margin: 0;
    cursor: pointer;
    display: grid;
    padding-inline: 0 !important;
}
button.xagio-button.xagio-button-primary.xagio-action-button.uk-form-select {
    padding-inline: 15.5px !important;
}
.xagio-keyword-cloud {
    padding: 18px 10px 10px 10px;
    margin-top: -8px;
    border-left: 1px solid;
    border-right: 1px solid;
    border-color: #a3a3a33b;
    background: white;
}
.updateKeywords .keywords-data td:last-child {
    font-weight: bold;
    color: #7d7d7d;
}
.notes-row {
    padding: 20px 10px 10px 10px;
    border-left: 1px solid;
    border-right: 1px solid;
    margin-top: -8px;
    background: white;
    z-index: 0;
    position: relative;
    border-color: #a3a3a33b;
}

.xagio-keyword-cloud:empty {
    padding: 0;
}

.group-keywords:has(.updateKeywords.hidden) {
    padding: 0;
}
.group-metrics p:first-child {
    margin-top: 0;
}
.xagio-header-actions-in-project {
    display: flex;
    gap: var(--xagio-gap-small);
}
h1.project-name {
    font-size: 23px;
    margin: 0;
}
.keywords-data tr > td:first-child + td {
    max-width: 400px;
    min-width: 200px;

    @media (min-width:1350px) {
        min-width: unset;
    }
    @media (min-width:1920px) {
        min-width: 200px;
    }
}
.empty-keywords {
    padding: 16px;
    font-size: 17px;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 10px;
}

.group-connect-group {
    position: relative;
}

.group-connect-group ul.xagio-button-dropdown {
    position: absolute;
    background: #ebebeb;
    margin: 0;
    padding: 0 0 10px 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    box-shadow: 0 11px 7px 2px #1e46744a;
    overflow: hidden;
    z-index: 1;
}

.group-connect-group ul.xagio-button-dropdown > li {
    padding: 10px 10px 10px 40px;
    font-size: 16px;
    font-weight: bold;
    color: #545454;
    margin: 0;
}

.group-connect-group button.attach-group.xagio-on {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.group-connect-group ul.xagio-button-dropdown > li:hover {
    background: #3A33E8;
    color: #1a4674;
    color: white;
    cursor: pointer;
}
.group-connect-group button.attach-group.xagio-on i {
    transform: rotate(-180deg);
}

.group-connect-group button.attach-group i {
    transition: transform 250ms cubic-bezier(0.65, 0.05, 0.36, 1);
    margin-left: 20px;
}

.volcpc_keywords, .ranking_keywords {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 70px;
    row-gap: 12px;
    border: 1px solid #194476;
    border-radius: 10px;
    padding: var(--xagio-gap-medium);
    max-height: 90px;
    overflow: auto;
}
p.keyword_volume_cost_text, p.keyword_competition_cost_text {
    font-size: 18px;
    margin-bottom: 10px;
    color: #303233;
}

.xagio-modal .select2.select2-container {
    padding: 7px 25px !important;
    background-color: var(--color-xagio-white-secondary) !important;
    color: #646970 !important;
    border-radius: 10px;
    width: 100% !important;
}
.xagio-modal.xagio-modal-aiwizard .xagio-modal-body {
    max-height: none;
    padding: 0;
}

.xagio-modal .select2.select2-container.select2-container--open.select2-container--below {
    border-radius: 10px 10px 0 0;
}
.xagio-modal .select2.select2-container.select2-container--open.select2-container--above {
    border-radius: 0 0 10px 10px;
}

.xagio-modal .select2-container--default .select2-selection--single .select2-selection__arrow, .xagio-modal .select2-container--default .select2-selection--multiple .select2-selection__arrow {
    height: 40px;
}
.xagio-modal .select2-container--default .select2-selection--single, .xagio-modal .select2-container--default .select2-selection--multiple {
    background-color: #f8f8f8;
    border: none;
    border-radius: 0;
}
.xagio-modal .select2-container .select2-selection--single .select2-selection__rendered, .xagio-modal .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding-left: 0;
}
.xagio-modal .select2-container .select2-selection--multiple .select2-selection__rendered {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
#rankTrackingForm .xagio-input-select.xagio-input-select-gray {
    height: 49px;
}

.xagio-checkbox-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}
label:has(.xagio-input-checkbox) {
    display: flex;
    gap: 10px;
    align-items: center;
}
.cluster_top_options {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 20px;
}
.cluster_top_options > div:nth-child(1), .cluster_top_options > div:nth-child(2) {
    flex-basis: 230px;
}
.cluster_top_options > div:nth-child(3) {
    flex-grow: 1;
}
.xagio-flex-space-between label {
    margin-bottom: 0 !important;
}

h1.ai-wizard-welcome {
    font-size: 42px;
    font-weight: 500;
    color: white;
    font-family: 'Outfit', sans-serif;
}

span.phrase-match-underline {
    text-decoration: underline;
}

img.ai-image {
    max-width: 98px;
}

h1.ai-wizard-welcome {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 0;
}

p.ai-wizard-information {
    margin: 0;
    font-size: 27px;
    max-width: 675px;
    color: white;
    text-align: center;
    font-family: 'Outfit', sans-serif;
    line-height: 35px;
}

img.ai-wizard-breadcrumb-ai-image {
    max-width: 65px;
}

.aiwizard-breadcrumb {
    display: flex;
    align-items: center;
    gap: 15px;
    font-weight: 500;
    font-family: 'Outfit', sans-serif;
    font-size: 26px;
    color: #194476;
    margin: 0 0 15px 0;
}

.aiwizard-search-holder {
    display: flex;
    gap: 10px;
}

.aiwizard-search-holder > div {
    display: grid;
    flex: 1;
}

img.ai-wizard-breadcrumb-xagio-image {
    max-height: 36px;
}
.aiwizard-wizard.sw.sw-theme-arrows.sw-justified {
    display: flex;
    flex-direction: column;
    gap: 35px;
}

.ai-wizard-buttons {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 35px;
    align-items: center;
}

.ai-wizard-buttons.ai-wizard-sticky-buttons {
    align-items: center;
    position: sticky;
    bottom: 0;
    background: white;
    padding-block: 30px;
    justify-content: space-between;
}

.xagio-slider-container.xagio-slider-with-grid {
    display: grid;
    grid-template-columns: 1fr 10fr;
}

label.xagio-input-checkbox-label {
    display: inline-block;
    font-size: 15px;
    font-family: 'Outfit', sans-serif;
}

label.xagio-input-checkbox-label input {
    margin-right: 7px;
}


.xagio-table-modal > thead:first-child > tr:first-child > td, .xagio-table-modal > thead:first-child > tr:first-child > th {
    padding: 0 0 10px 5px !important;
    border-bottom: 1px solid #c4cfdb;
    font-size: 16px;
    color: black;
}

.xagio-table-modal td {
    font-size: 14px;
    color: #545454;
}

.xagio-table-modal tbody td {
    padding: 10px 0 10px 5px !important;
    vertical-align: baseline;
}

.xagio-table-modal.xagio-vertical-center tbody td {
    vertical-align: middle;
}

.project-empty p {
    font-size: 18px;
    margin-top: 23px;
    display: flex;
    align-items: center;
    gap: 20px;
}
.project-empty {
    padding: var(--xagio-gap-large);
    background: white;
    border-radius: 10px;
}

.modal-csv-options-holder {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 20px;
}
.csv-option-hover {
    display: none;
    font-size: 16px;
}
.uk-progress {
    border-radius: 10px;
}
.uk-progress-bar {
    background: var(--color-xagio-blue-gradient);
    border-radius: 10px;
}
ul.delete-selected-groups-ul {
    max-height: 210px;
    overflow: auto;
    padding: 0;
    font-size: 14px;
}
.xagio-modal input[type="radio"] {
    width: 20px;
    height: 20px;
    border-radius: 100% !important;
    margin: 0;
    display: inline-grid;
    place-items: center;
}
.xagio-modal input[type="radio"]::before {
    width: 12px !important;
    height: 12px !important;
    margin: 0;
    padding: 0;
    line-height: normal;
    float: none;
    position: relative;
}

.share-modal-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}
.share-modal-link {
    background: #f1f8ff;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #0b243e;
    text-align: center;
}

.share-modal-link a {
    color: #303233;
    font-size: 15px;
}

h3.share-modal-h3 {
    text-align: center;
}
h3.share-modal-h3 > i {
    display: block;
    font-size: 110px;
    margin-bottom: 20px;
    color: #1a4674;
}
.share-modal-body > i {
    font-size: 7rem;
    color: #1a4674;
}

.share-modal-body {
    text-align: center;
}

.share-modal-body > h2 {
    font-size: 40px;
    color: black;
}

.share-modal-body > h3 {
    color: black;
    font-size: 21px;
}
input.keyword-selection:checked {
    background: #1a4674;
}
.posts-actions .xagio-flex > div {
    flex: 1;
}
.uk-dropdown.uk-dropdown-small.uk-dropdown-bottom {
    border-radius: 10px !important;
    background: white;
    border: 1px solid #ebebeb;
}
.uk-nav-dropdown>li>a:focus, .uk-nav-dropdown>li>a:hover {
    background: #3A33E8;
}
.xagio-migration-panel {
    background: var(--color-xagio-white-primary);
    padding: var(--xagio-gap-medium) var(--xagio-gap-large);
    margin-bottom: var(--xagio-gap-medium);
    border-radius: var(--xagio-box-border-radius);
}
.xagio-migration-panel:has(.migration-list) {
    padding: var(--xagio-gap-large);
}
.xagio-migration-panel-title {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin: 0;
    font-size: var(--xagio-panel-label-font-size);
    color: black;
}
.xagio-migration-panel-title i {
    font-size: 22px;
    color: #1d4476;
}

.xagio-migration-panel:last-child {
    margin: 0;
}
p.migration-info {
    font-size: var(--xagio-main-info-font-size);
    color: #545454;
}
ul.migration-list > li {
    font-size: var(--xagio-main-info-font-size);
    color: #545454;
    list-style-type: disc;
}
ul.migration-list {
    column-count: 2;
    columns: 2;
    list-style: square;
    margin-bottom: var(--xagio-gap-large);
}
.action-buttons-holder .attachToPagePost, .action-buttons-holder .attachToTaxonomy {
    display: none;
}

.action-buttons-holder li.li-attached .attachToPagePost, .action-buttons-holder li.li-attached .attachToTaxonomy {
    display: block;
}

.top-ten-result input.select-website:before {
    width: 26px !important;
    height: 26px !important;
}
.ai-wizard-cost {
    background: white;
    padding: 20px;
    display: grid;
    place-content: center;
    position: sticky;
    bottom: 20px;
    top: 30px;
    margin-left: 20px;
}

.ai-wizard-cost-label #xagsCost div {
    padding: 6px 8px;
}

.cf-actions-holder h2 {
    margin-bottom: 0;
}

.cf-actions .xagio-button {
    padding: 13px 16px;
}

.cf-actions select {
    width: 200px;
}

.cf-tabs {
    gap: var(--xagio-gap-small) !important;
}

.cf-tabs li {
    padding: 10px 22px !important;
    background: var(--color-xagio-white-primary) !important;
}

.cf-tabs li.xagio-tab-active {
    background: var(--color-xagio-blue) !important;
}

.cf-tabs li a {
    font-size: 14px !important;
}

.xagio-tab-content h3 {
    margin-bottom: 10px !important;
    color: #444;
}

.xagio-tab-content-icon {
    width: 18px;
    height: 18px;
    display: inline-grid;
    border-radius: 50%;
    place-items: center;
}
.xagio-tab-content-icon.red {
    background: red;
}
.xagio-tab-content-icon.yellow {
    background: darkorange;
}
.xagio-tab-content-icon.green {
    background: green;
}
.xagio-tab-content-icon i {
    font-size: 12px;
    color: white;
}
.apply-cf-template {
    margin-left: auto;
}

.rank-tracking-modal-se .select2-container {
    padding: 5px 25px !important;
}

.xagio-form-select {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    overflow: hidden;
}
.xagio-form-select select {
    position: absolute;
    top: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    left: 0;
    -webkit-appearance: none;
}
#top-ten-language-select {
    width: 100%;
}
label.xagio-social-label > i {
    font-size: 14px;
}

.xagio-refresh-vol-cpc-values.hide, .xagio-refresh-competition-values.hide {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

#consolidateModal .xagio-slider-label {
    font-size: 18px;
    line-height: 25px;
}

.hunter-slider-container {
    width: 100%;
}

.hunter-slider-container {
    height: 3px;
    position: relative;
    background: #e4e4e4;
    border-radius: 5px;
    margin-bottom: 2px;
}

.hunter-slider-container .price-slider {
    height: 100%;
    left: 0;
    right: 0;
    position: absolute;
    border-radius: 5px;
    background: #1a4573;
    margin-inline: 2px;
}

.range-input {
    position: relative;
}

.range-input input {
    position: absolute;
    width: 100%;
    height: 5px;
    background: none;
    top: -5px;
    pointer-events: none;
    cursor: pointer;
    -webkit-appearance: none;
}

/* Styles for the range thumb in WebKit browsers */
input[type="range"]::-webkit-slider-thumb {
    pointer-events: auto;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    -webkit-appearance: none;
    cursor: ew-resize;
    background: #1a4573;
}

.xagio-slider-input {
    display: flex;
    justify-content: space-between;
}

.xagio-slider-input > input[type="number"] {
    border: none;
    padding: 0;
    max-width: 60px;
    margin-inline: 0;
    margin-top: 10px;
}

.xagio-slider-input > input[type="number"]:last-child {
    text-align: right;
}
.xagio-modal p.xagio-slider-label {
    font-size: 18px;
}

input.select-project {
    border-radius: 5px !important;
    width: 20px;
    height: 20px;
    border-color: #1a4674;
}

input[type=checkbox].select-project:checked::before {
    content: "";
    background-image: url(/wp-content/plugins/xagio-seo/assets/css/icons/check.svg);
    background-size: 100%;
    background-repeat: no-repeat;
    width: 100% !important;
    height: 100%;
    background-color: #1e4674;
    border-radius: 5px;
    margin: 0 !important;
}

input.select-all-projects {
    height: 30px !important;
    width: 30px !important;
    border-radius: 6px !important;
    margin: 0;
}

input[type=checkbox].select-all-projects:checked::before {
    content: "";
    background-image: url(/wp-content/plugins/xagio-seo/assets/css/icons/check.svg);
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0;
    width: 100%;
    height: 100%;
    background-color: #1e4674;
    border-radius: 5px;
}


.keyword-cloud-global-holder {
    display: flex;
    gap: 30px;
    padding: 0 30px 30px 0;
}

.xagio-keyword-cloud-global.generated {
    border-color: #a3a3a33b;
    background: white;
    flex: 1;
}

span.seed_keywords_selected {
    color: #afb5bd;
    font-weight: 100;
}

.seed-keywords-global {
    flex-basis: 31%;
    background: #f6f7fb;
    border-radius: 10px;
    display: flex;
    font-size: 25px;
    font-weight: 600;
    text-align: center;
    flex-wrap: wrap;
    gap: 10px;
    place-content: baseline;
    padding: 30px;
}

.seed-keywords-panel-start i {
    font-size: 55px;
    margin-bottom: 16px;
    color: #214674;
    justify-self: center;
}
.seed-keywords-panel-start {
    flex: 1;
    height: 100%;
    place-content: center;
}

.seed-panel-name-container .xagio-input-text-mini {
    background: #2f2ae8;
    width: 130px !important;
    color: white;
    border-radius: 100vh !important;
    font-size: 16px;
}
.seed-keywords-inputs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    flex: 1;
}
.seed-keywords-panel-select {
    display: flex;
    flex-direction: column;
    gap: 30px;
    flex: 1;
    height: 100%;
}

.seed-keywords-panel-select div:first-child {
    align-self: baseline;
}

.seed-keywords-panel-select button:last-child {
    align-self: end;
}
form#seedPanelForm {
    height: 100%;
}