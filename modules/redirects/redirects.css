table.CSV_example_table,
.CSV_example_table th,
.CSV_example_table td {
    border-collapse: collapse !important;
    border: 10px solid white !important;
    border-left: none !important;
}

.CSV_example_table th,
.CSV_example_table td {
    padding: 10px 10px;
    text-align: center;
}

table.CSV_example_table {
    width: 100%;
    background: #f8f8f8;
}

div#wpwrap {
    background: #f5f7fb;
}

.footer-buttons button.uk-button {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 7px 20px !important;
    border: none;
}

.footer-buttons button.uk-button:hover {
    background: #0c355d;
}

table button.uk-button {
    background: #082440;
    border-radius: 5px !important;
    box-shadow: 0 2px 2px #dddddd;
    font-weight: normal;
    padding: 4px 12px !important;
    border: none;
}

table button.uk-button:hover {
    background: #0c355d;
}

.uk-block-xagio table.wp-list-table tbody tr td input[type=checkbox]:checked:before {
    margin: -3px 0 0 -5px !important;
    width: 26px !important;
    content: url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%27%20viewBox%3D%270%200%2020%2020%27%3E%3Cpath%20d%3D%27M14.83%204.89l1.34.94-5.81%208.38H9.02L5.78%209.67l1.34-1.25%202.57%202.4z%27%20fill%3D%27%233582c4%27%2F%3E%3C%2Fsvg%3E") !important;
}

.logTable-actions input.uk-form-small {
    padding: 15px !important;
    border: 1px solid #f3f3fal !important;
    border-radius: 5px !important;
    box-shadow: 0 1px 1px #dddddd !important;
    height: 40px !important;
}

.logTable-actions select.uk-form-small {
    padding: 1px 13px !important;
    border: 1px solid #f3f3fal !important;
    border-radius: 5px !important;
    box-shadow: 0 1px 1px #dddddd !important;
    height: 40px !important;
    font-size: 15px;
}

.uk-pagination > .uk-disabled > span {
    padding: 5px 23px;
    height: 26px;
    box-shadow: 0 1px 1px 1px rgb(115 121 130 / 13%);
    border: none;
    border-radius: 5px !important;
    line-height: 26px;
}

.uk-pagination > li > a, .uk-pagination > .uk-active > span {
    padding: 5px 12px;
    height: 26px;
    box-shadow: 0 1px 1px 1px rgb(115 121 130 / 13%);
    border: none;
    border-radius: 4px !important;
    line-height: 26px;
}

.dataTables_info {
    text-align: right;
}

table.xagio-table.logTable tbody td:nth-child(3) {
    color: #2830c6;
}
.xagio-tr-opened {
    position: relative;
}
.xagio-tr-opened::after {
    content: "";
    width: 20px;
    height: 20px;
    position: absolute;
    border-left: 1px solid;
    bottom: -12px;
    left: 50%;
    translate: -50% 0;
    rotate: 45deg;
    border-top: 1px solid;
    border-color: var(--color-xagio-deep-blue);
    background: var(--color-xagio-white-primary);
}
.xagio-toogle-tr-row {
    border: 1px solid var(--color-xagio-deep-blue);
    border-radius: 10px;
    padding: 20px 20px;
    background: var(--color-xagio-white-primary);
    display: flex;
    gap: 20px;
}
table.xagio-table tbody td:has(.xagio-toogle-tr-row) {
    padding-bottom: 10px;
}
.xagio-toogle-tr-row > ul {
    margin: 0;
    padding: 0;
    font-weight: bold;
    color: var( --color-xagio-red);
}

td.column-url {
    max-width: 370px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
}

td.column-url a {
    color: #2830c6;
}
.CSV_example_table td {
    color: #545454;
}

.CSV_example_table th {
    color: black;
    width: calc(100% / 6);
}

.frmLogSettings .help-icon
{
    font-size: 16px;
}

.xagio-panel-title.xagio-margin-bottom-large {
    margin-bottom: 40px;
}

.select2.select2-container {
    padding: 7px 25px !important;
    background-color: var(--color-xagio-white-secondary) !important;
    color: #646970 !important;
    border-radius: 10px;
}

.select2.select2-container.select2-container--open.select2-container--below {
    border-radius: 10px 10px 0 0;
}
.select2.select2-container.select2-container--open.select2-container--above {
    border-radius: 0 0 10px 10px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow, .select2-container--default .select2-selection--multiple .select2-selection__arrow {
    height: 40px;
}
.select2-container--default .select2-selection--single, .select2-container--default .select2-selection--multiple {
    background-color: #f8f8f8;
    border: none;
    border-radius: 0;
}
.select2-container .select2-selection--single .select2-selection__rendered, .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding-left: 0;
}
.xagio-input-select.xagio-input-select-gray {
    height: 49px;
}
.select2-search.select2-search--inline {
    margin-bottom: 0;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white !important;
    margin: 0 !important;
}

span.select2-selection.select2-selection--multiple {
    border: 0 !important;
    display: flex !important;
}

span.select2-dropdown.select2-dropdown--below,
span.select2-dropdown.select2-dropdown--above {
    border: 0;
    background: #f8f8f8;
    padding: 10px 20px;
}
span.select2-dropdown.select2-dropdown--below {
    border-radius: 0 0 10px 10px;
}
span.select2-dropdown.select2-dropdown--above {
    border-radius: 10px 10px 0 0;
}

li.select2-selection__choice {
    border: none !important;
    -moz-border-radius: 2px !important;
    -webkit-border-radius: 2px !important;
    padding: 6px 15px !important;
    text-decoration: none !important;
    background: #1e4674 !important;
    color: white !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    margin: 0 !important;
    display: flex;
    gap: 5px;
}
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}

.select2-results__group {
    background: var(--color-xagio-blue);
    color: white;
    padding: 6px 13px !important;
}

table.xagio-table .xagio-toggle-ref-table td {
    padding : 4px 24px;
}
table.xagio-table .xagio-toggle-ref-table tr:first-child td {
    padding-top : 0px;
    border-bottom: none;
}
table.xagio-table .xagio-toggle-ref-table tr:last-child td {
    border-bottom: none;
}
table.xagio-table.logTable .xagio-toggle-ref-table td:nth-child(3) {
    color: inherit;
}