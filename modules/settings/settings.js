var plugins, themes, ajax_timeout, timeout, elementorTemplateZip, matcher;
let elementorVersion = 'free';

(function ($) {

    function importKit(session) {
        var payload = {
            id                     : null,
            session                : session,
            include                : [
                'content',
                'settings'
            ],
            overrideConditions     : [],
            selectedCustomPostTypes: []
        };

        if (elementorVersion == 'pro') {
            payload.include.push('templates');
        }

        return $.ajax({
                          url : ajaxurl,
                          type: 'POST',
                          data: {
                              action: 'elementor_import_kit',
                              data  : JSON.stringify(payload),
                              _nonce: nonce
                          }
                      });
    }

    // 3. Import Kit Runner: runs a specific import step based on the runner type.
    function runImportKitRunner(session, runner) {
        var payload = {
            session: session,
            runner : runner
        };

        return $.ajax({
                          url : ajaxurl,
                          type: 'POST',
                          data: {
                              action: 'elementor_import_kit__runner',
                              data  : JSON.stringify(payload),
                              _nonce: nonce
                          }
                      });
    }

    var nonce = xagio_data.elementor_nonce;
    var ajaxurl = xagio_data.wp_get; // If not provided, you might also fall back to a global ajaxurl.

    // 1. Upload Kit: sends the file and nonce to the server.
    function uploadKit(e_import_file, kit_id) {
        var formData = new FormData();
        formData.append('action', 'elementor_upload_kit');
        formData.append('e_import_file', e_import_file);
        formData.append('kit_id', kit_id); // You can pass undefined or a valid kit ID if needed.
        formData.append('_nonce', nonce);

        return $.ajax({
                          url        : ajaxurl,
                          type       : 'POST',
                          data       : formData,
                          processData: false,
                          contentType: false
                      });
    }

    function checkAndInstallElementor() {
        $('#elementor-output').append('<p class="checking-elementor">Checking Elementor <i class="xagio-icon xagio-icon-refresh xagio-icon-spin"></i></p>');

        return $.ajax({
                          url     : xagio_data.wp_post,
                          type    : 'POST',
                          dataType: 'json',
                          data    : {
                              action: 'xagio_ocw_install_elementor'
                          }
                      }).then(function (response) {
            if (response.status === 'success') {
                $('#elementor-output').find('.checking-elementor').html(response.message);
                elementorVersion = response.data.version;

                // Revert kit - ensure this doesn't fail the whole chain
                return $.ajax({
                                  url     : xagio_data.wp_post,
                                  type    : 'POST',
                                  dataType: 'json',
                                  data    : {
                                      action  : 'elementor_revert_kit',
                                      _wpnonce: xagio_data._wpnonce
                                  }
                              });

            } else {
                return $.Deferred().reject(response.data.error || 'Unknown error installing Elementor.');
            }
        });
    }

    function startImportProcess() {
        var file = elementorTemplateZip;
        $('#elementor-output').append('<p><i class="xagio-icon xagio-icon-history"></i> Uploading kit file...</p>');

        uploadKit(file, undefined)
            .then(function (uploadResponse) {
                var session = uploadResponse.data.session;
                $('#elementor-output').append('<p><i class="xagio-icon xagio-icon-check"></i> Upload completed.</p>');
                $('#elementor-output').append('<p><i class="xagio-icon xagio-icon-history"></i> Starting kit import...</p>');
                return importKit(session).then(function () {
                    return session;
                });
            })
            .then(function (session) {
                var runners = [
                    "site-settings",
                    "plugins",
                    "templates",
                    "taxonomies",
                    "elementor-content",
                    "wp-content",
                    "elements-default-values"
                ];
                var chain = $.Deferred().resolve().promise();
                $.each(runners, function (index, runner) {
                    chain = chain.then(function () {
                        $('#elementor-output').append('<p><i class="xagio-icon xagio-icon-history"></i> Importing: ' +
                                                      runner + '...</p>');
                        return runImportKitRunner(session, runner)
                            .then(function () {
                                $('#elementor-output').append('<p><i class="xagio-icon xagio-icon-check"></i> Import of "' +
                                                              runner + '" completed.</p>');
                            });
                    });
                });
                return chain;
            })
            .done(function () {
                $('#elementor-output').append('<p><i class="xagio-icon xagio-icon-check"></i>  Elementor kit import process complete.</p>');
                $('.ocw-step-elementor').fadeOut(function () {
                    $('.ocw-step-1').fadeIn();
                });
            })
            .fail(function (error) {
                $('#elementor-output').append('<p style="color:red;">Error: ' + JSON.stringify(error) + '</p>');
            });
    }

    matcher = function (params, data) {
        var terms, text;
        if (params.term == null) {
            return data;
        }
        terms = params.term.toUpperCase().split(' ');
        text = data.text.toUpperCase();
        if (terms.every(function (term) {
            if (text.indexOf(term) > -1) {
                return true;
            }
        })) {
            return data;
        } else {
            return null;
        }
    };

    let actions = {
        loadTemplates: function () {
            $(document).on('click', '.claim-template', function (e) {
                e.preventDefault();

                let template = $(this).parents('.xagio-column-container.box-template');
                let template_button = template.find('.template-action-button');
                let template_id = template_button.data('id');
                let template_claimed = template.data('claimed');
                let template_key = template.data('key');

                xagioModal('Template Install', 'Are you sure you want to install this template? It will overwrite your existing active Elementor Template!', function (result) {

                    if (result) {

                        $('.search-templates').hide();
                        $('#templates').hide();
                        $('#pagination').hide();

                        // First, ensure Elementor is installed (or install it)
                        checkAndInstallElementor().always(function () {

                            // Now that Elementor is present, continue with the template workflow
                            if (template_claimed) {
                                $.post(xagio_data.wp_post, `action=xagio_ocw_get_template&template_key=${template_key}`, function (d) {
                                    if (d.status === 'success' && d.data) {
                                        // Fetch the template file
                                        fetch(d.data)
                                            .then(response => response.blob())
                                            .then(blob => {
                                                elementorTemplateZip = new File([blob], `${template_key}.zip`, {type: 'application/zip'});
                                                startImportProcess();
                                            })
                                            .catch(error => console.error("Error fetching template:", error));
                                    } else {
                                        console.error("Error retrieving template:", d.message);
                                    }
                                });
                            } else {
                                // Claim template first then get template
                                $.post(xagio_data.wp_post, `action=xagio_ocw_claim_template&template_id=${template_id}`, function (d) {
                                    if (d.status === 'success') {
                                        template.data('claimed', true);
                                        $.post(xagio_data.wp_post, `action=xagio_ocw_get_template&template_key=${template_key}`, function (d) {
                                            if (d.status === 'success' && d.data) {
                                                // Fetch the template file
                                                fetch(d.data)
                                                    .then(response => response.blob())
                                                    .then(blob => {
                                                        elementorTemplateZip = new File([blob], `${template_key}.zip`, {type: 'application/zip'});
                                                        startImportProcess();
                                                    })
                                                    .catch(error => console.error("Error fetching template:", error));
                                            } else {
                                                console.error("Error retrieving template:", d.message);
                                            }
                                        });
                                    } else {
                                        xagioNotify('error', d.message);
                                    }
                                });
                            }
                        });

                    }

                });

            });
            $.post(xagio_data.wp_post, 'action=xagio_ocw_get_templates', function (d) {

                let templates = d.data;
                let templates_holder = $('#templates');

                if (templates.length > 0) {
                    let currentPage = 1;
                    const itemsPerPage = 12;
                    // Use a filtered array that will be updated by the search
                    let filteredTemplates = templates;
                    let totalPages = Math.ceil(filteredTemplates.length / itemsPerPage);

                    // Function to render a specific page using the provided data array
                    function renderPage(page, data) {
                        templates_holder.empty();
                        let start = (page - 1) * itemsPerPage;
                        let end = start + itemsPerPage;
                        let pageTemplates = data.slice(start, end);

                        pageTemplates.forEach(function (template) {
                            let box_clone = $('.box-template.template').clone().removeClass('template');
                            box_clone.attr('data-key', template.key);
                            box_clone.attr('data-claimed', template.claimed);
                            box_clone.attr('data-category', template.category);
                            box_clone.find('.screenshot').attr('src', template.image);
                            box_clone.find('.template-name')
                                     .html(template.name)
                                     .attr('data-xagio-tooltip', '')
                                     .attr('data-xagio-title', template.name);
                            box_clone.find('.preview-template').attr('href', `https://templates.xagio.net/${template.key}`);
                            box_clone.find('.template-action-button')
                                     .attr('data-template', template.key)
                                     .attr('data-id', template.id)
                                     .attr('data-claimed', template.claimed);
                            templates_holder.append(box_clone);
                        });
                    }

                    // Function to render pagination links based on the provided data array
                    function renderPagination(data) {
                        let paginationContainer = $('#pagination');
                        if (paginationContainer.length === 0) {
                            templates_holder.after('<div id="pagination"></div>');
                            paginationContainer = $('#pagination');
                        }
                        paginationContainer.empty();
                        totalPages = Math.ceil(data.length / itemsPerPage);

                        let prevLink = $('<a href="#" class="page-link prev-link"></a>').text('Prev');
                        if (currentPage > 1) {
                            paginationContainer.append(prevLink);
                        } else {
                            prevLink.addClass('disabled');
                            paginationContainer.append(prevLink);
                        }

                        // Create numbered page links
                        for (let i = 1; i <= totalPages; i++) {
                            let pageLink = $('<a href="#" class="page-link"></a>').text(i).data('page', i);
                            if (i === currentPage) {
                                pageLink.addClass('active');
                            }
                            paginationContainer.append(pageLink);
                        }

                        let nextLink = $('<a href="#" class="page-link next-link"></a>').text('Next');
                        if (currentPage < totalPages) {
                            paginationContainer.append(nextLink);
                        } else {
                            nextLink.addClass('disabled');
                            paginationContainer.append(nextLink);
                        }
                    }

                    // Initial render with all templates
                    renderPage(currentPage, filteredTemplates);
                    renderPagination(filteredTemplates);

                    // Handle click on numbered page links
                    $(document).off('click', '.page-link:not(.prev-link):not(.next-link)')
                               .on('click', '.page-link:not(.prev-link):not(.next-link)', function (e) {
                                   e.preventDefault();
                                   currentPage = $(this).data('page');
                                   renderPage(currentPage, filteredTemplates);
                                   renderPagination(filteredTemplates);
                               });

                    // Handle click on Prev link
                    $(document).off('click', '.prev-link').on('click', '.prev-link', function (e) {
                        e.preventDefault();
                        if (currentPage > 1) {
                            currentPage--;
                            renderPage(currentPage, filteredTemplates);
                            renderPagination(filteredTemplates);
                        }
                    });

                    // Handle click on Next link
                    $(document).off('click', '.next-link').on('click', '.next-link', function (e) {
                        e.preventDefault();
                        if (currentPage < totalPages) {
                            currentPage++;
                            renderPage(currentPage, filteredTemplates);
                            renderPagination(filteredTemplates);
                        }
                    });

                    // --- MODIFIED SEARCH HANDLER ---
                    // This now filters the full templates array and then re-renders the pagination
                    $(document).on('keyup', '.search', function (e) {
                        e.preventDefault();
                        let search = $(this).val().toLowerCase();
                        filteredTemplates = templates.filter(function (template) {
                            return template.name.toLowerCase().includes(search);
                        });
                        currentPage = 1; // Reset to the first page after a search
                        renderPage(currentPage, filteredTemplates);
                        renderPagination(filteredTemplates);

                        // Show or hide "no templates" message
                        if (filteredTemplates.length === 0) {
                            $("#no-templates").show();
                        } else {
                            $("#no-templates").hide();
                        }
                    });
                    // --- END SEARCH HANDLER ---
                }

            });
        },

        general: function () {
            $(document).on('click', '.export_to_file', function (e) {
                e.preventDefault();
                var button = $(this);
                var target = button.data('target');
                window.location = xagio_data.wp_post + '?action=' + target;
            });

            let default_engine = $('#search_engine').attr('data-default');
            default_engine = default_engine.split(",");

            if (default_engine != '') {
                $('#search_engine').val(default_engine).trigger('change');
            }

            $('#search_country').select2({
                                             width      : "100%",
                                             placeholder: "Select a Country",
                                         });

            $('#search_location').select2({
                                            width      : "100%",
                                            placeholder: "Select a Location",
                                            allowClear: true,
                                            ajax: {
                                                url: xagio_data.wp_post, 
                                                type: 'POST',
                                                dataType: 'json',
                                                delay: 250,
                                                data: function(params) {
                                                    return {
                                                        action:'xagio_get_cities',
                                                        q: params.term,
                                                        countryCode: $('#search_country').find('option:selected').data('countrycode'),
                                                        page: params.page || 1,
                                                        _xagio_nonce: xagio_data.nonce
                                                    };
                                                },
                                                processResults: function (data, params) {
                                                    params.page = params.page || 1;
                                                    return {
                                                        results: data.data.items,
                                                        pagination: {
                                                            more: data.data.more
                                                        }
                                                    }
                                                },
                                                cache: true
                                            },
                                            minimumInputLength: 3,
                                          });

            $('#search_engine').select2({
                                            matcher    : matcher,
                                            width      : "100%",
                                            placeholder: "Select a Search Engine"
                                        });

            $(document).on('change', '#import_options', function (e) {
                e.preventDefault();
                clearTimeout(timeout);
                var form = $(this);

                var file_data = form.find("#import_options_file").prop("files")[0];
                var form_data = new FormData();
                form_data.append("import_options_file", file_data);

                $.ajax({
                           url        : xagio_data.wp_post + '?action=xagio_import_options',
                           dataType   : 'json',
                           cache      : false,
                           contentType: false,
                           processData: false,
                           data       : form_data,
                           type       : 'post',
                           statusCode : {
                               200: function (data) {
                                   xagioNotify(data.status, `${data.message} Refreshing page in 3 sec...`);
                                   timeout = setTimeout(function () {
                                       location.reload();
                                   }, 3000);
                               }
                           }
                       });

            });

        },

        wpEasySetup: function () {

            /**
             *  Initiate TagsInput
             */
            themes = $('#themes').tagsInput({'interactive': false});
            plugins = $('#plugins').tagsInput({'interactive': false});

            /**
             *  Perform Fresh Start
             */
            $(document).on('click', '.perform-easy-setup', function (e) {
                e.preventDefault();
                let button = $(this);
                let form = $('form.fs');
                button.disable('Loading ...');
                let formDataArray = form.serializeArray();
                let labelsArray = [];

                $(formDataArray).each(function (index, data) {
                    if (data.value === "1") {
                        let label = $('label[for="' + data.name + '"]').text().replace(/ - /g, "");
                        labelsArray.push(label.trim());
                    }
                    if (data.name === "fs_plugins") {
                        if (data.value !== "") {
                            labelsArray.push("Install plugins: " + data.value.replace(",", ", "));
                        }
                    }
                    if (data.name === "fs_themes") {
                        if (data.value !== "") {
                            labelsArray.push("Install themes: " + data.value.replace(",", ", "));
                        }
                    }
                    if (data.name === "fs_create_categories_list[]" || data.name === "fs_create_blank_pages_list[]" ||
                        data.name === "fs_create_blank_posts_list[]") {
                        if (data.value !== "") {
                            labelsArray.push("- " + data.value);
                        }
                    }
                });

                let message = `<div class="modal-message xagio-margin-bottom-medium">This action will do the following:</div>`;

                message += '<div class="modal-items">';
                labelsArray.forEach(function (data) {
                    message += `<p>${data}</p>`;
                });
                message += '</div>';

                if (labelsArray.length > 0) {
                    xagioModal("Are you sure?", message, function (yes) {
                        if (yes) {
                            $.post(xagio_data.wp_post, form.serializeArray(), function (d) {
                                if (d.status === "success") {
                                    $('.easy-setup-backup-notice').removeClass('xagio-hidden');
                                    $('.easy-setup-backup').html(`<a href="${d.backup}" target="_blank">${d.backup}</a>`);
                                    xagioNotify('success', "Operation completed.");
                                    button.disable();
                                }
                            });
                        } else {
                            button.disable();
                        }
                    })
                } else {
                    button.disable();
                    xagioNotify("danger", "Nothing selected!");
                    return false;
                }
            });

            $(document).on('submit', 'form.fs', function (e) {
                e.preventDefault();
            });

            /**
             *  Select result and put it into a tag
             */
            $(document).on('click', '.select-result', function () {
                var name = $(this).data('name');
                var type = $(this).data('type');
                if (!window[type].tagExist(name)) {
                    window[type].addTag(name);
                }
            });

            /**
             *  Key press events for Plugins/Themes search
             */

            $('#search_plugins,#search_themes').on('keypress', function (e) {
                e.stopPropagation();
                var element = this;
                var type = $(element).data('type');
                var results = $('#result_' + type);
                results.empty();
                clearTimeout(ajax_timeout);
                ajax_timeout = setTimeout(function () {
                    $(element).attr('disabled', 'disabled');
                    results.append(
                        '<div class="search-loading">Loading... <i class="xagio-icon xagio-icon-sync xagio-icon-spin"></i></div>'
                    );
                    var data = [
                        {
                            name : 'action',
                            value: 'xagio_search_wp_api'
                        },
                        {
                            name : 'type',
                            value: type
                        },
                        {
                            name : 'search',
                            value: $(element).val()
                        }
                    ];
                    $.post(xagio_data.wp_post, data, function (d) {

                        results.empty();

                        $(element).removeAttr('disabled');
                        var data = d[type];
                        if (data.length < 1) {
                            results.append(
                                '<div class="search-no-results"><i class="xagio-icon xagio-icon-warning"></i> No results for search query <b>"' +
                                $(element).val() + '"</b>.</div>'
                            );
                            return 0;
                        }

                        let author_link = '';
                        for (var i = 0; i < data.length; i++) {
                            var row = data[i];
                            author_link = row.author;
                            if (type === 'themes') {
                                if (row.author.author_url == 'false') {
                                    row.author.author_url = '#';
                                }
                                author_link = `<a href="${row.author.author_url}" target="_blank">${row.author.display_name}</a>`
                            }

                            results.append(
                                '<div class="search-result">' +
                                '<p class="search-result-title">' + row.name + ' <small>by <b>' + author_link +
                                '</b></small></p>' +
                                '<p class="search-result-description">' +
                                (row.hasOwnProperty('short_description') ? row.short_description : row.description) +
                                '</p>' +
                                '<div class="search-result-actions">' +
                                '<button type="button" class="xagio-button xagio-button-primary xagio-button-padding-small select-result" data-type="' +
                                type + '" data-name="' + row.slug +
                                '"><i class="xagio-icon xagio-icon-plus"></i> Add</button>' +
                                '' +
                                '' +
                                '</div>' +
                                '' +
                                '' +
                                '</div>'
                            );
                        }

                    });
                }, 600);
            });

            /**
             *  Create Categories
             */
            $(document).on('change', '#fs_create_categories', function (e) {
                e.preventDefault();
                $('.fs_create_categories_list').toggleClass('xagio-hidden');
            });
            $(document).on('click', '.uk-button-add-category', function (e) {
                e.preventDefault();
                $('<input name="fs_create_categories_list[]" type="text" placeholder="eg. Category Name" class="xagio-input-text-mini"/>').insertBefore($('.uk-button-add-category'));
            });
            $(document).on('click', '.uk-button-remove-category', function (e) {
                e.preventDefault();
                $('.fs_create_categories_list').find('input').last().remove();
            });

            /**
             *  Create Pages
             */
            $(document).on('change', '#fs_create_blank_pages', function (e) {
                e.preventDefault();
                $('.fs_create_blank_pages_list').toggleClass('xagio-hidden');
            });
            $(document).on('click', '.uk-button-add-pages', function (e) {
                e.preventDefault();
                $('<input name="fs_create_blank_pages_list[]" type="text" placeholder="eg. Page Name" class="xagio-input-text-mini"/>').insertBefore($('.uk-button-add-pages'));
            });
            $(document).on('click', '.uk-button-remove-pages', function (e) {
                e.preventDefault();
                $('.fs_create_blank_pages_list').find('input').last().remove();
            });

            /**
             *  Create Posts
             */
            $(document).on('change', '#fs_create_blank_posts', function (e) {
                e.preventDefault();
                $('.fs_create_blank_posts_list').toggleClass('xagio-hidden');
            });
            $(document).on('click', '.uk-button-add-post', function (e) {
                e.preventDefault();
                $('<input name="fs_create_blank_posts_list[]" type="text" placeholder="eg. Post Name" class="xagio-input-text-mini"/>').insertBefore($('.uk-button-add-post'));
            });
            $(document).on('click', '.uk-button-remove-post', function (e) {
                e.preventDefault();
                $('.fs_create_blank_posts_list').find('input').last().remove();
            });

        },

        toggleCaptchaFields: function () {
            $(document).on('change', '#XAGIO_RECAPTCHA', function (e) {
                $('.recaptcha-settings').toggleClass('xagio-hidden');
            });
        },

        locationKeywordSettingsSelect2: function () {
            let languageSelect = $('#xagioSettings-locationKeywordLanguage');
            let countrySelect = $('#xagioSettings-locationKeywordCountry');

            let saved_language = languageSelect.attr('data-default');

            if (saved_language !== '') {
                languageSelect.val(saved_language);
            }

            languageSelect.select2({
                                       placeholder: "Select Language",
                                       width      : '100%'
                                   });

            let saved_country = countrySelect.attr('data-default');

            if (saved_country !== '') {
                countrySelect.val(saved_country);
            }

            countrySelect.select2({
                                      placeholder: "Select Country",
                                      width      : '100%'
                                  });
        },

        saveKeywordSettingsLanguageOnChange: function () {
            $("#xagioSettings-locationKeywordLanguage").on('change', function () {
                let language = $(this).val();

                if (language !== '') {
                    $.post(xagio_data.wp_post, `action=xagio_set_default_keyword_language&language=${language}`, function (d) {
                        xagioNotify((d.status == 'success') ? d.status : 'danger', d.message);
                    });
                }
            });
        },

        saveKeywordSettingsCountryOnChange: function () {
            $("#xagioSettings-locationKeywordCountry").on('change', function () {
                let country = $(this).val();

                if (country !== '') {
                    $.post(xagio_data.wp_post, `action=xagio_set_default_keyword_country&country=${country}`, function (d) {
                        xagioNotify((d.status == 'success') ? d.status : 'danger', d.message);
                    });
                }
            });
        },

        saveRankTrackerCountryOnChange: function () {
            $("#search_country").on('change', function () {
                let country = $(this).val();
                if (country !== '') {

                    $.post(xagio_data.wp_post, `action=xagio_set_default_country&data=${country}`, function (d) {
                        if (d.status == 'success') {
                            xagioNotify('success', d.message);
                            $("#search_location").empty().trigger('change');
                        } else {
                            xagioNotify('danger', d.message);
                        }
                    });
                }
            });
        },

        saveRankTrackerLocatioinOnChange: function () {
            $("#search_location").on('change', function () {
                let location = $(this).val();
                $.post(xagio_data.wp_post, {
                    action: 'xagio_set_default_location',
                    data  : location
                }, function (d) {
                    xagioNotify(d.status == 'success' ? 'success' : 'danger', d.message);
                });
            });
        },

        saveSearchEngineOnChange: function () {
            $('#search_engine').on('change', function () {
                let select = $(this);

                let data = select.select2('data');

                if (data.length >= 1) {
                    let searchEngine = [];
                    for (let i = 0; i < data.length; i++) {
                        let id = data[i].id;
                        let text = data[i].text;
                        let sd = {
                            'id'  : id,
                            'text': text
                        }
                        searchEngine.push(sd);
                    }

                    let params = new FormData();
                    params.append('action', 'xagio_set_default_search_engine');

                    for (let i = 0; i < searchEngine.length; i++) {
                        const engine = searchEngine[i];
                        params.append(`data[${i}][id]`, engine.id);
                        params.append(`data[${i}][text]`, engine.text);
                    }

                    $.ajax({
                               url        : xagio_data.wp_post,
                               type       : 'POST',
                               data       : params,
                               processData: false, // Necessary for FormData
                               contentType: false, // Necessary for FormData
                               success    : function (d) {
                                   xagioNotify(d.status, d.message);
                               }
                           });
                }
            })
        },

        auditSaveDefaultLocation: function () {
            $("#auditWebsite_default-location").on('change', function () {
                let val = $(this).val();
                let locationCode = $(this).find('option:selected').data("lang-code");

                $.post(xagio_data.wp_post, `action=xagio_set_default_audit_location&data=${val},${locationCode}`, function (d) {
                    xagioNotify((d.status == 'success') ? d.status : 'danger', d.message);
                });
            })
        },

        setDefaultAuditLocation: function () {
            let auditLocationSelect = $("#auditWebsite_default-location");

            let data = auditLocationSelect.data('default');

            if (data) {
                let splitData = data.split(',');

                let value = splitData[0];
                let locationCode = splitData[1];

                $('#auditWebsite_default-location option').removeAttr('selected');
                $(`#auditWebsite_default-location option[value=${value}][data-lang-code=${locationCode}]`).attr('selected', true);
            }

            auditLocationSelect.select2({
                                            placeholder: "Select Location",
                                            width      : '100%',
                                        });
        },

        aiWizardSaveDefaultSearchEngine: function () {
            $("#AiWizard_default-search-engine").on('change', function () {
                let val = $(this).val();

                $.post(xagio_data.wp_post, `action=xagio_set_default_ai_wizard_search_engine&value=${val}`, function (d) {
                    xagioNotify((d.status == 'success') ? d.status : 'danger', d.message);
                });
            })
        },

        setDefaultAiWizardSearchEngine: function () {
            let engineSelect = $("#AiWizard_default-search-engine");
            let value = engineSelect.data('default');

            if (value) {
                $('#AiWizard_default-search-engine option').removeAttr('selected');
                $(`#AiWizard_default-search-engine option[value=${value}]`).attr('selected', true);
            }

            engineSelect.select2({
                                     matcher    : matcher,
                                     width      : "100%",
                                     placeholder: "Select a Search Engine"
                                 });
        },

        aiWizardSaveDefaultLocation: function () {
            $("#AiWizard_default-location").on('change', function () {
                let val = $(this).val();

                $.post(xagio_data.wp_post, `action=xagio_set_default_ai_wizard_location&value=${val}`, function (d) {
                    xagioNotify((d.status == 'success') ? d.status : 'danger', d.message);
                });
            })
        },

        setDefaultAiWizardLocation: function () {
            let locationSelect = $("#AiWizard_default-location");
            let value = locationSelect.data('default');

            if (value) {
                $('#AiWizard_default-location option').removeAttr('selected');
                $(`#AiWizard_default-location option[value=${value}]`).attr('selected', true);
            }

            locationSelect.select2({
                                       placeholder: "Select Location",
                                       width      : '100%'
                                   });
        },

    };


    $(document).ready(function () {
        actions.loadTemplates();
        actions.general();
        actions.wpEasySetup();
        actions.toggleCaptchaFields();
        actions.locationKeywordSettingsSelect2();
        actions.saveKeywordSettingsCountryOnChange();
        actions.saveKeywordSettingsLanguageOnChange();
        actions.saveRankTrackerCountryOnChange();
        actions.saveRankTrackerLocatioinOnChange();
        actions.saveSearchEngineOnChange();
        actions.auditSaveDefaultLocation();
        actions.setDefaultAuditLocation();
        actions.aiWizardSaveDefaultSearchEngine();
        actions.setDefaultAiWizardSearchEngine();
        actions.aiWizardSaveDefaultLocation();
        actions.setDefaultAiWizardLocation();
    });


})(jQuery);
