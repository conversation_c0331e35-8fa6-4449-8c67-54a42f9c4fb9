<?php
/**
 * Plugin Name: SLMM SEO Plugin
 * Description: Enhanced SEO tools with AI-powered content generation for WordPress Classic Editor
 * Version: 4.10.0
 * Author: Massive Organic
 * Author URI: https://massiveorganic.com.au
 * Text Domain: slmm-seo-bundle
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.2
 * Network: false
 *
 * @package SLMM_SEO_Bundle
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

$plugin_prefix = 'SLMMSEOPLUGIN';

// Extract the version number
$plugin_data = get_file_data(__FILE__, ['Version' => 'Version']);

// Plugin Constants
define($plugin_prefix . '_DIR', plugin_basename(__DIR__));
define($plugin_prefix . '_BASE', plugin_basename(__FILE__));
define($plugin_prefix . '_PATH', plugin_dir_path(__FILE__));
define($plugin_prefix . '_VER', $plugin_data['Version']);
define($plugin_prefix . '_CACHE_KEY', 'slmm_seo_plugin-cache-key-for-plugin');
define($plugin_prefix . '_REMOTE_URL', 'https://ranking.advancedseotraining.com/wp-content/plugins/hoster/inc/secure-download.php?file=json&download=1667&token=53111fa7f243d17f5241bd908c017259df13e59d7378bcef67ada7046e809740');

require constant($plugin_prefix . '_PATH') . 'inc/update.php';

// Declare global variable for the update checker instance
global $slmm_seo_update_checker;

$slmm_seo_update_checker = new SLMMSEOPLUGIN_DPUpdateChecker(
	constant($plugin_prefix . '_BASE'), // This is the plugin slug
	constant($plugin_prefix . '_VER'),
	constant($plugin_prefix . '_CACHE_KEY'),
	constant($plugin_prefix . '_REMOTE_URL'),
);

// Load the actual plugin file
require_once __DIR__ . '/slmm-seo-plugin.php'; 