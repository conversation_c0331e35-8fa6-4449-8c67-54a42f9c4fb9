<?php
require_once dirname(__DIR__) . '/plugin.php';

/**
 * Plugin Name: Content Freshness Reminder
 * Description: A system to remind users to change content by 10-15% every 6-9 months.
 * Author: SEO Deme - SLMM
 */

// Ensure this file is being run within the WordPress context
if (!defined('ABSPATH')) {
    exit;
}

// Include the WP_List_Table class
if (!class_exists('WP_List_Table')) {
    require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
}

add_filter('wp_post_revision_meta_keys', 'content_freshness_revision_meta_keys', 10, 2);

function content_freshness_revision_meta_keys($keys, $post_type) {
    if (!is_object($post_type)) {
        $post_type = get_post_type_object($post_type);
    }
    return $keys;
}

// Add menu item (hidden)
add_action('admin_menu', 'content_freshness_menu');

function content_freshness_menu() {
    $hook = add_submenu_page(
        null,                   // Don't add to any menu
        'Content Freshness',    // Page title
        'Content Freshness',    // Menu title (unused)
        'manage_options',       // Capability
        'content-freshness',    // Menu slug
        'content_freshness_page' // Callback function
    );

    // Add your action for this specific page
    add_action("load-$hook", 'content_freshness_page_load');
}

// This function will run when your hidden page loads
function content_freshness_page_load() {
    // Add screen options
    add_screen_option('per_page', array(
        'label' => 'Items per page',
        'default' => 20,
        'option' => 'content_freshness_per_page'
    ));

    // Enqueue scripts and styles specific to this page
    add_action('admin_enqueue_scripts', 'content_freshness_enqueue_scripts');
}

// Enqueue scripts and styles
function content_freshness_enqueue_scripts($hook) {
    // Debug: Check what hook we're getting
    // error_log('Content Freshness Hook: ' . $hook);
    
    if ($hook != 'admin_page_content-freshness' && $hook != 'toplevel_page_content-freshness') {
        return;
    }

    wp_enqueue_style('wp-list-table');
    wp_enqueue_script('jquery');
    wp_enqueue_script('jquery-ui-datepicker');
    wp_enqueue_style('jquery-ui-datepicker', 'https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css');
    
    wp_add_inline_script('jquery-ui-datepicker', '
    jQuery(document).ready(function($) {
        $("#hide-cpt").on("click", function(e) {
            e.preventDefault();
            var hiddenCPTs = $("input[name=\'cpt_filter[]\']:checked").map(function() {
                return this.value;
            }).get();
            
            $.post(ajaxurl, {
                action: "update_hidden_cpts",
                hidden_cpts: hiddenCPTs
            }, function(response) {
                location.reload();
            });
        });

        $("#reset-cpt-filter").on("click", function(e) {
            e.preventDefault();
            $("a.button:not(:contains(\'Posts\'), :contains(\'Pages\'))").removeClass("button-primary");
            $.post(ajaxurl, {
                action: "reset_cpt_filter"
            }, function(response) {
                location.reload();
            });
        });

        $("#export-csv").on("click", function(e) {
            e.preventDefault();
            var contentType = new URLSearchParams(window.location.search).get("content_type") || "all";
            window.location.href = ajaxurl + "?action=export_content_freshness_csv&content_type=" + contentType;
        });

        $("#revision_depth").on("change", function() {
            $("#content-freshness-filter-form").submit();
        });

       // Hide buttons if no items are found
        if ($(".wp-list-table tr.no-items").length > 0) {
            $(".content-freshness-filters, #export-csv").hide();
        }

        $("#toggle-cpt").on("click", function(e) {
            e.preventDefault();
            $("#additional-options").toggle();
        });

        $("#update-cpt-visibility").on("click", function(e) {
            e.preventDefault();
            var visibleCPTs = $("input[name=\'cpt_visibility[]\']:checked").map(function() {
                return this.value;
            }).get();
            
            $.post(ajaxurl, {
                action: "update_cpt_visibility",
                visible_cpts: visibleCPTs
            }, function(response) {
                location.reload();
            });
        });
    });
    ');

    wp_add_inline_style('wp-list-table', '
        /* Content Freshness Dark Theme */
        .content-freshness-dark-theme {
            --slmm-primary: #7C3AED;
            --slmm-primary-hover: #8B5CF6;
            --slmm-primary-light: rgba(124, 58, 237, 0.08);
            --slmm-primary-subtle: rgba(124, 58, 237, 0.04);
            --slmm-dark-bg: #0A0A0F;
            --slmm-dark-surface: #141419;
            --slmm-dark-surface-hover: #1A1A1F;
            --slmm-dark-border: #1F1F24;
            --slmm-dark-border-subtle: #16161B;
            --slmm-text-primary: #F1F5F9;
            --slmm-text-secondary: #94A3B8;
            --slmm-text-muted: #64748B;
            --slmm-text-dim: #475569;
            --slmm-success: #22C55E;
            --slmm-warning: #F59E0B;
            --slmm-error: #EF4444;
            --slmm-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            --slmm-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.4);
        }

        /* Main container dark theme */
        .content-freshness-dark-theme {
            background: var(--slmm-dark-bg) !important;
            color: var(--slmm-text-primary) !important;
            margin: 0 -20px -10px !important;
            padding: 2rem !important;
            min-height: 100vh !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        }

        .content-freshness-dark-theme h1 {
            color: var(--slmm-text-primary) !important;
            font-size: 1.75rem !important;
            font-weight: 600 !important;
            margin-bottom: 2rem !important;
            padding-bottom: 1rem !important;
            border-bottom: 1px solid var(--slmm-dark-border) !important;
        }

        /* Filter buttons styling */
        .content-freshness-dark-theme .content-freshness-filters {
            margin-bottom: 1.5rem !important;
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 0.5rem !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button {
            background: var(--slmm-dark-surface) !important;
            border: 2px solid var(--slmm-dark-border) !important;
            color: var(--slmm-text-secondary) !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            text-decoration: none !important;
            transition: all 0.3s ease !important;
            box-shadow: none !important;
            text-shadow: none !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button:hover {
            background: var(--slmm-dark-surface-hover) !important;
            border-color: var(--slmm-primary) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button-primary {
            background: var(--slmm-primary) !important;
            border-color: var(--slmm-primary) !important;
            color: white !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button-primary:hover {
            background: var(--slmm-primary-hover) !important;
            border-color: var(--slmm-primary-hover) !important;
        }

        /* Additional options styling */
        .content-freshness-dark-theme #additional-options {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            border-radius: 8px !important;
            padding: 1rem !important;
            margin-bottom: 1rem !important;
        }

        .content-freshness-dark-theme #additional-options label {
            color: var(--slmm-text-primary) !important;
            font-weight: 500 !important;
            margin-bottom: 0.5rem !important;
            display: block !important;
        }

        .content-freshness-dark-theme #additional-options input[type="checkbox"] {
            margin-right: 0.5rem !important;
        }

        /* Action buttons */
        .content-freshness-dark-theme #export-csv, 
        .content-freshness-dark-theme #reset-cpt-filter, 
        .content-freshness-dark-theme #update-cpt-visibility, 
        .content-freshness-dark-theme #toggle-cpt {
            background: var(--slmm-dark-surface) !important;
            border: var(--slmm-primary)!important;
            border-radius: 6px !important;
            color: white !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            margin-right: 0.5rem !important;
            margin-bottom: 0.5rem !important;
            box-shadow: none !important;
            text-shadow: none !important;
        }

        .content-freshness-dark-theme #export-csv:hover, 
        .content-freshness-dark-theme #reset-cpt-filter:hover, 
        .content-freshness-dark-theme #update-cpt-visibility:hover, 
        .content-freshness-dark-theme #toggle-cpt:hover {
            background: var(--slmm-primary-hover) !important;
            transform: translateY(-1px) !important;
        }

        /* Form elements */
        .content-freshness-dark-theme #revision_depth {
            background: var(--slmm-dark-surface) !important;
            border: 2px solid var(--slmm-dark-border) !important;
            border-radius: 6px !important;
            color: var(--slmm-text-primary) !important;
            padding: 0.5rem !important;
            margin-bottom: 1rem !important;
        }

        .content-freshness-dark-theme #revision_depth:focus {
            outline: none !important;
            border-color: var(--slmm-primary) !important;
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
        }

        .content-freshness-dark-theme #revision_depth option {
            background: var(--slmm-dark-surface) !important;
            color: var(--slmm-text-primary) !important;
        }

        /* Table styling */
        .content-freshness-dark-theme .wp-list-table {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            border-radius: 8px !important;
            overflow: hidden !important;
        }

        .content-freshness-dark-theme .wp-list-table th {
            background: var(--slmm-dark-bg) !important;
            color: var(--slmm-text-primary) !important;
            border-bottom: 1px solid var(--slmm-dark-border) !important;
            font-weight: 600 !important;
            padding: 1rem 0.75rem !important;
        }

        .content-freshness-dark-theme .wp-list-table td {
            background: var(--slmm-dark-surface) !important;
            color: var(--slmm-text-secondary) !important;
            border-bottom: 1px solid var(--slmm-dark-border-subtle) !important;
            padding: 0.75rem !important;
        }

        .content-freshness-dark-theme .wp-list-table tbody tr:hover td {
            background: var(--slmm-dark-surface-hover) !important;
        }

        .content-freshness-dark-theme .wp-list-table .row-actions {
            color: var(--slmm-text-muted) !important;
        }

        .content-freshness-dark-theme .wp-list-table .row-actions a {
            color: var(--slmm-primary) !important;
            text-decoration: none !important;
        }

        .content-freshness-dark-theme .wp-list-table .row-actions a:hover {
            color: var(--slmm-primary-hover) !important;
        }

        /* Pagination */
        .content-freshness-dark-theme .tablenav {
            background: var(--slmm-dark-bg) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .pagination-links a, 
        .content-freshness-dark-theme .pagination-links span {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .pagination-links a:hover {
            background: var(--slmm-primary) !important;
            border-color: var(--slmm-primary) !important;
            color: white !important;
        }

        .content-freshness-dark-theme .pagination-links .current {
            background: var(--slmm-primary) !important;
            border-color: var(--slmm-primary) !important;
            color: white !important;
        }

        .content-freshness-dark-theme .displaying-num {
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .paging-input input {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .tablenav-paging-text {
            color: var(--slmm-text-primary) !important;
        }

        /* Freshness indicators - keep original colors for readability */
        .content-freshness-dark-theme .freshness-indicator {
            padding: 5px !important;
            border-radius: 6px !important;
            font-weight: 600 !important;
            display: inline-block !important;
        }

        .content-freshness-dark-theme .freshness-indicator span {
            padding: 4px 8px !important;
            border-radius: 4px !important;
            font-weight: 600 !important;
            font-size: 0.875rem !important;
        }

        /* No items message */
        .content-freshness-dark-theme .wp-list-table .no-items td {
            background: var(--slmm-dark-surface) !important;
            color: var(--slmm-text-muted) !important;
            text-align: center !important;
            padding: 2rem !important;
            font-style: italic !important;
        }

        /* Checkboxes */
        .content-freshness-dark-theme .wp-list-table input[type="checkbox"] {
            background: var(--slmm-dark-surface) !important;
            border: 2px solid var(--slmm-dark-border) !important;
        }

        .content-freshness-dark-theme .wp-list-table input[type="checkbox"]:checked {
            background: var(--slmm-primary) !important;
            border-color: var(--slmm-primary) !important;
        }

        /* Strong text styling */
        .content-freshness-dark-theme .wp-list-table strong {
            color: var(--slmm-text-primary) !important;
        }

        /* Links */
        .content-freshness-dark-theme .wp-list-table a {
            color: var(--slmm-primary) !important;
            text-decoration: none !important;
        }

        .content-freshness-dark-theme .wp-list-table a:hover {
            color: var(--slmm-primary-hover) !important;
        }

        .content-freshness-dark-theme .wp-list-table th a {
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .wp-list-table th a:hover {
            color: var(--slmm-primary) !important;
        }

        /* Additional text color fixes */
        .content-freshness-dark-theme .change_percentage span {
            color: var(--slmm-text-muted) !important;
        }

        /* Responsive adjustments */
        @media (max-width: 782px) {
            .content-freshness-dark-theme {
                padding: 1rem !important;
            }
            
            .content-freshness-dark-theme .content-freshness-filters {
                flex-direction: column !important;
            }
            
            .content-freshness-dark-theme .content-freshness-filters .button {
                margin-bottom: 0.25rem !important;
            }
        }
    ');
}

// Modify this function to handle the screen options
add_filter('set_screen_option_content_freshness_per_page', 'content_freshness_set_option', 10, 3);

function content_freshness_set_option($status, $option, $value) {
    if ('content_freshness_per_page' == $option) return $value;
    return $status;
}

// Main page content
function content_freshness_page() {
    // Check user capabilities
    if (!current_user_can('manage_options')) {
        return;
    }

    // Check if post revisions are active globally
    $wp_post_revisions = defined('WP_POST_REVISIONS') ? WP_POST_REVISIONS : true;

    if (!$wp_post_revisions) {
        echo '<div class="notice notice-warning"><p>Post revisions are not enabled globally. Please enable them in your wp-config.php file for accurate content freshness tracking.</p></div>';
    }

    // Get the content type filter
    $content_type = isset($_GET['content_type']) ? sanitize_text_field($_GET['content_type']) : 'all';

    // Get the revision depth
    $revision_depth = isset($_POST['revision_depth']) ? intval($_POST['revision_depth']) : 1;

    // Get hidden CPTs
    $hidden_cpts = get_option('content_freshness_hidden_cpts', array());

    // Create an instance of our table class
    $content_freshness_table = new Content_Freshness_List_Table();

    // Fetch, prepare, sort, and filter our data
    $content_freshness_table->prepare_items($content_type, $revision_depth, $hidden_cpts);
    ?>
    <style>
        /* Content Freshness Dark Theme - Inline Fallback */
        .content-freshness-dark-theme {
            --slmm-primary: #7C3AED;
            --slmm-primary-hover: #8B5CF6;
            --slmm-dark-bg: #0A0A0F;
            --slmm-dark-surface: #141419;
            --slmm-dark-surface-hover: #1A1A1F;
            --slmm-dark-border: #1F1F24;
            --slmm-dark-border-subtle: #16161B;
            --slmm-text-primary: #F1F5F9;
            --slmm-text-secondary: #94A3B8;
            --slmm-text-muted: #64748B;
            background: var(--slmm-dark-bg) !important;
            color: var(--slmm-text-primary) !important;
            margin: 0 -20px -10px !important;
            padding: 2rem !important;
            min-height: 100vh !important;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
        }

        .content-freshness-dark-theme h1 {
            color: var(--slmm-text-primary) !important;
            font-size: 1.75rem !important;
            font-weight: 600 !important;
            margin-bottom: 2rem !important;
            padding-bottom: 1rem !important;
            border-bottom: 1px solid var(--slmm-dark-border) !important;
        }

        /* Filter buttons styling */
        .content-freshness-dark-theme .content-freshness-filters {
            margin-bottom: 1.5rem !important;
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 0.5rem !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button {
            background: var(--slmm-dark-surface) !important;
            border: 2px solid var(--slmm-dark-border) !important;
            color: var(--slmm-text-secondary) !important;
            padding: 0.5rem 1rem !important;
            border-radius: 6px !important;
            font-weight: 500 !important;
            text-decoration: none !important;
            transition: all 0.3s ease !important;
            box-shadow: none !important;
            text-shadow: none !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button:hover {
            background: var(--slmm-dark-surface-hover) !important;
            border-color: var(--slmm-primary) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button-primary {
            background: var(--slmm-primary) !important;
            border-color: var(--slmm-primary) !important;
            color: white !important;
        }

        .content-freshness-dark-theme .content-freshness-filters .button-primary:hover {
            background: var(--slmm-primary-hover) !important;
            border-color: var(--slmm-primary-hover) !important;
        }

        /* Action buttons */
        .content-freshness-dark-theme #export-csv, 
        .content-freshness-dark-theme #reset-cpt-filter, 
        .content-freshness-dark-theme #update-cpt-visibility, 
        .content-freshness-dark-theme #toggle-cpt {
            background: var(--slmm-primary) !important;
            border: none !important;
            border-radius: 6px !important;
            color: white !important;
            padding: 0.5rem 1rem !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            margin-right: 0.5rem !important;
            margin-bottom: 0.5rem !important;
            box-shadow: none !important;
            text-shadow: none !important;
        }

        .content-freshness-dark-theme #export-csv:hover, 
        .content-freshness-dark-theme #reset-cpt-filter:hover, 
        .content-freshness-dark-theme #update-cpt-visibility:hover, 
        .content-freshness-dark-theme #toggle-cpt:hover {
            background: var(--slmm-primary-hover) !important;
            transform: translateY(-1px) !important;
        }

        /* Form elements */
        .content-freshness-dark-theme #revision_depth {
            background: var(--slmm-dark-surface) !important;
            border: 2px solid var(--slmm-dark-border) !important;
            border-radius: 6px !important;
            color: var(--slmm-text-primary) !important;
            padding: 0.5rem !important;
            margin-bottom: 1rem !important;
        }

        .content-freshness-dark-theme #revision_depth:focus {
            outline: none !important;
            border-color: var(--slmm-primary) !important;
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
        }

        .content-freshness-dark-theme #revision_depth option {
            background: var(--slmm-dark-surface) !important;
            color: var(--slmm-text-primary) !important;
        }

        /* Table styling */
        .content-freshness-dark-theme .wp-list-table {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            border-radius: 8px !important;
            overflow: hidden !important;
        }

        .content-freshness-dark-theme .wp-list-table th {
            background: var(--slmm-dark-bg) !important;
            color: var(--slmm-text-primary) !important;
            border-bottom: 1px solid var(--slmm-dark-border) !important;
            font-weight: 600 !important;
            padding: 1rem 0.75rem !important;
        }

        .content-freshness-dark-theme .wp-list-table td {
            background: var(--slmm-dark-surface) !important;
            color: var(--slmm-text-secondary) !important;
            border-bottom: 1px solid var(--slmm-dark-border-subtle) !important;
            padding: 0.75rem !important;
        }

        .content-freshness-dark-theme .wp-list-table tbody tr:hover td {
            background: var(--slmm-dark-surface-hover) !important;
        }

        /* Alternating row colors */
        .content-freshness-dark-theme .wp-list-table tbody tr:nth-child(even) td {
            background: rgba(255, 255, 255, 0.02) !important;
        }

        .content-freshness-dark-theme .wp-list-table tbody tr:nth-child(even):hover td {
            background: var(--slmm-dark-surface-hover) !important;
        }

        .content-freshness-dark-theme .wp-list-table .row-actions a {
            color: var(--slmm-primary) !important;
            text-decoration: none !important;
        }

        .content-freshness-dark-theme .wp-list-table .row-actions a:hover {
            color: var(--slmm-primary-hover) !important;
        }

        .content-freshness-dark-theme .wp-list-table strong {
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .wp-list-table th a {
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .wp-list-table th a:hover {
            color: var(--slmm-primary) !important;
        }

        /* Pagination */
        .content-freshness-dark-theme .tablenav {
            background: var(--slmm-dark-bg) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .pagination-links a, 
        .content-freshness-dark-theme .pagination-links span {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .pagination-links a:hover {
            background: var(--slmm-primary) !important;
            border-color: var(--slmm-primary) !important;
            color: white !important;
        }

        .content-freshness-dark-theme .pagination-links .current {
            background: var(--slmm-primary) !important;
            border-color: var(--slmm-primary) !important;
            color: white !important;
        }

        .content-freshness-dark-theme .displaying-num {
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .paging-input input {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            color: var(--slmm-text-primary) !important;
        }

        .content-freshness-dark-theme .tablenav-paging-text {
            color: var(--slmm-text-primary) !important;
        }

        /* Additional options styling */
        .content-freshness-dark-theme #additional-options {
            background: var(--slmm-dark-surface) !important;
            border: 1px solid var(--slmm-dark-border) !important;
            border-radius: 8px !important;
            padding: 1rem !important;
            margin-bottom: 1rem !important;
        }

        .content-freshness-dark-theme #additional-options label {
            color: var(--slmm-text-primary) !important;
            font-weight: 500 !important;
            margin-bottom: 0.5rem !important;
            display: block !important;
        }
    </style>
    <div class="wrap content-freshness-dark-theme">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

        <div class="content-freshness-filters">
            <a href="?page=content-freshness&content_type=all" class="button<?php echo $content_type === 'all' ? ' button-primary' : ''; ?>">All</a>
            <a href="?page=content-freshness&content_type=post" class="button<?php echo $content_type === 'post' ? ' button-primary' : ''; ?>">Posts</a>
            <a href="?page=content-freshness&content_type=page" class="button<?php echo $content_type === 'page' ? ' button-primary' : ''; ?>">Pages</a>
            <?php
            $non_empty_cpts = content_freshness_get_non_empty_cpts();
            $visible_cpts = get_option('content_freshness_visible_cpts', array());
            foreach ($non_empty_cpts as $post_type) {
                if (in_array($post_type->name, $visible_cpts)) {
                    echo '<a href="?page=content-freshness&content_type=' . esc_attr($post_type->name) . '" class="button' . ($content_type === $post_type->name ? ' button-primary' : '') . '">' . esc_html($post_type->label) . ' (' . wp_count_posts($post_type->name)->publish . ')</a> ';
                }
            }
            ?>
            <button id="toggle-cpt" class="button">Show/Hide Additional Options</button>
        </div>

        <div id="additional-options" style="display: none; margin-top: 10px;">
            <?php
            foreach ($non_empty_cpts as $post_type) {
                if (!in_array($post_type->name, $visible_cpts)) {
                    echo '<label><input type="checkbox" name="cpt_visibility[]" value="' . esc_attr($post_type->name) . '"> ' . esc_html($post_type->label) . ' (' . wp_count_posts($post_type->name)->publish . ')</label><br>';
                }
            }
            ?>
            <button id="update-cpt-visibility" class="button" style="margin-top: 5px">View CPT</button>
        </div>

        <div style="margin-top: 10px; margin-bottom: 10px;">
            <button id="export-csv" class="button">Export to CSV</button>
            <button id="reset-cpt-filter" class="button">Reset CPTs</button>
        </div>

        <form id="content-freshness-filter-form" method="post">
            <input type="hidden" name="page" value="content-freshness">
            <select name="revision_depth" id="revision_depth">
                <?php for ($i = 1; $i <= 5; $i++) : ?>
                    <option value="<?php echo $i; ?>"<?php selected($revision_depth, $i); ?>><?php echo $i; ?> Revision<?php echo $i > 1 ? 's' : ''; ?> Back</option>
                <?php endfor; ?>
            </select>
            <?php $content_freshness_table->display(); ?>
        </form>
    </div>
    <?php
}


// Custom WP_List_Table class
class Content_Freshness_List_Table extends WP_List_Table {
    protected function get_revision_for_comparison($post_id, $revision_depth) {
        $revisions = wp_get_post_revisions($post_id, array(
            'posts_per_page' => $revision_depth,
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        if (empty($revisions)) {
            return null;
        }

        return end($revisions);
    }
    public function prepare_items($content_type = 'all', $revision_depth = 1, $hidden_cpts = array()) {
        $columns = $this->get_columns();
        $hidden = array();
        $sortable = $this->get_sortable_columns();
    
        $this->_column_headers = array($columns, $hidden, $sortable);
    
        $user = get_current_user_id();
        $screen = get_current_screen();
        $option = $screen->get_option('per_page', 'option');
        $per_page = get_user_meta($user, $option, true);
        if (empty($per_page) || $per_page < 1) {
            $per_page = $screen->get_option('per_page', 'default');
        }
    
        $current_page = $this->get_pagenum();
    
        $post_types = array('post', 'page');
        if ($content_type === 'all') {
            $filtered_cpts = isset($_POST['cpt_filter']) ? $_POST['cpt_filter'] : array();
            $post_types = array_merge($post_types, array_diff($filtered_cpts, $hidden_cpts));
        } elseif ($content_type !== 'post' && $content_type !== 'page' && !in_array($content_type, $hidden_cpts)) {
            $post_types = array($content_type);
        }
    
    
        $args = array(
            'post_type' => $post_types,
            'posts_per_page' => $per_page,
            'paged' => $current_page,
            'post_status' => 'publish',
            'orderby' => 'modified',
            'order' => 'DESC',
        );
    
        $query = new WP_Query($args);
    
        $this->items = array_map(function($post) use ($revision_depth) {
            return $this->prepare_item_for_display($post, $revision_depth);
        }, $query->posts);
    
        $total_items = $query->found_posts;
    
        $this->set_pagination_args(array(
            'total_items' => $total_items,
            'per_page' => $per_page,
            'total_pages' => ceil($total_items / $per_page)
        ));
    }

    public function get_columns() {
        return array(
            'cb' => '<input type="checkbox" />',
            'title' => 'Title',
            'url' => 'URL',
            'publish_date' => 'Publish Date',
            'last_modified' => 'Last Modified',
            'change_percentage' => 'Content Change %',
            'freshness_indicator' => 'Freshness Indicator - Since Publication',
            'actions' => 'Actions',
        );
    }
    public function get_sortable_columns() {
        return array(
            'title' => array('title', false),
            'publish_date' => array('publish_date', false),
            'last_modified' => array('last_modified', true),
            'change_percentage' => array('change_percentage', false),
        );
    }

   protected function column_default($item, $column_name) {
    switch ($column_name) {
        case 'title':
        case 'url':
        case 'publish_date':
        case 'last_modified':
        case 'change_percentage':
        case 'revision_date':
        case 'freshness_indicator':
            return $item[$column_name];
        default:
            return '';
    }
}

    protected function column_cb($item) {
        return sprintf(
            '<input type="checkbox" name="post[]" value="%s" />', $item['ID']
        );
    }

    protected function column_title($item) {
        $title = '<strong>' . $item['title'] . '</strong>';
        return $title;
    }

    protected function column_actions($item) {
        $actions = array(
            'edit' => sprintf('<a href="%s">Edit</a>', get_edit_post_link($item['ID'])),
            'view' => sprintf('<a href="%s">View</a>', get_permalink($item['ID'])),
        );
    
        return $this->row_actions($actions);
    }
    protected function prepare_item_for_display($post, $revision_depth) {
        $revisions = wp_get_post_revisions($post->ID, array(
            'posts_per_page' => $revision_depth + 1,  // Include one more revision
            'orderby' => 'date',
            'order' => 'DESC'
        ));
    
        $cumulative_change = array(
            'percentage' => 0,
            'word_diff' => 0,
            'added' => 0,
            'removed' => 0,
            'total_changes' => 0,
            'hidden_divs_added' => 0,
            'hidden_div_words' => 0,
            'youtube_added' => 0,
            'images_added' => 0
        );
    
        $current_content = $post->post_content;
        $previous_content = $current_content;
    
        if (!empty($revisions)) {
            // Compare current version with the most recent revision
            $latest_revision = array_shift($revisions);
            $change = $this->calculate_change_percentage($latest_revision->post_content, $current_content);
            $this->update_cumulative_change($cumulative_change, $change);
    
            // Compare remaining revisions
            foreach ($revisions as $revision) {
                $change = $this->calculate_change_percentage($revision->post_content, $previous_content);
                $this->update_cumulative_change($cumulative_change, $change);
                $previous_content = $revision->post_content;
            }
        }
    
        $publish_date = get_the_date('Y-m-d', $post->ID);
        $freshness_indicator = $this->get_freshness_indicator($publish_date);
    
        $change_percentage = $cumulative_change['percentage'] > 0 ? 
            $this->get_detailed_change_info($cumulative_change) : 
            'No change';
    
        return array(
            'ID' => $post->ID,
            'title' => $post->post_title,
            'url' => str_replace(home_url(), '', get_permalink($post->ID)),
            'publish_date' => get_the_date('d/m/Y', $post->ID),
            'last_modified' => get_the_modified_date('d/m/Y', $post->ID),
            'change_percentage' => $change_percentage,
            'freshness_indicator' => $freshness_indicator,
        );
    }
    
    // Add this new helper method
    private function update_cumulative_change(&$cumulative_change, $change) {
        $cumulative_change['percentage'] += $change['percentage'];
        $cumulative_change['word_diff'] += abs($change['word_diff']);
        $cumulative_change['added'] += $change['added'];
        $cumulative_change['removed'] += $change['removed'];
        $cumulative_change['total_changes'] += $change['total_changes'];
        $cumulative_change['hidden_divs_added'] += $change['hidden_divs_added'];
        $cumulative_change['hidden_div_words'] += $change['hidden_div_words'];
        $cumulative_change['youtube_added'] += $change['youtube_added'];
        $cumulative_change['images_added'] += $change['images_added'];
    }
public function calculate_change_percentage($original, $revised) {
    $original_content = $original;
    $revised_content = $revised;

    // Count hidden divs
    $hidden_divs_original = preg_match_all('/<div[^>]*style\s*=\s*["\'][^"\']*display\s*:\s*none[^"\']*["\'][^>]*>/i', $original_content, $matches);
    $hidden_divs_revised = preg_match_all('/<div[^>]*style\s*=\s*["\'][^"\']*display\s*:\s*none[^"\']*["\'][^>]*>/i', $revised_content, $matches);
    $hidden_divs_added = max(0, $hidden_divs_revised - $hidden_divs_original);

    // Count YouTube videos
    $youtube_original = preg_match_all('/\[embed\]https?:\/\/(www\.)?youtube\.com/i', $original_content, $matches);
    $youtube_revised = preg_match_all('/\[embed\]https?:\/\/(www\.)?youtube\.com/i', $revised_content, $matches);
    $youtube_added = max(0, $youtube_revised - $youtube_original);

    // Count images
    $images_original = preg_match_all('/<img[^>]+>/i', $original_content, $matches);
    $images_revised = preg_match_all('/<img[^>]+>/i', $revised_content, $matches);
    $images_added = max(0, $images_revised - $images_original);

    // Count words in hidden divs
    preg_match_all('/<div[^>]*style\s*=\s*["\'][^"\']*display\s*:\s*none[^"\']*["\'][^>]*>(.*?)<\/div>/is', $revised_content, $hidden_div_matches);
    $hidden_div_words = 0;
    foreach ($hidden_div_matches[1] as $hidden_content) {
        $hidden_div_words += str_word_count(strip_tags($hidden_content));
    }

    // Strip tags for word count
    $original_content = strip_tags($original_content);
    $revised_content = strip_tags($revised_content);

    $original_words = str_word_count($original_content);
    $revised_words = str_word_count($revised_content);

    $original_array = explode(' ', $original_content);
    $revised_array = explode(' ', $revised_content);

    $added_words = array_diff($revised_array, $original_array);
    $removed_words = array_diff($original_array, $revised_array);

    $added_count = count($added_words);
    $removed_count = count($removed_words);
    $total_changes = $added_count + $removed_count;

    if ($original_words === 0 && $revised_words === 0) {
        return array(
            'percentage' => 0,
            'word_diff' => 0,
            'added' => 0,
            'removed' => 0,
            'total_changes' => 0,
            'hidden_divs_added' => $hidden_divs_added,
            'hidden_div_words' => $hidden_div_words,
            'youtube_added' => $youtube_added,
            'images_added' => $images_added
        );
    }

    if ($original_words === 0) {
        return array(
            'percentage' => 100,
            'word_diff' => $revised_words,
            'added' => $revised_words,
            'removed' => 0,
            'total_changes' => $revised_words,
            'hidden_divs_added' => $hidden_divs_added,
            'hidden_div_words' => $hidden_div_words,
            'youtube_added' => $youtube_added,
            'images_added' => $images_added
        );
    }

    $percentage = ($total_changes / max($original_words, $revised_words)) * 100;

    return array(
        'percentage' => round($percentage, 2),
        'word_diff' => abs($revised_words - $original_words),
        'added' => $added_count,
        'removed' => $removed_count,
        'total_changes' => $total_changes,
        'hidden_divs_added' => $hidden_divs_added,
        'hidden_div_words' => $hidden_div_words,
        'youtube_added' => $youtube_added,
        'images_added' => $images_added
    );
}

   // In the Content_Freshness_List_Table class, find the get_detailed_change_info function
public function get_detailed_change_info($change) {
    $info = $change['percentage'] . '% (' . $change['word_diff'] . ' words net change, ' . 
            $change['total_changes'] . ' words affected)';
    $details = 'Added: ' . $change['added'] . ' words, Removed: ' . $change['removed'] . ' words';
    
    $visual_changes = array();
    if ($change['youtube_added'] > 0) {
        $visual_changes[] = $change['youtube_added'] . ' YouTube video' . ($change['youtube_added'] > 1 ? 's' : '') . ' added';
    }
    if ($change['images_added'] > 0) {
        $visual_changes[] = $change['images_added'] . ' image' . ($change['images_added'] > 1 ? 's' : '') . ' added';
    }
    if ($change['hidden_divs_added'] > 0) {
        $visual_changes[] = $change['hidden_divs_added'] . ' hidden div' . ($change['hidden_divs_added'] > 1 ? 's' : '') . ' added';
    }
    
    if (!empty($visual_changes)) {
        $details .= '<br>Visual changes: ' . implode(', ', $visual_changes);
    }
    
    return $info . '<br><span style="font-size: 0.9em; color: #666;">' . $details . '</span>';
}

    public function get_freshness_indicator($date) {
        if (!$date) {
            return '<span class="freshness-indicator">Unknown - Unable to determine freshness</span>';
        }
    
        try {
            $now = new DateTime();
            $publish_date = new DateTime($date);
            $diff = $now->diff($publish_date);
            $months = $diff->m + ($diff->y * 12);
            $days = $diff->d;
    
            $indicator = sprintf('%d months %d days', $months, $days);
    
            $bg_color = '';
            $comment = '';
            if ($months < 3 || ($months == 3 && $days <= 12)) {
                $bg_color = '#22C55E'; // Dark theme green
                $comment = 'Content is fresh as a daisy';
            } elseif ($months < 6) {
                $bg_color = '#FCD34D'; // Dark theme yellow
                $comment = 'Content is still good, but consider refreshing soon';
            } elseif ($months < 8) {
                $bg_color = '#FB923C'; // Dark theme orange
                $comment = 'Time to bring it back to life!';
            } else {
                $bg_color = '#F87171'; // Dark theme red
                $comment = 'Content needs urgent attention!';
            }
    
            // Determine text color based on background
            $text_color = ($months < 3 || ($months == 3 && $days <= 12)) ? 'white' : '#1F2937';
    
            return sprintf(
                '<span class="freshness-indicator"><span style="background-color: %s; color: %s; padding: 4px 8px; border-radius: 4px; font-weight: 600;">%s</span> - %s</span>',
                $bg_color,
                $text_color,
                $indicator,
                $comment
            );
        } catch (Exception $e) {
            return '<span class="freshness-indicator">Error - ' . esc_html($e->getMessage()) . '</span>';
        }
    }
}

// AJAX handler for exporting to CSV
add_action('wp_ajax_export_content_freshness_csv', 'content_freshness_export_csv');


function content_freshness_export_csv() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $filename = 'content_freshness_' . date('Y-m-d') . '.csv';
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    $post_types = array_merge(array('post', 'page'), get_post_types(array('_builtin' => false)));
    $hidden_cpts = (array) get_option('content_freshness_hidden_cpts', array());
    
    foreach ($post_types as $post_type) {
        // Include hidden CPTs in the CSV export
        $args = array(
            'post_type' => $post_type,
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderby' => 'modified',
            'order' => 'DESC',
        );

        $query = new WP_Query($args);

        if ($query->have_posts()) {
            fputcsv($output, array(strtoupper($post_type)));
            fputcsv($output, array('Title', 'URL', 'Publish Date', 'Last Modified', 'Change %', 'Freshness Indicator'));

            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();
                $post = get_post($post_id);
                
                $table = new Content_Freshness_List_Table();
                $item = $table->prepare_item_for_display($post, 1);

                $url = str_replace(home_url(), '', get_permalink($post_id));
                $publish_date = get_the_date('Y-m-d', $post_id);
                $last_modified = get_the_modified_date('Y-m-d', $post_id);
                
                // Calculate change percentage
                $revisions = wp_get_post_revisions($post_id, array('posts_per_page' => 1));
                $change_percentage = '0%';
                if (!empty($revisions)) {
                    $revision = array_shift($revisions);
                    $change = $table->calculate_change_percentage($revision->post_content, $post->post_content);
                    $change_percentage = $change['percentage'] . '% (' . $change['word_diff'] . ' words)';
                }

                // Get freshness indicator
                $freshness_indicator = strip_tags($table->get_freshness_indicator($publish_date));

                fputcsv($output, array(
                    $post->post_title,
                    $url,
                    $publish_date,
                    $last_modified,
                    $change_percentage,
                    $freshness_indicator
                ));
            }

            fputcsv($output, array('')); // Empty line between post types
        }

        wp_reset_postdata();
    }

    fclose($output);
    exit;
}

add_action('wp_ajax_update_cpt_filter', 'content_freshness_update_cpt_filter');

function content_freshness_update_cpt_filter() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $cpt_filter = isset($_POST['cpt_filter']) ? $_POST['cpt_filter'] : array();
    update_option('content_freshness_cpt_filter', $cpt_filter);
    wp_die();
}
add_action('wp_ajax_update_hidden_cpts', 'content_freshness_update_hidden_cpts');

function content_freshness_update_hidden_cpts() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $hidden_cpts = isset($_POST['hidden_cpts']) ? $_POST['hidden_cpts'] : array();
    $all_cpts = get_post_types(array('_builtin' => false), 'names');
    $hidden_cpts = array_intersect($hidden_cpts, $all_cpts); // Ensure only valid CPTs are hidden
    update_option('content_freshness_hidden_cpts', $hidden_cpts);
    wp_die();
}

add_action('wp_ajax_reset_cpt_filter', 'content_freshness_reset_cpt_filter');

function content_freshness_reset_cpt_filter() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $visible_cpts = array('post', 'page');
    update_option('content_freshness_visible_cpts', $visible_cpts);
    wp_die();
}

function content_freshness_get_non_empty_cpts() {
    $all_post_types = get_post_types(array('show_ui' => true), 'objects');
    $non_empty_cpts = array();

    foreach ($all_post_types as $post_type) {
        if ($post_type->name !== 'post' && $post_type->name !== 'page') {
            $count = wp_count_posts($post_type->name)->publish;
            if ($count > 0) {
                $non_empty_cpts[$post_type->name] = $post_type;
            }
        }
    }

    return $non_empty_cpts;
}
add_action('wp_ajax_update_cpt_visibility', 'content_freshness_update_cpt_visibility');

function content_freshness_update_cpt_visibility() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $visible_cpts = isset($_POST['visible_cpts']) ? $_POST['visible_cpts'] : array();
    update_option('content_freshness_visible_cpts', $visible_cpts);
    wp_die();
}