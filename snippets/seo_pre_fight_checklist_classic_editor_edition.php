<?php
if (!defined('ABSPATH')) {
    die();
}

// Only initialize if checklist is enabled in settings
$options = get_option('chatgpt_generator_options', array());
$checklist_enabled = isset($options['enable_checklist']) ? $options['enable_checklist'] : false;

if ($checklist_enabled) {
    // Register the meta box
    add_action('add_meta_boxes', 'custom_checklist_meta_box');

    function custom_checklist_meta_box() {
        // Get all post types
        $post_types = get_post_types(['public' => true]);
        
        // Add meta box to all post types
        foreach ($post_types as $post_type) {
            add_meta_box(
                'custom_checklist',
                'Content Checklist',
                'custom_checklist_meta_box_callback',
                $post_type,
                'normal',
                'high'
            );
        }
    }
    function count_hidden_divs($content) {
        $pattern = '/<div[^>]*style\s*=\s*["\'][^"\']*display\s*:\s*none[^"\']*["\'][^>]*>/i';
        preg_match_all($pattern, $content, $matches);
        return count($matches[0]);
    }

    function slmm_detect_and_parse_schema($post_id) {
        $post_url = get_permalink($post_id);
        $response = wp_remote_get($post_url);
        if (is_wp_error($response)) {
            return array('error' => 'Error fetching page content: ' . $response->get_error_message());
        }

        $content = wp_remote_retrieve_body($response);
        $schemas = array();

        // Detect JSON-LD schema
        if (preg_match_all('/<script\s+type=(["\'])application\/ld\+json\1.*?>(.*?)<\/script>/is', $content, $matches)) {
            foreach ($matches[2] as $json) {
                $schemas[] = trim($json);
            }
        }

        $all_schema = implode("\n\n", $schemas);
        $line_count = substr_count($all_schema, "\n") + 1;

        // Simple validation check
        $schema_errors = false;
        foreach ($schemas as $schema_json) {
            json_decode($schema_json);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $schema_errors = true;
                break;
            }
        }

        return array(
            'all_schema' => $all_schema,
            'line_count' => $line_count,
            'schema_errors' => $schema_errors
        );
    }

    function slmm_parse_schemas($schemas) {
        $parsed = array();
        $all_schema = '';
        foreach ($schemas as $index => $schema) {
            $type = isset($schema['@type']) ? $schema['@type'] : 'Unknown';
            if (!isset($parsed[$type])) {
                $parsed[$type] = array('count' => 0, 'errors' => 0, 'warnings' => 0, 'items' => array());
            }
            $parsed[$type]['count']++;
            $parsed[$type]['items'][] = json_encode($schema, JSON_PRETTY_PRINT);
            $all_schema .= json_encode($schema, JSON_PRETTY_PRINT) . "\n\n";
        }
        return array('parsed' => $parsed, 'all_schema' => $all_schema);
    }

    function custom_checklist_meta_box_callback($post) {
        // Retrieve existing values
        $checklist = get_post_meta($post->ID, '_custom_checklist', true);
        if (!is_array($checklist)) {
            $checklist = array();
        }

        // Retrieve existing notes
        $notes = get_post_meta($post->ID, '_custom_notes', true);
        // Count hidden divs
$post_content = $post->post_content;
$hidden_div_count = count_hidden_divs($post_content);
        if (!is_array($notes)) {
            $notes = array();
        }

        // Nonce field for security
        wp_nonce_field('custom_checklist_nonce_action', 'custom_checklist_nonce');

        // Detect schema
        $schema_data = slmm_detect_and_parse_schema($post->ID);
        $schema_detected = !empty($schema_data) && !isset($schema_data['error']);

        // Output control buttons with Select All on left, Expand/Collapse on right
        echo '<div style="display: flex; justify-content: space-between; margin-bottom: 10px; font-size: 15px;">';
        echo '<div>';
        echo '<button type="button" id="select-all" class="button">Select All</button>';
        echo '</div>';
        echo '<div style="gap: 5px; display: flex;">';
        echo '<button type="button" id="expand-all" class="button">Expand All</button>';
        echo '<button type="button" id="collapse-all" class="button">Collapse All</button>';
        echo '</div>';
        echo '</div>';

        // Output the accordion
        echo '<div id="custom-checklist-accordion">';

       // Schema section
echo '<div class="accordion-section">';
echo '<h3 class="accordion-header">';
$schema_color = 'red';
if ($schema_data['all_schema']) {
    $schema_color = $schema_data['schema_errors'] ? 'orange' : 'green';
}
echo '<span class="schema-indicator" style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ' . $schema_color . '; margin-right: 5px;"></span>';
echo 'SCHEMA';
echo '<span class="chevron">&#9660;</span>';
echo '</h3>';
echo '<div class="accordion-content" style="display: none;">'; // Start closed
if (!is_singular()) { // Only show rescan button on non-singular pages
    echo '<button id="rescan-schema" class="button">Rescan Schema</button>';
    echo '<span id="rescan-result" style="margin-left: 10px;"></span>';
                echo '<br><br>';
            }
        echo '<button id="validate-schema" class="button">Validate Schema</button>'; // Move the button here
        // Display schema error message if there are errors
        if ($schema_data['schema_errors']) {
            echo '<div style="display: inline-block; vertical-align: middle; background-color: red; color: white; border-radius: 4px; padding: 3px 20px; margin-left: 10px; margin-top: 9px; height: 25px; line-height: 23px;">schema appears to have errors</div>';
        }

        if ($schema_data['all_schema']) {
            echo '<div class="schema-item">';
            echo '<h4 class="schema-header" style="cursor: pointer;">All Schema --- <span style="font-weight: normal; font-size: 0.9em;">There are ' . $schema_data['line_count'] . ' lines of schema detected</span> <span class="chevron">&#9660;</span></h4>';
            echo '<div class="schema-content" style="display: none;">'; // Start closed
            echo '<pre id="all-schema-content" style="white-space: pre-wrap; word-wrap: break-word;">' . esc_html($schema_data['all_schema']) . '</pre>';
            echo '</div>';
            echo '</div>';
        } else {
            echo '<p>No schema detected or error occurred.</p>';
        }
        echo '</div>';
        echo '</div>';

        // Define the sections and items
        $sections = array(
        
            'FEATURED IMAGES' => array(
                'Ensure you have featured image - ideally 1200x900',
                'When it comes to the FEATURED IMAGE URL you can use LONGER PHRASES than your URL'
            ),
            'HIDDEN DIV' => array(  
                'Hidden Div (do you have Cora content?)'
            ),  
            'NOTES' => $notes, // This will be handled differently
        );

        $section_index = 0; // To open the first accordion by default
        $has_featured_image = has_post_thumbnail($post->ID);
        $featured_image_status = $has_featured_image ? 'green' : 'red';
        $featured_image_text = $has_featured_image ? '' : 'Featured Image not detected';
        
        
        foreach ($sections as $section_title => $items) {
            if ($section_title !== 'SCHEMA' && $section_title !== 'NOTES') {
                $section_key = sanitize_title($section_title);
                $section_checked = isset($checklist['section_' . $section_key]) ? 'checked' : '';
                $section_label_style = $section_checked ? 'text-decoration: line-through; color: #ccc;' : '';
                echo '<div class="accordion-section">';
                echo '<h3 class="accordion-header" style="' . $section_label_style . '">';
                echo '<label style="display: flex; align-items: center;">';
                echo '<input type="checkbox" class="section-checkbox" data-section="' . esc_attr($section_key) . '" name="custom_checklist[section_' . esc_attr($section_key) . ']" ' . $section_checked . ' style="margin-right: 5px;"> ';
                echo esc_html($section_title);
                
                // Add Featured Image status dot and text
                if ($section_title === 'FEATURED IMAGES') {
                    echo '<span class="featured-image-indicator" style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ' . $featured_image_status . '; margin-left: 10px;"></span>';
                    echo '<span class="featured-image-status" style="margin-left: 5px; font-size: 0.8em; font-weight: normal;">' . $featured_image_text . '</span>';
                }
                
                // Add Hidden Div status dot and text
                if ($section_title === 'HIDDEN DIV') {
                    echo '<span class="hidden-div-indicator" style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ' . ($hidden_div_count > 0 ? 'green' : '') . '; margin-left: 10px;"></span>';
                    echo '<span class="hidden-div-status" style="margin-left: 5px; font-size: 0.8em; font-weight: normal;">' . ($hidden_div_count > 0 ? $hidden_div_count . ' hidden div' . ($hidden_div_count > 1 ? 's' : '') . ' detected' : 'No hidden divs detected') . '</span>';
                echo '<script>
                    jQuery(document).ready(function($) {
                        function isVisualEditorActive() {
                            return $("#wp-content-wrap").hasClass("tmce-active");
                        }

                        function getEditorContent() {
                            if (isVisualEditorActive()) {
                                return tinymce.get("content").getContent({format: "html"});
                            } else {
                                return $("#content").val();
                            }
                        }

                        function updateHiddenDivIndicator() {
                            var content = getEditorContent();
                            var hiddenDivs = (content.match(/<div[^>]*(?:style="[^"]*display:\s*none[^"]*"|data-nosnippet)[^>]*>/gi) || []).length;
                            var indicator = document.querySelector(".hidden-div-indicator");
                            var count = document.querySelector(".hidden-div-count");
                            var status = document.querySelector(".hidden-div-status");
                            
                            if (hiddenDivs > 0) {
                                indicator.style.backgroundColor = "green";
                                count.textContent = "";
                                status.textContent = hiddenDivs + " hidden div" + (hiddenDivs > 1 ? "s" : "") + " detected";
                            } else {
                                indicator.style.backgroundColor = "";
                                count.textContent = "";
                                status.textContent = "No hidden divs detected";
                            }
                        }

                        function forceUpdateHiddenDivIndicator() {
                            setTimeout(updateHiddenDivIndicator, 100);
                        }

                        // Update on tab switch
                        $(document).on("click", ".wp-switch-editor", forceUpdateHiddenDivIndicator);

                        // Update on hidden div button click
                        $("#hidden-div").on("click", forceUpdateHiddenDivIndicator);

                        // Update on editor init and content change
                        if (typeof tinymce !== "undefined") {
                            tinymce.on("AddEditor", function(e) {
                                var editor = e.editor;
                                if (editor.id === "content") {
                                    editor.on("init", forceUpdateHiddenDivIndicator);
                                    editor.on("change keyup input NodeChange", forceUpdateHiddenDivIndicator);
                                }
                            });
                        }

                        // Update on text editor changes
                        $("#content").on("input change keyup", forceUpdateHiddenDivIndicator);

                        // Initial update
                        forceUpdateHiddenDivIndicator();

                        // Update when switching between visual/text editors
                        $(document).on("tinymce-editor-setup", forceUpdateHiddenDivIndicator);
                        $(document).on("tinymce-editor-init", forceUpdateHiddenDivIndicator);
                    });
                </script>';
                }
                
                echo '</label>';
                echo '<span class="chevron">&#9660;</span>';
                echo '</h3>';
                echo '<div class="accordion-content">';
                echo '<ul style="list-style-type: none; margin: 0; padding: 0;">';
                foreach ($items as $item) {
                    $item_key = sanitize_title($section_title . '_' . $item);
                    $is_featured_image_item = ($section_title === 'FEATURED IMAGES' && strpos($item, 'Ensure you have featured image') !== false);
                    
                    if ($is_featured_image_item) {
                        $checked = $has_featured_image ? 'checked' : '';
                        $disabled = $has_featured_image ? '' : 'disabled';
                    } else {
                        $checked = isset($checklist[$item_key]) ? 'checked' : '';
                        $disabled = '';
                    }
                    
                    $label_style = $checked ? 'text-decoration: line-through; color: #ccc;' : '';
                
                    echo '<li>';
                    echo '<label style="display: flex; align-items: end; ' . $label_style . '">';
                    echo '<input type="checkbox" name="custom_checklist[' . esc_attr($item_key) . ']" data-section="' . esc_attr($section_key) . '" ' . $checked . ' ' . $disabled . ' style="margin-right: 5px;"> ';
                    echo esc_html($item);
                    if ($is_featured_image_item && !$has_featured_image) {
                        echo ' <span style="color: red; font-size: 0.8em; margin-left: 5px;">(Featured image required)</span>';
                    }
                    echo '</label>';
                    echo '</li>';
            
                }
                echo '</ul>';
                echo '</div>';
                echo '</div>';
            } elseif ($section_title === 'NOTES') {
                // NOTES section does not have a section checkbox
                echo '<div class="accordion-section">';
                echo '<h3 class="accordion-header">';
                echo esc_html($section_title) . ' <span class="chevron">&#9660;</span>';
                echo '</h3>';
                echo '<div class="accordion-content">';
                echo '<div id="notes-container">';
                if (!empty($notes)) {
                    foreach ($notes as $index => $note) {
                        $item_key = 'note_' . $index;
                        $checked = isset($checklist[$item_key]) ? 'checked' : '';
                        $label_style = $checked ? 'text-decoration: line-through; color: #ccc;' : '';

                        echo '<div class="note-item">';
                        echo '<label style="flex: block; align-items: end;">';
                        echo '<input type="checkbox" name="custom_checklist[' . esc_attr($item_key) . ']" ' . $checked . ' style="margin-right: 5px;"> ';
                        echo '<input type="text" name="custom_notes[' . esc_attr($index) . ']" value="' . esc_attr($note) . '" style="width: 80%; ' . $label_style . '"/>';
                        echo '</label>';
                        echo '<button type="button" class="remove-note button">-</button>';
                        echo '</div>';
                    }
                }
                // Blank note input for adding new notes
                echo '<div class="note-item">';
                echo '<label style="display: flex; align-items: center;">';
                echo '<input type="checkbox" disabled style="margin-right: 5px;"> ';
                echo '<input type="text" id="new-note-text" placeholder="Add a new note..." style="width: 80%;" />';
                echo '</label>';
                echo '<button type="button" id="add-note" class="button">+</button>';
                echo '</div>';
                echo '</div>'; // End of notes-container
                echo '</div>';
                echo '</div>';
            }

            $section_index++;
        }

        echo '</div>'; // End of accordion

        ?>
        <style>
            #custom-checklist-accordion .accordion-header {
                cursor: pointer;
                background: #f1f1f1;
                padding: 10px;
                margin: 0;
                border-bottom: 1px solid #ccc;
                position: relative;
                display: flex;
                align-items: center;
            }

            #custom-checklist-accordion .accordion-content {
                padding: 10px;
                border: 1px solid #ccc;
                border-top: none;
                display: none;
            }

            #custom-checklist-accordion .accordion-section {
                border: 1px solid #ccc;
                margin-bottom: 5px;
            }

            #custom-checklist-accordion .chevron {
                margin-left: auto;
                font-size: 16px;
            }

            .note-item {
                margin-bottom: 5px;
                display: flex;
                align-items: center;
            }

            .note-item label {
                display: flex;
                align-items: center;
                width: 100%;
            }

            .note-item input[type="text"] {
                margin-right: 5px;
                width: 80%;
            }

            .note-item button {
                vertical-align: middle;
                margin-left: 5px;
            }

            /* Confetti styles */
            #confetti-wrapper {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                overflow: hidden;
                z-index: 9999;
            }

            [class^="confetti-"] {
                position: absolute;
                animation: confetti-fall 4s linear forwards;
            }

            @keyframes confetti-fall {
                0% {
                    transform: translateY(0) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }

            /* Mobile responsiveness */
            @media (max-width: 600px) {
                #progress-container {
                    flex-direction: column;
                    align-items: flex-start;
                    margin-bottom: 10px;
                }

                #progress-container label {
                    margin-bottom: 5px;
                }

                #progress-bar {
                    width: 100%;
                }

                .note-item {
                    flex-wrap: wrap;
                }

                .note-item input[type="text"], .note-item button {
                    width: 100%;
                    margin-bottom: 5px;
                }

                .accordion-header {
                    flex-wrap: wrap;
                }

                #expand-all, #collapse-all {
                    width: 100%;
                    margin-bottom: 5px;
                }

                #custom-checklist-accordion .accordion-header input[type="checkbox"] {
                    margin-bottom: 5px;
                }

                #select-all {
                    width: 100%;
                    margin-bottom: 5px;
                }
            }

            .schema-item {
                margin-bottom: 10px;
                padding: 5px;
                border: 1px solid #ddd;
                background-color: #f9f9f9;
            }

            .schema-header {
                margin: 0 0 5px 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
            }

            .schema-header .chevron {
                margin-left: auto;
            }

            #validate-schema {
                margin-bottom: 10px;
                margin-top: 10px;
            }

            .accordion-content>#rescan-schema {
             display: none;
            }
            .column-schema .validate-schema { margin-left: 5px; }
            .featured-image-indicator {
    flex-shrink: 0;
}
.featured-image-status {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.hidden-div-indicator {
    flex-shrink: 0;
}
.hidden-div-status {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

        
        </style>
        <script>
        (function($){
            $(document).ready(function(){
                // Initialize accordions
                var headers = $('#custom-checklist-accordion .accordion-header');
                var contents = $('#custom-checklist-accordion .accordion-content');

                // Open the first accordion by default
                contents.first().show();
                headers.first().addClass('open');

                // Toggle accordion on header click (excluding clicks on checkboxes)
                $('#custom-checklist-accordion').on('click', '.accordion-header', function(e){
                    if ($(e.target).is('input[type="checkbox"]') || $(e.target).is('label')) {
                        return;
                    }
                    $(this).toggleClass('open');
                    $(this).next('.accordion-content').slideToggle();
                    updateChevron($(this));
                });

                // Update chevron direction
                function updateChevron(header) {
                    var chevron = header.find('.chevron');
                    if (header.hasClass('open')) {
                        chevron.html('&#9650;'); // Up arrow
                    } else {
                        chevron.html('&#9660;'); // Down arrow
                    }
                }

                // Initialize chevrons
                headers.each(function(){
                    updateChevron($(this));
                });

                // Expand All / Collapse All buttons
                $('#expand-all').click(function(){
                    contents.slideDown();
                    headers.addClass('open');
                    headers.each(function(){
                        updateChevron($(this));
                    });
                });

                $('#collapse-all').click(function(){
                    contents.slideUp();
                    headers.removeClass('open');
                    headers.each(function(){
                        updateChevron($(this));
                    });
                });

                // Select All / Deselect All button
                $('#select-all').click(function(){
                    var totalCheckboxes = $('#custom-checklist-accordion input[type="checkbox"]').not('.note-item input[type="checkbox"]');
                    var checkedCheckboxes = totalCheckboxes.filter(':checked');
                    var isSelectAll = checkedCheckboxes.length !== totalCheckboxes.length;

                    if (isSelectAll) {
                        totalCheckboxes.prop('checked', true);
                        $('#select-all').text('Deselect All');
                        $('#progress-label').text('Success!');
                        triggerConfetti();
                    } else {
                        totalCheckboxes.prop('checked', false);
                        $('#select-all').text('Select All');
                        $('#progress-label').text('Progress:');
                    }

                    // Update styles
                    $('#custom-checklist-accordion input[type="checkbox"]').trigger('change');
                    updateProgressBar();
                });

                // Apply strikethrough on page load
                $('#custom-checklist-accordion input[type="checkbox"]').each(function(){
                    if ($(this).is(':checked')) {
                        if ($(this).closest('.note-item').length > 0) {
                            $(this).siblings('input[type="text"]').css({'text-decoration':'line-through', 'color':'#ccc'});
                        } else if ($(this).hasClass('section-checkbox')) {
                            var section = $(this).closest('.accordion-section');
                            section.find('.accordion-header').css({'text-decoration':'line-through', 'color':'#ccc'});
                            section.find('input[type="checkbox"]').prop('checked', true);
                            section.find('label').css({'text-decoration':'line-through', 'color':'#ccc'});
                        } else {
                            $(this).parent('label').css({'text-decoration':'line-through', 'color':'#ccc'});
                        }
                    }
                });

                // Handle checkbox change
                $('#custom-checklist-accordion').on('change', 'input[type="checkbox"]', function(){
                    if ($(this).is(':checked')) {
                        if ($(this).closest('.note-item').length > 0) {
                            $(this).siblings('input[type="text"]').css({'text-decoration':'line-through', 'color':'#ccc'});
                        } else if ($(this).hasClass('section-checkbox')) {
                            // Section checkbox checked
                            var sectionKey = $(this).data('section');
                            var section = $(this).closest('.accordion-section');
                            section.find('input[type="checkbox"]').prop('checked', true);
                            section.find('.accordion-header').css({'text-decoration':'line-through', 'color':'#ccc'});
                            section.find('label').css({'text-decoration':'line-through', 'color':'#ccc'});
                        } else {
                            $(this).parent('label').css({'text-decoration':'line-through', 'color':'#ccc'});
                        }
                    } else {
                        if ($(this).closest('.note-item').length > 0) {
                            $(this).siblings('input[type="text"]').css({'text-decoration':'none', 'color':'inherit'});
                        } else if ($(this).hasClass('section-checkbox')) {
                            // Section checkbox unchecked
                            var sectionKey = $(this).data('section');
                            var section = $(this).closest('.accordion-section');
                            section.find('input[type="checkbox"]').prop('checked', false);
                            section.find('.accordion-header').css({'text-decoration':'none', 'color':'inherit'});
                            section.find('label').css({'text-decoration':'none', 'color':'inherit'});
                        } else {
                            $(this).parent('label').css({'text-decoration':'none', 'color':'inherit'});
                        }
                    }
                    // Check if all checkboxes in the section are checked
                    var section = $(this).closest('.accordion-section');
                    var checkboxes = section.find('input[type="checkbox"]').not('.section-checkbox');
                    var allChecked = checkboxes.length > 0 && checkboxes.length === checkboxes.filter(':checked').length;
                    if (allChecked) {
                        section.find('.accordion-header').css({'text-decoration':'line-through', 'color':'#ccc'});
                        section.find('.section-checkbox').prop('checked', true);
                    } else {
                        section.find('.accordion-header').css({'text-decoration':'none', 'color':'inherit'});
                        section.find('.section-checkbox').prop('checked', false);
                    }
                    // Update Select All / Deselect All button text
                    updateSelectAllButton();
                    // Update progress bar
                    updateProgressBar();
                });

                // Initialize section headers based on checkboxes
                $('.accordion-section').each(function(){
                    var section = $(this);
                    var checkboxes = section.find('input[type="checkbox"]').not('.section-checkbox');
                    var allChecked = checkboxes.length > 0 && checkboxes.length === checkboxes.filter(':checked').length;
                    if (allChecked) {
                        section.find('.accordion-header').css({'text-decoration':'line-through', 'color':'#ccc'});
                        section.find('.section-checkbox').prop('checked', true);
                    }
                });

                // Add new note
                $('#add-note').click(function(){
                    var noteText = $('#new-note-text').val();
                    if (noteText.trim() !== '') {
                        var index = $('#notes-container .note-item').length - 1; // Exclude the new note input
                        var itemKey = 'note_' + index;
                        var noteHtml = '<div class="note-item">';
                        noteHtml += '<label style="display: flex; align-items: center;">';
                        noteHtml += '<input type="checkbox" name="custom_checklist[' + itemKey + ']" style="margin-right: 5px;"> ';
                        noteHtml += '<input type="text" name="custom_notes[' + index + ']" value="' + noteText + '" style="width: 80%;" />';
                        noteHtml += '</label>';
                        noteHtml += '<button type="button" class="remove-note button">-</button>';
                        noteHtml += '</div>';
                        $(noteHtml).insertBefore($(this).closest('.note-item'));
                        $('#new-note-text').val('');
                    }
                });

                // Remove note
                $('#notes-container').on('click', '.remove-note', function(){
                    $(this).closest('.note-item').remove();
                });

                // Progress Bar
                function updateProgressBar() {
                    // Exclude NOTES section from progress calculation
                    var totalCheckboxes = $('#custom-checklist-accordion input[type="checkbox"]').not('.note-item input[type="checkbox"]').not('.section-checkbox');
                    var checkedCheckboxes = totalCheckboxes.filter(':checked');
                    var percentage = (checkedCheckboxes.length / totalCheckboxes.length) * 100;
                    $('#progress-fill').css('width', percentage + '%');
                    $('#progress-percentage').text(Math.round(percentage) + '%');
                    // Change color from red to green
                    var color = percentageToColor(percentage);
                    $('#progress-fill').css('background-color', color);
                    // Change label to "Success!" at 100%
                    if (percentage === 100) {
                        $('#progress-label').text('Success!');
                        // Show confetti animation
                        triggerConfetti();
                    } else {
                        $('#progress-label').text('Progress:');
                    }
                }

                function percentageToColor(percentage) {
                    var red = percentage < 50 ? 255 : Math.floor(255 - (percentage * 2 - 100) * 255 / 100);
                    var green = percentage > 50 ? 255 : Math.floor((percentage * 2) * 255 / 100);
                    return 'rgb(' + red + ',' + green + ',0)';
                }

                // Update Select All / Deselect All button text
                function updateSelectAllButton() {
                    var totalCheckboxes = $('#custom-checklist-accordion input[type="checkbox"]').not('.note-item input[type="checkbox"]');
                    var checkedCheckboxes = totalCheckboxes.filter(':checked');
                    if (checkedCheckboxes.length === totalCheckboxes.length) {
                        $('#select-all').text('Deselect All');
                    } else {
                        $('#select-all').text('Select All');
                    }
                }

                // Initialize progress bar and Select All button on page load
                updateProgressBar();
                updateSelectAllButton();

                $('#validate-schema').click(function(e) {
                    e.preventDefault();
                    var permalink = $('#sample-permalink').text().trim();
                    if (!permalink) {
                        alert('Unable to find the permalink. Please save the post first.');
                        return;
                    }
                    var validatorUrl = 'https://validator.schema.org/#url=' + encodeURIComponent(permalink);
                    window.open(validatorUrl, '_blank');
                });

                // Confetti Animation
                function triggerConfetti() {
                    // Only trigger once per completion
                    if (!$('#confetti-wrapper').length) {
                        // Create confetti wrapper
                        $('body').append('<div id="confetti-wrapper"></div>');
                        var $confettiWrapper = $('#confetti-wrapper');
                        var colors = ['#d13447', '#ffbf00', '#263672'];
                        for (var i = 0; i < 150; i++) {
                            var $confetti = $('<div class="confetti-' + i + '"></div>');
                            var width = Math.floor(Math.random() * 8) + 4;
                            var height = width * 0.4;
                            var color = colors[Math.floor(Math.random() * colors.length)];
                            var left = Math.floor(Math.random() * 100);
                            var animationDuration = (Math.random() * 1) + 3; // 3 to 4 seconds
                            var animationDelay = Math.random() * 0.5; // Up to 0.5 seconds delay
                            $confetti.css({
                                'width': width + 'px',
                                'height': height + 'px',
                                'background-color': color,
                                'top': '0%',
                                'left': left + '%',
                                'opacity': (Math.random() + 0.5),
                                'transform': 'rotate(' + (Math.random() * 360) + 'deg)',
                                'animation': 'confetti-fall ' + animationDuration + 's linear ' + animationDelay + 's forwards'
                            });
                            $confettiWrapper.append($confetti);
                        }
                        // Remove confetti after animation completes
                        setTimeout(function(){
                            $confettiWrapper.remove();
                        }, 4000); // Adjust time according to animation duration
                    }
                }

                // Handle schema accordion
                $('.schema-header').click(function(e) {
                    e.stopPropagation();
                    $(this).toggleClass('open');
                    $(this).find('.chevron').html($(this).hasClass('open') ? '&#9650;' : '&#9660;');
                    $(this).next('.schema-content').slideToggle();
                });

                // Display all schema without parsing
                var allSchemaContent = $('#all-schema-content').text();
                if (allSchemaContent) {
                    $('#all-schema-content').text(allSchemaContent);
                }

                // Ensure the Schema section starts closed
                $('#custom-checklist-accordion .accordion-section:first-child .accordion-content').hide();

                // Rescan Schema functionality
$('#rescan-schema').click(function(e) {
    e.preventDefault();
    var postId = $('#post_ID').val();
    var $button = $(this);
    var $resultSpan = $('#rescan-result');
    
    $button.prop('disabled', true);
    $resultSpan.text('Rescanning...');
    
    $.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'slmm_rescan_schema',
            post_id: postId,
            nonce: '<?php echo wp_create_nonce("slmm_rescan_schema_nonce"); ?>'
        },
        success: function(response) {
            if (response.success && !response.has_errors) {
                $resultSpan.html('<span style="color: green;">Schema is valid</span>');
                $('.schema-error-message').remove();
                $('.schema-indicator').css('background-color', 'green');
            } else if (response.success && response.has_errors) {
                $resultSpan.html('<span style="color: orange;">Schema has errors</span>');
                if (!$('.schema-error-message').length) {
                    $('.schema-indicator').after('<div class="schema-error-message">schema appears to have errors</div>');
                }
                $('.schema-indicator').css('background-color', 'orange');
            } else {
                $resultSpan.html('<span style="color: red;">No valid schema detected</span>');
                $('.schema-error-message').remove();
                $('.schema-indicator').css('background-color', 'red');
            }
        },
        error: function() {
            $resultSpan.text('Error occurred while rescanning');
        },
        complete: function() {
                        $button.prop('disabled', false);
                    }
                });
            });
            });
        })(jQuery);
        </script>

        
        <?php
    }

    // Save the meta box data
    add_action('save_post', 'custom_checklist_save_meta_box_data');
    add_action('save_page', 'custom_checklist_save_meta_box_data');

    function custom_checklist_save_meta_box_data($post_id) {
        // Check if nonce is set
        if (!isset($_POST['custom_checklist_nonce'])) {
            return;
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['custom_checklist_nonce'], 'custom_checklist_nonce_action')) {
            return;
        }

        // Check autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check permissions for both posts and pages
        if (!current_user_can('edit_post', $post_id) && !current_user_can('edit_page', $post_id)) {
            return;
        }

        // Save or delete the checklist data
        if (isset($_POST['custom_checklist'])) {
            update_post_meta($post_id, '_custom_checklist', $_POST['custom_checklist']);
        } else {
            delete_post_meta($post_id, '_custom_checklist');
        }

        // Save or delete the notes data
        if (isset($_POST['custom_notes'])) {
            update_post_meta($post_id, '_custom_notes', $_POST['custom_notes']);
        } else {
            delete_post_meta($post_id, '_custom_notes');
        }
    }

    // Add custom column to post list
function slmm_add_schema_column($columns) {
    $columns['schema'] = 'Schema';
    return $columns;
}

// Populate custom column
function slmm_populate_schema_column($column_name, $post_id) {
    if ($column_name !== 'schema') {
        return;
    }

    $schema = get_post_meta($post_id, '_slmm_insert_schema', true);
    $schema_status = slmm_get_schema_status($schema);

    $color = $schema_status['color'];
    $display_text = $schema_status['text'];
    $line_count = $schema_status['lines'];

    echo "<span class='schema-indicator' style='display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: {$color}; margin-right: 5px;'></span>";
    echo "<span class='schema-status'>{$display_text}</span><br>";
    if ($line_count > 0) {
        echo "<span class='schema-lines' style='font-size: 0.8em; color: #666;'>{$line_count} lines of schema detected</span><br>";
    }
    echo "<button class='rescan-schema button button-small' data-post-id='{$post_id}'>Rescan Schema</button>";
    echo "<button class='validate-schema button button-small' data-post-id='{$post_id}'>Validate Schema</button>";

    echo "<span style='display:none;'>{$display_text}</span>"; // Hidden span for sorting
}

// Function to determine schema status and count lines
function slmm_get_schema_status($schema) {
    if (empty($schema)) {
        return array('color' => 'red', 'text' => 'No Schema Detected', 'lines' => 0);
    }

    // Count lines of schema
    $line_count = substr_count($schema, "\n") + 1;

    // Basic check for schema structure
    if (strpos($schema, '<script type="application/ld+json">') === false || strpos($schema, '</script>') === false) {
        return array('color' => 'orange', 'text' => 'Schema with Errors', 'lines' => $line_count);
    }

    // Try to parse JSON
    $json_schema = preg_replace('/^<script type="application\/ld\+json">|<\/script>$/i', '', trim($schema));
    $decoded = json_decode($json_schema);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return array('color' => 'orange', 'text' => 'Schema with Errors', 'lines' => $line_count);
    }

    return array('color' => 'green', 'text' => 'Schema Present', 'lines' => $line_count);
}

// Make column sortable
function slmm_schema_column_sortable($columns) {
    $columns['schema'] = 'schema';
    return $columns;
}

// Handle sorting
function slmm_schema_column_orderby($query) {
    if (!is_admin()) {
        return;
    }

    $orderby = $query->get('orderby');

    if ('schema' == $orderby) {
        $query->set('meta_key', '_slmm_insert_schema');
        $query->set('orderby', 'meta_value');
    }
}

// Register actions and filters for all post types
function slmm_register_schema_column_for_post_types() {
    $post_types = get_post_types(['public' => true], 'names');

    foreach ($post_types as $post_type) {
        add_filter("manage_{$post_type}_posts_columns", 'slmm_add_schema_column');
        add_action("manage_{$post_type}_posts_custom_column", 'slmm_populate_schema_column', 10, 2);
        add_filter("manage_edit-{$post_type}_sortable_columns", 'slmm_schema_column_sortable');
    }

    add_action('pre_get_posts', 'slmm_schema_column_orderby');
}
add_action('admin_init', 'slmm_register_schema_column_for_post_types');

// Add custom CSS for the schema column
function slmm_add_schema_column_css() {
    $screen = get_current_screen();
    if ($screen && in_array($screen->base, ['edit', 'edit-tags'])) {
        echo '<style>
            .column-schema { width: 150px; }
            .column-schema .button-small { margin-top: 5px; }
        </style>';
    }
}
add_action('admin_head', 'slmm_add_schema_column_css');

// AJAX handler for rescanning schema
function slmm_rescan_schema() {
    check_ajax_referer('slmm_rescan_schema_nonce', 'nonce');
    
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized');
    }
    
    $post_id = intval($_POST['post_id']);
    $schema = get_post_meta($post_id, '_slmm_insert_schema', true);
    
    $schema_status = slmm_get_schema_status($schema);
    wp_send_json_success($schema_status);
}
add_action('wp_ajax_slmm_rescan_schema', 'slmm_rescan_schema');

// Add JavaScript for handling schema rescan on the All Pages view
function slmm_add_schema_rescan_script() {
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        $('.rescan-schema').click(function(e) {
            e.preventDefault();
            var $button = $(this);
            var postId = $button.data('post-id');
            var $row = $button.closest('tr');
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'slmm_rescan_schema',
                    post_id: postId,
                    nonce: '<?php echo wp_create_nonce("slmm_rescan_schema_nonce"); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        $row.find('.schema-indicator').css('background-color', response.data.color);
                        $row.find('.schema-status').text(response.data.text);
                        if (response.data.lines > 0) {
                            var linesText = response.data.lines + ' lines of schema detected';
                            if ($row.find('.schema-lines').length) {
                                $row.find('.schema-lines').text(linesText);
                            } else {
                                $('<span class="schema-lines" style="font-size: 0.8em; color: #666;"></span>')
                                    .text(linesText)
                                    .insertAfter($row.find('.schema-status'));
                            }
                        } else {
                            $row.find('.schema-lines').remove();
                        }
                    } else {
                        alert('Error occurred while rescanning');
                    }
                },
                error: function() {
                    alert('Error occurred while rescanning');
                },
                complete: function() {
                    $button.prop('disabled', false);
                }
            });
        });
        $('.validate-schema').click(function(e) {
            e.preventDefault();
            var $button = $(this);
            var $row = $button.closest('tr');
            var $viewLink = $row.find('.view a');
            
            if ($viewLink.length) {
                var viewUrl = $viewLink.attr('href');
                var validatorUrl = 'https://validator.schema.org/#url=' + encodeURIComponent(viewUrl);
                window.open(validatorUrl, '_blank');
            } else {
                alert('Unable to find the view link. Please check if the post is published.');
            }
        });
    });
    </script>
    <?php
}
add_action('admin_footer', 'slmm_add_schema_rescan_script');

// Add the Insert Schema meta box
function slmm_add_insert_schema_meta_box() {
    add_meta_box(
        'insert_schema_meta_box',
        'Insert Schema',
        'slmm_render_insert_schema_meta_box',
        null, // This will add the meta box to all post types
        'side',
        'default'
    );
}
add_action('add_meta_boxes', 'slmm_add_insert_schema_meta_box');

function slmm_render_insert_schema_meta_box($post) {
    wp_nonce_field('slmm_insert_schema_nonce', 'slmm_insert_schema_nonce');
    $schema = get_post_meta($post->ID, '_slmm_insert_schema', true);
    ?>
    <textarea id="slmm_insert_schema" name="slmm_insert_schema" style="width: 100%; min-height: 200px;" placeholder="Insert fully validated schema here including <script> tags"><?php echo esc_textarea($schema); ?></textarea>
    <?php
}

// Save the Insert Schema data
function slmm_save_insert_schema_meta_box_data($post_id) {
    if (!isset($_POST['slmm_insert_schema_nonce']) || !wp_verify_nonce($_POST['slmm_insert_schema_nonce'], 'slmm_insert_schema_nonce')) {
        return;
    }
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    if (isset($_POST['slmm_insert_schema'])) {
        $schema = wp_unslash($_POST['slmm_insert_schema']);
        update_post_meta($post_id, '_slmm_insert_schema', $schema);
    }
}
add_action('save_post', 'slmm_save_insert_schema_meta_box_data');

// Add support for WP Sheet Editor
function slmm_add_insert_schema_to_wp_sheet_editor($columns) {
    $columns['_slmm_insert_schema'] = array(
        'key' => '_slmm_insert_schema',
        'name' => 'Insert Schema',
        'data_type' => 'meta_data',
        'column_width' => 300,
        'supports_formulas' => true,
        'formatted' => array(
            'editor' => 'textarea',
        ),
    );
    return $columns;
}
add_filter('vg_sheet_editor/columns', 'slmm_add_insert_schema_to_wp_sheet_editor');

// Output the schema in the front-end
function slmm_output_insert_schema() {
    if (is_singular()) {
        $schema = get_post_meta(get_the_ID(), '_slmm_insert_schema', true);
        if (!empty($schema)) {
            echo wp_kses($schema, array(
                'script' => array(
                    'type' => array(),
                )
            )) . "\n";
        }
    }
}

// Allow script tags in schema data
function slmm_allow_script_tags($allowed_tags) {
    $allowed_tags['script'] = array(
        'type' => true
    );
    return $allowed_tags;
}
add_filter('wp_kses_allowed_html', 'slmm_allow_script_tags', 10, 2);

// Handle saving schema data
function slmm_save_schema_data($post_id, $value) {
    // Remove any potentially malicious scripts while keeping legitimate schema script tags
    $clean_value = wp_kses($value, array(
        'script' => array(
            'type' => array()
        )
    ));
    update_post_meta($post_id, '_slmm_insert_schema', $clean_value);
}

// Filter for WP Sheet Editor
add_filter('vg_sheet_editor/save_rows/before_saving_cell', function($value, $post_id, $column_key) {
    if ($column_key === '_slmm_insert_schema') {
        slmm_save_schema_data($post_id, $value);
        return $value;
    }
    return $value;
}, 10, 3);
add_action('wp_head', 'slmm_output_insert_schema', 0);

}
?>