<?php
require_once dirname(__DIR__) . '/plugin.php';

/*
Plugin Name: WordPress Structure Analyzer
Description: Analyzes and displays the website structure in a grid layout.
Author: Your Name
*/

// Add menu item to the admin menu
add_action('admin_menu', 'wsa_add_admin_menu');


// Add AJAX handlers
add_action('wp_ajax_wsa_get_children_and_links', 'wsa_ajax_get_children_and_links');
add_action('wp_ajax_wsa_get_all_incoming_links', 'wsa_ajax_get_all_incoming_links');

// Enqueue inline styles and scripts
add_action('admin_head', 'wsa_add_inline_styles');
add_action('admin_footer', 'wsa_add_inline_scripts');
function get_status_info($post) {
    $status = $post->post_status;
    $status_class = '';
    $status_label = '';
    $full_status = '';

    switch ($status) {
        case 'draft':
            $status_class = 'wsa-draft';
            $status_label = 'D';
            $full_status = 'Draft';
            break;
        case 'private':
            $status_class = 'wsa-private';
            $status_label = 'P';
            $full_status = 'Private';
            break;
        case 'pending':
            $status_class = 'wsa-unpublished';
            $status_label = 'U';
            $full_status = 'Unpublished';
            break;
        case 'publish':
            if (!empty($post->post_password)) {
                $status_class = 'wsa-password-protected';
                $status_label = 'S';
                $full_status = 'Password Protected';
            } else {
                $status_class = 'wsa-published';
                $full_status = 'Published';
            }
            break;
        default:
            $status_class = 'wsa-published';
            $full_status = 'Published';
    }

    return array($status_class, $status_label, $full_status);
}

function wsa_add_admin_menu() {
    global $menu, $submenu;
    
    // Add the page without adding a menu item
    $hookname = add_submenu_page(
        null,                   // Don't add to any menu
        'Website Structure',    // Page title
        'Website Structure',    // Menu title (unused)
        'manage_options',       // Capability
        'wp-structure-analyzer', // Menu slug
        'wsa_display_page'      // Callback function
    );

    // Remove the menu item if it was somehow added
    foreach ($menu as $key => $item) {
        if ($item[2] === 'wp-structure-analyzer') {
            unset($menu[$key]);
            break;
        }
    }

    // Remove from submenus if it was added there
    if (isset($submenu['wp-structure-analyzer'])) {
        unset($submenu['wp-structure-analyzer']);
    }
}


function wsa_add_inline_styles() {
    echo '<style>
    /* WordPress Structure Analyzer Dark Theme - Scoped Variables */
    .wsa-dark-theme {
        --slmm-primary: #7C3AED;
        --slmm-primary-hover: #8B5CF6;
        --slmm-primary-light: rgba(124, 58, 237, 0.08);
        --slmm-primary-subtle: rgba(124, 58, 237, 0.04);
        --slmm-dark-bg: #0A0A0F;
        --slmm-dark-surface: #141419;
        --slmm-dark-surface-hover: #1A1A1F;
        --slmm-dark-border: #1F1F24;
        --slmm-dark-border-subtle: #16161B;
        --slmm-text-primary: #F1F5F9;
        --slmm-text-secondary: #94A3B8;
        --slmm-text-muted: #64748B;
        --slmm-text-dim: #475569;
        --slmm-success: #10B981;
        --slmm-warning: #F59E0B;
        --slmm-error: #EF4444;
        --slmm-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        --slmm-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.4);
    }

    /* Main container styling - ONLY for SLMM Structure Analyzer */
    .wsa-dark-theme .wrap {
        background: var(--slmm-dark-bg) !important;
        color: var(--slmm-text-primary) !important;
        min-height: 100vh;
        margin: 0 -20px -10px;
        padding: 20px;
    }

    .wsa-dark-theme .wrap h1 {
        color: var(--slmm-text-primary) !important;
        font-size: 1.75rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
    }

    .wsa-dark-theme .wsa-container, 
    .wsa-dark-theme .wpbody-content {
        display: flex;
        flex-direction: column;
        height: 100%;
        background: var(--slmm-dark-bg);
        color: var(--slmm-text-primary);
    }
    
    .wsa-dark-theme .wsa-grid-container {
        min-height: 2vh;
        max-height: 80vh;
        overflow-y: auto;
        resize: vertical;
        border-bottom: 5px solid var(--slmm-dark-border);
        position: relative;
        flex: 0 0 auto;
        background: var(--slmm-dark-surface);
        border-radius: 12px;
        border: 1px solid var(--slmm-dark-border-subtle);
        box-shadow: var(--slmm-shadow);
    }
    
    .wsa-dark-theme .wsa-grid {
        display: flex;
        overflow-x: auto;
        padding: 20px;
        background: var(--slmm-dark-surface);
    }
    
    .wsa-dark-theme .wsa-column {
        flex: 0 0 250px;
        padding: 10px;
        border-right: 1px solid var(--slmm-dark-border);
    }
    
    .wsa-dark-theme .wsa-page {
        margin-bottom: 10px;
        padding: 8px;
        border: 1px solid var(--slmm-dark-border);
        border-radius: 8px;
        min-width: 400px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
        background: var(--slmm-dark-bg);
        color: var(--slmm-text-primary);
    }
    
    .wsa-page:hover {
        background: var(--slmm-dark-surface-hover);
        border-color: var(--slmm-dark-border);
        transform: translateY(-1px);
        box-shadow: var(--slmm-shadow);
    }
    
    .wsa-page-info {
        display: flex;
        align-items: center;
        width: 100%;
    }
    
    .wsa-page-title {
        flex-grow: 1;
        font-size: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 5px;
        color: var(--slmm-text-primary);
        font-weight: 500;
    }
    
    .wsa-page-slug {
        font-size: 12px;
        color: var(--slmm-primary);
        margin-top: 2px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .wsa-edit-link, .wsa-edit-link-no-children, .wsa-edit-link-has-children {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        text-decoration: none;
        font-size: 14px;
        margin-left: 5px;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .wsa-edit-link::after {
        content: "›";
        position: absolute;
        top: 43%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: var(--wsa-edit-link-font-size, 17px);
        line-height: 1;
        transition: all 0.3s ease;
    }
    
    .wsa-edit-link-has-children, .wsa-has-children .wsa-edit-link {
        background-color: var(--slmm-primary);
    }
    
    .wsa-edit-link-no-children {
        background-color: var(--slmm-text-muted);
    }
    
    .wsa-has-children {
        border: 2px solid var(--slmm-primary);
    }
    
    .wsa-active-page {
        border: 2px solid var(--slmm-primary-hover) !important;
        background: var(--slmm-primary-light) !important;
    }
    
    .wsa-active-parent {
        border: 2px solid var(--slmm-primary) !important;
    }
    
    .wsa-draft, .wsa-private, .wsa-unpublished {
        background: var(--slmm-dark-border-subtle);
        border-color: var(--slmm-warning);
    }
    
    .wsa-status, .wsa-child-count {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 50%;
        margin-right: 5px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .wsa-status {
        background-color: var(--slmm-warning);
        color: #1a1a1a;
    }
    
    .wsa-child-count {
        background: var(--slmm-dark-border);
        color: var(--slmm-text-primary);
        min-width: 20px;
        border: 1px solid var(--slmm-dark-border);
    }
    
    .wsa-links {
        font-size: 12px;
        margin-top: 5px;
        transition: all 0.3s ease;
    }
    
    .wsa-internal-link {
        color: var(--slmm-primary);
        font-weight: 600;
    }
    
    .wsa-external-link {
        color: var(--slmm-warning);
        font-weight: 500;
    }
    
    #wsa-incoming-links {
        flex: 1 1 auto;
        overflow-y: auto;
        padding: 20px;
        border-top: 1px solid var(--slmm-dark-border);
        background: var(--slmm-dark-surface);
        border-radius: 12px;
        margin-top: 1rem;
        border: 1px solid var(--slmm-dark-border-subtle);
        box-shadow: var(--slmm-shadow);
    }

    #wsa-incoming-links h3 {
        color: var(--slmm-text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    #wsa-incoming-links input[type="text"] {
        background: var(--slmm-dark-bg) !important;
        border: 2px solid var(--slmm-dark-border) !important;
        border-radius: 8px !important;
        color: var(--slmm-text-primary) !important;
        padding: 0.5rem !important;
        font-size: 0.875rem;
    }

    #wsa-incoming-links input[type="text"]:focus {
        outline: none !important;
        border-color: var(--slmm-primary) !important;
        box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
    }

    #wsa-incoming-links .button {
        background: var(--slmm-primary) !important;
        border: none !important;
        color: white !important;
        padding: 0.5rem 1rem !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }

    #wsa-incoming-links .button:hover {
        background: var(--slmm-primary-hover) !important;
    }
    
    .wsa-incoming-links-table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
        background: var(--slmm-dark-bg);
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid var(--slmm-dark-border);
    }
    
    .wsa-incoming-links-table th, .wsa-incoming-links-table td {
        border: 1px solid var(--slmm-dark-border);
        padding: 8px;
        text-align: left;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: var(--slmm-text-primary);
    }
    
    .wsa-incoming-links-table th {
        background: var(--slmm-dark-surface);
        cursor: col-resize;
        font-weight: 600;
        color: var(--slmm-text-primary);
    }
    
    .wsa-incoming-links-table td:first-child {
        white-space: normal;
        word-wrap: break-word;
        max-width: 250px;
    }

    .wsa-incoming-links-table a {
        color: var(--slmm-primary);
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .wsa-incoming-links-table a:hover {
        color: var(--slmm-primary-hover);
        text-decoration: underline;
    }
    
    .wsa-incoming-links-table th:nth-child(1),
    .wsa-incoming-links-table td:nth-child(1),
    .wsa-incoming-links-table th:nth-child(3),
    .wsa-incoming-links-table td:nth-child(3) {
        width: 45%;
    }
    
    .wsa-incoming-links-table th:nth-child(2),
    .wsa-incoming-links-table td:nth-child(2) {
        width: 10%;
    }
    
    .wsa-resizer {
        position: absolute;
        top: 0;
        right: -2px;
        width: 5px;
        height: 100%;
        cursor: col-resize;
        user-select: none;
        background-color: transparent;
    }
    
    .wsa-incoming-links-table th:last-child .wsa-resizer {
        display: none;
    }
    
    .wsa-no-links .wsa-chevron {
        border: 1px solid var(--slmm-error);
        border-radius: 50%;
    }
    
    .wsa-controls {
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-wrap: wrap;
        background: var(--slmm-dark-surface);
        padding: 1rem;
        border-radius: 12px;
        border: 1px solid var(--slmm-dark-border-subtle);
        box-shadow: var(--slmm-shadow);
    }
    
    .wsa-controls button {
        background: var(--slmm-dark-surface) !important;
        border: none !important;
        color: white !important;
        padding: 0.5rem 1rem !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        margin-right: 0 !important;
        border: 1px solid var(--slmm-primary) !important;
    }

    .wsa-controls button:hover {
        background: var(--slmm-primary-hover) !important;
        transform: translateY(-1px);
    }

    #wsa-current-slug {
        background: var(--slmm-dark-bg);
        color: var(--slmm-text-primary);
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        border: 1px solid var(--slmm-dark-border);
        font-family: monospace;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .wsa-chevron {
        margin-left: 5px;
        font-size: 18px;
        font-weight: bold;
    }
    
    .wsa-blue-chevron {
        color: var(--slmm-primary);
    }
    
    .wsa-grey-chevron {
        color: var(--slmm-text-muted);
    }
    
    .wsa-slug-parent {
        color: var(--slmm-text-secondary);
    }
    
    .wsa-slug-last-child {
        color: var(--slmm-primary);
    }
    
    .wsa-outgoing-links {
        margin-top: 20px;
        padding: 15px;
        border-top: 1px solid var(--slmm-dark-border);
        background: var(--slmm-dark-bg);
        border-radius: 8px;
    }
    
    .wsa-outgoing-links ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .wsa-outgoing-links ul li {
        margin-bottom: 0.5rem;
    }

    .wsa-outgoing-links ul li a {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--slmm-primary);
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .wsa-outgoing-links ul li a:hover {
        color: var(--slmm-primary-hover);
        text-decoration: underline;
    }
    
    .wsa-arrow-column {
        width: 50px;
        text-align: center;
        font-weight: bold;
        color: var(--slmm-text-primary);
    }
    
    .wsa-no-incoming-links .wsa-page-title {
        color: var(--slmm-error);
    }
    
    .wsa-resize-handle {
        position: absolute;
        right: 0;
        top: 0;
        width: 5px;
        height: 100%;
        cursor: col-resize;
    }
    
    .wsa-th-content {
        position: relative;
        padding-right: 5px;
    }
    
    .wsa-no-outgoing-links .wsa-edit-link::after {
        color: var(--slmm-warning);
        font-weight: 900;
        transform: scale3d(3, -3.3, 2);
        transform-origin: right;
        translate: 2px -1.5px;
    }
    
    .wsa-tree-item {
        cursor: pointer;
        line-height: 1.5;
        color: var(--slmm-text-primary);
        padding: 0.25rem 0;
    }
    
    .wsa-toggle, .wsa-toggle-placeholder {
        display: inline-block;
        width: 20px;
        text-align: center;
        cursor: pointer;
        color: var(--slmm-primary);
    }
    
    .wsa-toggle {
        font-weight: bold;
    }
    
    .wsa-slug {
        font-size: 0.8em;
        color: var(--slmm-text-muted);
        display: block;
        margin-top: 2px;
    }
    
    .wsa-tree-item > .wsa-slug {
        margin-left: 30px;
    }

    .wsa-folder {
        font-weight: bold;
        color: var(--slmm-text-primary);
    }

    .wsa-status {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        border-radius: 50%;
        margin-left: 5px;
        font-size: 12px;
        font-weight: bold;
        color: white;
    }
    
    .wsa-draft .wsa-status {
        background-color: var(--slmm-error);
    }
    
    .wsa-private .wsa-status {
        background-color: var(--slmm-primary);
    }
    
    .wsa-unpublished .wsa-status {
        background-color: var(--slmm-warning);
        color: #1a1a1a;
    }

    .wsa-broken-link-indicator {
        width: 8px;
        height: 8px;
        background-color: var(--slmm-error);
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
        vertical-align: middle;
    }

    .wsa-links li {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }

    .wsa-links .wsa-broken-link::before {
        content: "";
        width: 8px;
        height: 8px;
        background-color: var(--slmm-error);
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
        flex-shrink: 0;
    }

    .wsa-links .wsa-broken-link-indicator {
        flex-shrink: 0;
    }
    
    .wsa-broken-link a {
        text-decoration: underline wavy var(--slmm-error);
        color: var(--slmm-error) !important;
    }
    
    .wsa-page a {
        text-decoration: none;
    }

    /* Modal/Popup styling for structure view */
    #wsa-structure-popup {
        background: var(--slmm-dark-surface) !important;
        border: 1px solid var(--slmm-dark-border) !important;
        border-radius: 12px !important;
        box-shadow: var(--slmm-shadow-lg) !important;
        color: var(--slmm-text-primary) !important;
    }

    #wsa-structure-popup h2 {
        color: var(--slmm-text-primary) !important;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    #wsa-structure-popup button {
        background: var(--slmm-primary) !important;
        border: none !important;
        color: white !important;
        padding: 0.5rem 1rem !important;
        border-radius: 6px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }

    #wsa-structure-popup button:hover {
        background: var(--slmm-primary-hover) !important;
    }

    #wsa-close-popup {
        background: var(--slmm-error) !important;
        color: white !important;
        border: none !important;
        border-radius: 50% !important;
        width: 30px !important;
        height: 30px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        font-weight: bold !important;
        cursor: pointer !important;
        padding: 0 !important;
    }

    #wsa-close-popup:hover {
        background: #dc2626 !important;
    }

    /* Scrollbar styling */
    .wsa-grid-container::-webkit-scrollbar,
    #wsa-incoming-links::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .wsa-grid-container::-webkit-scrollbar-track,
    #wsa-incoming-links::-webkit-scrollbar-track {
        background: var(--slmm-dark-bg);
        border-radius: 4px;
    }

    .wsa-grid-container::-webkit-scrollbar-thumb,
    #wsa-incoming-links::-webkit-scrollbar-thumb {
        background: var(--slmm-dark-border);
        border-radius: 4px;
    }

    .wsa-grid-container::-webkit-scrollbar-thumb:hover,
    #wsa-incoming-links::-webkit-scrollbar-thumb:hover {
        background: var(--slmm-primary);
    }

    /* Link count styling */
    .wsa-link-count {
        color: var(--slmm-text-secondary);
        font-weight: 600;
    }

    /* Search result highlighting */
    .wsa-incoming-links-table tr:hover {
        background: var(--slmm-dark-surface-hover);
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .wsa-controls {
            flex-direction: column;
            align-items: stretch;
        }

        .wsa-controls button {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .wsa-page {
            min-width: 300px;
        }

        .wsa-column {
            flex: 0 0 200px;
        }
    }
    </style>';
}

// Find this line:
function wsa_add_inline_scripts() {
    ?>
    <script>
    // Replace everything after this opening script tag with the following code:
    jQuery(document).ready(function($) {
        function checkBrokenLinks() {
            $('.wsa-page').each(function() {
                var $page = $(this);
                var pageId = $page.data('page-id');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wsa_check_broken_links',
                        page_id: pageId
                    },
                    success: function(response) {
                        if (response.success && response.data.has_broken_links) {
                            $page.addClass('wsa-has-broken-links');
                            if (!$page.find('.wsa-broken-link-indicator').length) {
                                $page.find('.wsa-page-info').prepend('<span class="wsa-broken-link-indicator"></span>');
                            }
                        }
                    }
                });
            });
        }

        // Call this function on page load
        checkBrokenLinks();

        function loadChildren(pageId, $column) {
            console.log("Loading children for page ID:", pageId);
            $.ajax({
                url: ajaxurl,
                type: "POST",
                data: {
                    action: "wsa_get_children_and_links",
                    page_id: pageId
                },
                success: function(response) {
                    if (response.success) {
                        var $newColumn = $("<div class='wsa-column'></div>").append(response.data.children);
                        $column.after($newColumn);
                        
                        var $lastColumn = $(".wsa-column").last();
                        $lastColumn.append("<div class='wsa-outgoing-links'>" + response.data.links + "</div>");
                        
                        updateEditLinkStatus();
                        updateChevronVisibility();
                        applyStyles();
                    } else {
                        console.error("AJAX request was not successful");
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error("AJAX error:", textStatus, errorThrown);
                }
            });
        }

        $(document).on("click", ".wsa-page", function(e) {
            e.stopPropagation();
            var $this = $(this);
            var pageId = $this.data("page-id");
            var $column = $this.closest(".wsa-column");
            var level = $column.index() + 1;

            console.log("Clicked page:", pageId);

            $(".wsa-column").slice(level).remove();
            $(".wsa-page").removeClass("wsa-active-page wsa-active-parent");
            $this.addClass("wsa-active-page");
            $this.prependTo($column);

            var $currentColumn = $column;
            while ($currentColumn.length) {
                var $activeParentPage = $currentColumn.find(".wsa-page.wsa-active-page, .wsa-page.wsa-has-children");
                $activeParentPage.addClass("wsa-active-parent");
                $currentColumn = $currentColumn.prev(".wsa-column");
            }

            loadChildren(pageId, $column);
        });

        $(document).on("click", ".wsa-page", function() {
            updateCurrentSlug();
        });

        $("#wsa-collapse-all").on("click", function() {
            $(".wsa-column:not(:first-child)").remove();
            $(".wsa-page").removeClass("wsa-active-page wsa-active-parent");
            updateEditLinkStatus();
            updateChevronVisibility();
        });

        $.ajax({
            url: ajaxurl,
            type: "POST",
            data: {
                action: "wsa_get_all_incoming_links"
            },
            success: function(response) {
                if (response.success) {
                    displayIncomingLinks(response.data);
                } else {
                    console.error("Failed to get incoming links");
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.error("AJAX error when getting incoming links:", textStatus, errorThrown);
            }
        });

        function displayIncomingLinks(links) {
            var html = "<h3>Incoming Links</h3>";
            html += '<input type="text" id="wsa-target-page-search" placeholder="Search Target Page" style="margin-bottom: 10px;">';
            html += '<button id="wsa-sort-most-links" class="button" style="margin-left: 10px;">Sort by Most Links</button>';
            html += '<button id="wsa-sort-least-links" class="button" style="margin-left: 10px;">Sort by Least Links</button>';
            
            if (Object.keys(links).length > 0) {
                html += "<table class='wsa-incoming-links-table'><tr>" +
                    "<th>Target Page<div class='wsa-resizer'></div></th>" +
                    "<th class='wsa-arrow-column'><==<div class='wsa-resizer'></div></th>" +
                    "<th>Incoming Link Anchor / Originating Page</th>" +
                    "</tr>";
                
                var sortedLinks = Object.entries(links).sort((a, b) => b[1].links.length - a[1].links.length);
                
                sortedLinks.forEach(function([targetPage, pageData]) {
                    html += "<tr><td><a href='" + ajaxurl.replace('admin-ajax.php', '') + "post.php?post=" + pageData.page_id + "&action=edit' target='_blank'>" + pageData.title + "</a></td>" +
                        "<td class='wsa-arrow-column'><== <span class='wsa-link-count'>(" + pageData.links.length + ")</span></td>" +
                        "<td>";
                    pageData.links.forEach(function(link) {
                        html += link.anchor + " : <a href='#' class='wsa-originating-page' data-page-id='" + link.page_id + "'>" + link.url + "</a><br>";
                    });
                    html += "</td></tr>";
                });
                html += "</table>";
            } else {
                html += "<p>No incoming links found.</p>";
            }
            $("#wsa-incoming-links").html(html);

            $(".wsa-originating-page").on("click", function(e) {
                e.preventDefault();
                var pageId = $(this).data("page-id");
                $(".wsa-page[data-page-id='" + pageId + "']").click();
            });

            $("#wsa-target-page-search").on("input", function() {
                var searchText = $(this).val().toLowerCase();
                $(".wsa-incoming-links-table tr").each(function(index) {
                    if (index === 0) return;
                    var targetPage = $(this).find("td:first").text().toLowerCase();
                    $(this).toggle(targetPage.includes(searchText));
                });
            });

            function sortTable(ascending) {
                var rows = $(".wsa-incoming-links-table tr").toArray().slice(1);
                rows.sort(function(a, b) {
                    var aLinks = $(a).find("td:last br").length + 1;
                    var bLinks = $(b).find("td:last br").length + 1;
                    return ascending ? aLinks - bLinks : bLinks - aLinks;
                });
                $(".wsa-incoming-links-table").append(rows);
            }

            $("#wsa-sort-most-links").on("click", function() {
                sortTable(false);
            });

            $("#wsa-sort-least-links").on("click", function() {
                sortTable(true);
            });
        }

        var zoomLevel = 100;
        var zoomStep = 10;
        var urlsVisible = true;

        $("#wsa-toggle-urls").on("click", function() {
            urlsVisible = !urlsVisible;
            $(this).text(urlsVisible ? "Hide URLs" : "Show URLs");
            applyStyles();
        });
        var titlesVisible = true;

        $("#wsa-toggle-titles").on("click", function() {
            titlesVisible = !titlesVisible;
            $(this).text(titlesVisible ? "Hide Titles" : "Show Titles");
            applyStyles();
        });

        function updateZoom() {
            applyStyles();
        }

        function applyStyles() {
            var scaleFactor = zoomLevel / 100;
            $(".wsa-page-title").css({
                "font-size": (16 * scaleFactor) + "px",
                "display": "-webkit-box",
                "-webkit-line-clamp": "2",
                "-webkit-box-orient": "vertical",
                "max-width": (120 * scaleFactor) + "ch",
                "overflow": "hidden",
                "text-overflow": "ellipsis",
                "margin-top": (2 * scaleFactor) + "px"
            });
            $(".wsa-page-slug").css("font-size", (10 * scaleFactor) + "px");
            $(".wsa-internal-link").css("font-size", (12 * scaleFactor) + "px");
            $(".wsa-links").css("font-size", (12 * scaleFactor) + "px");
            $(".wsa-child-count, .wsa-status").css({
                "font-size": (12 * scaleFactor) + "px",
                "width": (20 * scaleFactor) + "px",
                "height": (20 * scaleFactor) + "px",
                "line-height": (20 * scaleFactor) + "px"
            });
            $(".wsa-edit-link, .wsa-edit-link-no-children, .wsa-edit-link-has-children").css({
                "font-size": zoomLevel + "%",
                "width": (20 * scaleFactor) + "px",
                "height": (20 * scaleFactor) + "px"
            });
            $(".wsa-edit-link::after").css({
                "font-size": (17 * scaleFactor) + "px",
                "line-height": (20 * scaleFactor) + "px"
            });
            $(".wsa-page").css({
                "margin-bottom": (10 * scaleFactor) + "px",
                "padding": (5 * scaleFactor) + "px"
            });
            $(".wsa-links").css({
                "font-size": (12 * scaleFactor) + "px",
                "margin-top": (5 * scaleFactor) + "px"
            });
            if (!urlsVisible) {
                $(".wsa-page-slug, .wsa-slug-last-child, .wsa-internal-link").css("display", "none");
            } else {
                $(".wsa-page-slug, .wsa-slug-last-child, .wsa-internal-link").css("display", "");
            }
            if (!titlesVisible) {
                $(".wsa-page-title").css("display", "none");
            } else {
                $(".wsa-page-title").css("display", "");
            }
        }

        $("#wsa-zoom-in").on("click", function() {
            zoomLevel += zoomStep;
            applyStyles();
        });

        $("#wsa-zoom-out").on("click", function() {
            zoomLevel -= zoomStep;
            if (zoomLevel < 10) zoomLevel = 10;
            applyStyles();
        });
        function updateEditLinkStatus() {
            $(".wsa-edit-link").each(function() {
                var $page = $(this).closest(".wsa-page");
                var pageId = $page.data("page-id");
                var hasChildren = $page.hasClass("wsa-has-children") || $page.next(".wsa-column").length > 0;
                $(this).toggleClass("wsa-edit-link-has-children", hasChildren);
                $(this).toggleClass("wsa-edit-link-no-children", !hasChildren);
            });
        }
        function updateChevronVisibility() {
            $('.wsa-chevron').each(function() {
                var $chevron = $(this);
                var $page = $chevron.closest('.wsa-page');
                var hasChildren = $page.find('.wsa-edit-link-has-children').length > 0;
                var hasLinks = $page.next('.wsa-column').find('.wsa-outgoing-links').length > 0;
                
                if (hasChildren) {
                    $chevron.removeClass('wsa-grey-chevron').addClass('wsa-blue-chevron').show();
                } else if (hasLinks) {
                    $chevron.removeClass('wsa-blue-chevron').addClass('wsa-grey-chevron').show();
                } else {
                    $chevron.hide();
                }
            });
        }

        updateEditLinkStatus();
        updateChevronVisibility();

        $(document).ajaxComplete(function(event, xhr, settings) {
            if (settings.url === ajaxurl && settings.data.indexOf('action=wsa_get_children_and_links') !== -1) {
                updateEditLinkStatus();
                updateChevronVisibility();
            }
        });

        function updateCurrentSlug() {
            var $activePage = $(".wsa-active-page");
            var slugPath = $activePage.data("slug-path") || "/";
            $("#wsa-current-slug").text(slugPath);
        }

        $(document).on("click", ".wsa-page", function() {
            updateCurrentSlug();
        });

        updateCurrentSlug();

        function makeTableColumnsResizable(tableSelector) {
            var pressed = false;
            var startX, startWidth;
            var $table = $(tableSelector);

            $table.find('.wsa-resizer').mousedown(function(e) {
                e.preventDefault();
                pressed = true;
                startX = e.pageX;
                var $th = $(this).closest('th');
                startWidth = $th.width();

                $(document).mousemove(function(e) {
                    if (pressed) {
                        var width = startWidth + (e.pageX - startX);
                        $th.css('width', width);
                        var index = $th.index() + 1;
                        $table.find('tr td:nth-child(' + index + ')').css('width', width);
                    }
                });

                $(document).mouseup(function() {
                    if (pressed) {
                        pressed = false;
                        $(document).off('mousemove');
                    }
                });
            });
        }

        makeTableColumnsResizable('.wsa-incoming-links-table');

        $("#wsa-print-structure").on("click", function() {
            $.ajax({
                url: ajaxurl,
                type: "POST",
                data: {
                    action: "wsa_get_site_structure"
                },
                success: function(response) {
                    if (response.success) {showStructurePopup(response.data);
                    } else {
                        console.error("Failed to get site structure");
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error("AJAX error when getting site structure:", textStatus, errorThrown);
                }
            });
        });

        function showStructurePopup(structure) {
            var popup = $('<div id="wsa-structure-popup"></div>').css({
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '80%',
                height: '80%',
                backgroundColor: 'white',
                border: '1px solid #ccc',
                padding: '20px',
                boxShadow: '0 0 10px rgba(0,0,0,0.5)',
                zIndex: 9999,
                display: 'flex',
                flexDirection: 'column'
            }).appendTo('body');

            $('<div></div>').css({
                display: 'flex',
                justifyContent: 'space-between',
                marginBottom: '10px'
            }).append(
                $('<h2>Site Structure</h2>'),
                $('<button id="wsa-close-popup">X</button>').css({
                    background: 'none',
                    border: 'none',
                    fontSize: '20px',
                    cursor: 'pointer'
                })
            ).appendTo(popup);

            var controlButtons = $('<div></div>').css({
                marginBottom: '10px'
            }).append(
                $('<button id="wsa-expand-all">Expand All</button>'),
                $('<button id="wsa-collapse-all" style="margin-left: 10px;">Collapse All</button>')
            ).appendTo(popup);

            var content = $('<div></div>').html(structure.html).css({
                flex: 1,
                overflow: 'auto',
                whiteSpace: 'pre-wrap',
                wordWrap: 'break-word',
                fontFamily: 'monospace'
            }).appendTo(popup);

            $('<div></div>').css({
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '10px'
            }).append(
                $('<button id="wsa-save-structure">Save</button>'),
                $('<button id="wsa-copy-structure">Copy</button>')
            ).appendTo(popup);

            function toggleChildren($item, show) {
                var $toggle = $item.find('> .wsa-toggle').first();
                var $children = $item.next('.wsa-children');
                
                if (show) {
                    $toggle.text('-');
                    $children.show();
                } else {
                    $toggle.text('+');
                    $children.hide();
                }
            }

            function expandAll() {
                content.find('.wsa-tree-item').each(function() {
                    var $item = $(this);
                    var $toggle = $item.find('> .wsa-toggle').first();
                    var $children = $item.next('.wsa-children');
                    
                    $toggle.text('-');
                    $children.show();
                });
            }

            function collapseAll() {
                content.find('.wsa-tree-item').each(function() {
                    var $item = $(this);
                    var $toggle = $item.find('> .wsa-toggle').first();
                    var $children = $item.next('.wsa-children');
                    
                    $toggle.text('+');
                    $children.hide();
                });
            }

            content.on('click', '.wsa-toggle', function(e) {
                e.stopPropagation();
                var $item = $(this).closest('.wsa-tree-item');
                toggleChildren($item, $(this).text() === '+');
            });

            $("#wsa-expand-all").on('click', expandAll);
            $("#wsa-collapse-all").on('click', collapseAll);

            $("#wsa-close-popup, #wsa-save-structure, #wsa-copy-structure").on("click", function() {
                if (this.id === "wsa-save-structure") {
                    var blob = new Blob([structure.csv], {type: "text/csv;charset=utf-8"});
                    saveAs(blob, "site-structure.csv");
                } else if (this.id === "wsa-copy-structure") {
                    var tempElement = content.clone();
                    tempElement.find('.wsa-toggle, .wsa-toggle-placeholder').remove();
                    var textToCopy = tempElement.text();
                    navigator.clipboard.writeText(textToCopy).then(function() {
                        alert("Structure copied to clipboard!");
                    }, function(err) {
                        console.error('Could not copy text: ', err);
                    });
                }
                popup.remove();
            });

            // Collapse all items by default
            collapseAll();

            // Ensure expand all and collapse all work even if nothing has been clicked
            popup.on('click', '#wsa-expand-all', expandAll);
            popup.on('click', '#wsa-collapse-all', collapseAll);
        }

        // Add the 404 scan functionality
        $("#wsa-scan-404s").on("click", function() {
    var $button = $(this);
    var originalText = $button.text();
    $button.text("Scanning...").prop("disabled", true);

    var batch = 0;

    function processBatch(batchNumber) {
        $.ajax({
            url: ajaxurl,
            type: "POST",
            data: {
                action: "wsa_scan_404s",
                batch: batchNumber
            },
            success: function(response) {
                if (response.success) {
                    if (!response.data.completed) {
                        // Update progress
                        var progress = response.data.progress;
                        $button.text("Scanning... (" + progress + "%)");

                        // Process next batch after a short delay
                        setTimeout(function() {
                            processBatch(response.data.next_batch);
                        }, 100); // 100 milliseconds delay
                    } else {
                        // Scan completed
                        $button.text(originalText).prop("disabled", false);
                        alert("Scan completed successfully");
                        location.reload();
                    }
                } else {
                    alert("Error during scan: " + response.data);
                    $button.text(originalText).prop("disabled", false);
                }
            },
            error: function() {
                alert("Error during scan");
                $button.text(originalText).prop("disabled", false);
            }
        });
    }

    // Start processing batches
            processBatch(batch);
        });

    });
    </script>
    <?php
}



function wsa_display_page() {
    ?>
    <div class="wsa-dark-theme">
    <div class="wrap wsa-container">
        <h1>Website Structure</h1>
        <div class="wsa-controls">
    <button id="wsa-collapse-all" class="button">Collapse All</button>
    <button id="wsa-zoom-in" class="button">+</button>
    <button id="wsa-zoom-out" class="button">-</button>
    <button id="wsa-toggle-urls" class="button">Hide URLs</button>
    <button id="wsa-toggle-titles" class="button">Hide Titles</button>
    <div id="wsa-current-slug" style="display: inline-block; margin-left: 10px; font-weight: bold;"></div>
    <button id="wsa-print-structure" class="button" style="float: right;">Print Structure</button>
    <button id="wsa-scan-404s" class="button" style="float: right; margin-right: 10px;">Scan 404s</button></div>
        <div class="wsa-grid-container">
            <div class="wsa-grid">
                <div class="wsa-column">
                    <?php echo wsa_generate_home_button(); ?>
                </div>
            </div>
        </div>
        <div id="wsa-incoming-links"></div>
    </div>
    </div>
    <?php
}

function wsa_generate_home_button() {
    $pages = get_pages(array(
        'sort_column' => 'menu_order,post_title',
        'post_status' => 'publish,draft,private,pending'
    ));
    $child_count = count($pages);

    $output = '<div class="wsa-page wsa-has-children" data-page-id="home">';
    $output .= '<div class="wsa-page-info">';
    $output .= '<span class="wsa-child-count">' . $child_count . '</span>';
    $output .= '<div class="wsa-page-content">';
    $output .= '<div class="wsa-page-title">Home Page</div>';
    $output .= '<div class="wsa-page-slug">/</div>';
    $output .= '</div>';
    $output .= '</div>';
    $output .= '<a href="' . admin_url('edit.php?post_type=page') . '" class="wsa-edit-link" target="_blank"></a>';
    $output .= '</div>';
    return $output;
}


function wsa_generate_page_html($parent_id) {
    $args = array(
        'post_type'      => 'page',
        'posts_per_page' => -1,
        'post_parent'    => $parent_id,
        'post_status'    => 'any',
        'order'          => 'ASC',
        'orderby'        => 'menu_order title',
    );

    $pages = get_posts($args);

    if (empty($pages)) {
        return '';
    }

    foreach ($pages as &$page) {
        $page->has_children = wsa_page_has_descendants($page->ID);
        $page->broken_links = wsa_check_broken_internal_links($page->ID);
    }
    unset($page);

    usort($pages, function($a, $b) {
        if ($a->has_children == $b->has_children) {
            return 0;
        }
        return $a->has_children ? -1 : 1;
    });

    $output = '';

    foreach ($pages as $page) {
        list($status_class, $status_label, $full_status) = get_status_info($page);

        $has_children = $page->has_children;
        $children_class = $has_children ? 'wsa-has-children' : '';
        $slug_path = get_page_uri($page->ID);

        $has_no_links = wsa_page_has_no_links($page->ID);
        $no_links_class = $has_no_links ? 'wsa-no-links' : '';
        $no_incoming_links_class = !wsa_page_has_incoming_links($page->ID) ? 'wsa-no-incoming-links' : '';
        $no_outgoing_links_class = wsa_page_has_no_outgoing_links($page->ID) ? 'wsa-no-outgoing-links' : '';

        $has_broken_links = !empty($page->broken_links);
        $broken_links_class = $has_broken_links ? 'wsa-has-broken-links' : '';

        $output .= '<div class="wsa-page ' . $status_class . ' ' . $children_class . ' ' . $no_links_class . ' ' . $no_incoming_links_class . ' ' . $no_outgoing_links_class . ' ' . $broken_links_class . '" data-page-id="' . $page->ID . '" data-slug-path="' . $slug_path . '">';

        $child_pages = get_pages(array(
            'child_of' => $page->ID,
            'post_status' => array('publish', 'draft', 'private', 'pending'),
        ));
        $child_pages = is_array($child_pages) ? $child_pages : array();
        $child_count = count($child_pages);

        $output .= '<div class="wsa-page-info">';
        if ($has_broken_links) {
            $output .= '<span class="wsa-broken-link-indicator"></span>';
        }
        $output .= '<span class="wsa-child-count">' . $child_count . '</span>';
        
        if ($status_label) {
            $output .= '<span class="wsa-status">' . $status_label . '</span>';
        }

        $output .= '<div class="wsa-page-content">';
        $output .= '<div class="wsa-page-title">' . esc_html($page->post_title) . '</div>';
        $output .= '<div class="wsa-page-slug">' . wsa_get_colored_slug($page->ID) . '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '<a href="' . get_edit_post_link($page->ID, '') . '" class="wsa-edit-link" target="_blank"></a>';
        $output .= '</div>';
    }

    return $output;
}

function wsa_page_has_descendants($page_id) {
    $args = array(
        'post_type'      => 'page',
        'posts_per_page' => 1,
        'post_parent'    => $page_id,
        'fields'         => 'ids',
    );

    $children = get_posts($args);

    if (!empty($children)) {
        return true;
    }

    // If no immediate children, check for grandchildren
    $args['post_parent__in'] = get_posts(array(
        'post_type'      => 'page',
        'posts_per_page' => -1,
        'post_parent'    => $page_id,
        'fields'         => 'ids',
    ));

    if (!empty($args['post_parent__in'])) {
        $grandchildren = get_posts($args);
        return !empty($grandchildren);
    }

    return false;
}

function wsa_page_has_links($page_id) {
    $content = get_post_field('post_content', $page_id);
    preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"/', $content, $matches);
    
    return !empty($matches[1]);
}

function wsa_page_has_no_links($page_id) {
    $content = get_post_field('post_content', $page_id);
    preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"/', $content, $matches);
    
    return empty($matches[1]);
}
function wsa_page_has_incoming_links($page_id) {
    $incoming_links = wsa_get_all_incoming_links();
    $page_url = wsa_trim_internal_link(get_permalink($page_id));
    return isset($incoming_links[$page_url]);
}
function wsa_page_has_no_outgoing_links($page_id) {
    $content = get_post_field('post_content', $page_id);
    preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"/', $content, $matches);
    
    if (empty($matches[1])) {
        return true; // No links found
    }
    
    foreach ($matches[1] as $link) {
        if (wsa_is_internal_link($link) || !wsa_is_internal_link($link)) {
            return false; // Found at least one internal or external link
        }
    }
    
    return true; // No internal or external links found
}

function wsa_page_has_children($page_id) {
    $children = get_pages(array(
        'child_of' => $page_id,
        'number' => 1,
    ));
    return !empty($children);
}
function wsa_get_colored_slug($page_id) {
    $page_path = get_page_uri($page_id);
    $path_parts = explode('/', $page_path);
    $colored_parts = array();
    
    $total_parts = count($path_parts);
    foreach ($path_parts as $index => $part) {
        $color_class = ($index === $total_parts - 1) ? 'wsa-slug-last-child' : 'wsa-slug-parent';
        $colored_parts[] = '<span class="' . $color_class . '">' . $part . '/</span>';
    }
    
    return '/' . implode('', $colored_parts);
}

function wsa_get_outgoing_links($page_id) {
    $content = get_post_field('post_content', $page_id);
    $links = array();

    preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"/', $content, $matches);

    if (!empty($matches[1])) {
        $links = array_unique($matches[1]);
        $links = array_map('wsa_trim_internal_link', $links);
    }

    return $links;
}

function wsa_trim_internal_link($link) {
    $home_url = home_url();
    if (strpos($link, $home_url) === 0) {
        return str_replace($home_url, '', $link);
    }
    return $link;
}

function wsa_is_internal_link($link) {
    $home_url = home_url();
    return strpos($link, $home_url) === 0 || strpos($link, '/') === 0;
}

function wsa_ajax_get_children_and_links() {
    if (!isset($_POST['page_id'])) {
        wp_send_json_error('Invalid request');
    }

    $page_id = $_POST['page_id'] === 'home' ? 0 : intval($_POST['page_id']);
    $children_html = $page_id === 0 ? wsa_generate_page_html(0) : wsa_generate_page_html($page_id);
    $links_html = $page_id === 0 ? '' : wsa_generate_links_html($page_id);

    wp_send_json_success(array(
        'children' => $children_html,
        'links' => $links_html
    ));
}
function wsa_generate_links_html($page_id) {
    $outgoing_links = wsa_get_outgoing_links($page_id);
    $broken_links = wsa_check_broken_internal_links($page_id);
    $output = '<div class="wsa-links">';
    
    if (!empty($outgoing_links)) {
        $output .= '<ul>';
        foreach ($outgoing_links as $link) {
            $link_class = wsa_is_internal_link($link) ? 'wsa-internal-link' : 'wsa-external-link';
            $is_broken = false;
            foreach ($broken_links as $broken_link) {
                if ($broken_link['url'] === $link) {
                    $is_broken = true;
                    break;
                }
            }
            $broken_class = $is_broken ? 'wsa-broken-link' : '';
            $output .= '<li class="' . $broken_class . '"><a href="' . esc_url($link) . '" class="' . $link_class . '" target="_blank">' . esc_html($link) . '</a></li>';
        }
        $output .= '</ul>';
    }

    $output .= '</div>';
    return $output;
}

function wsa_ajax_get_all_incoming_links() {
    $incoming_links = wsa_get_all_incoming_links();
    wp_send_json_success($incoming_links);
}

function wsa_get_all_incoming_links() {
    $incoming_links = array();
    $all_pages = get_pages();

    foreach ($all_pages as $page) {
        $content = $page->post_content;
        preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"(?:[^>]*?)>([^<]*)<\/a>/', $content, $matches, PREG_SET_ORDER);

        foreach ($matches as $match) {
            $link_url = wsa_trim_internal_link($match[1]);
            $anchor_text = $match[2];

            $target_page = get_page_by_path(ltrim($link_url, '/'));
            if ($target_page) {
                $target_url = wsa_trim_internal_link(get_permalink($target_page->ID));
                if (!isset($incoming_links[$target_url])) {
                    $incoming_links[$target_url] = array(
                        'title' => $target_page->post_title,
                        'page_id' => $target_page->ID,
                        'links' => array()
                    );
                }
                $incoming_links[$target_url]['links'][] = array(
                    'anchor' => $anchor_text,
                    'url' => wsa_trim_internal_link(get_permalink($page->ID)),
                    'page_id' => $page->ID
                );
            }
        }
    }

    return $incoming_links;
}
function wsa_generate_site_structure() {
    $structure = array(
        'csv' => "TYPE,FOLDER,TITLE,SLUG,Publishing Status,Internal Links,External Links,Broken Links\n",
        'html' => ""
    );

    // Pages
    $pages = get_pages(array('sort_column' => 'menu_order,post_title', 'post_status' => 'publish,draft,private,pending'));
    $page_tree = array();
    foreach ($pages as $page) {
        $parent_id = $page->post_parent ?: 'pages';
        if (!isset($page_tree[$parent_id])) {
            $page_tree[$parent_id] = array();
        }
        $page_tree[$parent_id][] = $page;
    }

    // Posts
    $posts = get_posts(array('posts_per_page' => -1, 'post_type' => 'post', 'post_status' => 'publish,draft,private,pending'));

    // Custom Post Types
    $custom_post_types = get_post_types(array('_builtin' => false), 'objects');
    $cpt_posts = array();
    foreach ($custom_post_types as $cpt => $cpt_object) {
        $cpt_posts[$cpt] = get_posts(array('posts_per_page' => -1, 'post_type' => $cpt, 'post_status' => 'publish,draft,private,pending'));
    }

    // Recursive function to build the structure
    function build_structure($parent_id, $page_tree, $depth = 0, $type = 'Page') {
        $output = array('csv' => '', 'html' => '');
        if (isset($page_tree[$parent_id])) {
            foreach ($page_tree[$parent_id] as $page) {
                $title = str_replace('"', '""', $page->post_title); // Escape double quotes for CSV
                $slug = get_page_uri($page->ID);
                $indent = str_repeat("\t", $depth);
                
                list($status_class, $status_label, $full_status) = get_status_info($page);
                // Get links information
            $internal_links = 0;
            $external_links = 0;
            $broken_links = 0;
            $content = $page->post_content;
            preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"/', $content, $matches);
            if (!empty($matches[1])) {
                foreach ($matches[1] as $link) {
                    if (wsa_is_internal_link($link)) {
                        $internal_links++;
                        if (!get_page_by_path(ltrim(wsa_trim_internal_link($link), '/'))) {
                            $broken_links++;
                        }
                    } else {
                        $external_links++;
                    }
                }
            }
                
            if (isset($page_tree[$page->ID])) {
                // This is a folder
                $output['csv'] .= "{$type},{$indent}{$title},,{$full_status},{$internal_links},{$external_links},{$broken_links}\n";
                    $output['html'] .= "<div class='wsa-tree-item {$status_class}' data-id='{$page->ID}' data-parent='{$parent_id}' style='padding-left: " . (($depth + 1) * 20) . "px;'>";
                    $output['html'] .= "<span class='wsa-toggle'>+</span> <strong class='wsa-folder'>" . strtoupper(htmlspecialchars($title)) . "</strong> ";
                    if ($status_label) {
                        $output['html'] .= "<span class='wsa-status'>{$status_label}</span>";
                    }
                    $output['html'] .= "<br><span class='wsa-slug'>/{$slug}</span></div>";
                    $output['html'] .= "<div class='wsa-children' style='display: none;'>";
                    $child_output = build_structure($page->ID, $page_tree, $depth + 1, $type);
                    $output['csv'] .= $child_output['csv'];
                    $output['html'] .= $child_output['html'];
                    $output['html'] .= "</div>";
                } else {
                    // This is a page
                    $output['csv'] .= "{$type},{$indent},\"{$title}\",/{$slug},{$full_status},{$internal_links},{$external_links},{$broken_links}\n";
                    $output['html'] .= "<div class='wsa-tree-item {$status_class}' data-id='{$page->ID}' data-parent='{$parent_id}' style='padding-left: " . (($depth + 1) * 20) . "px;'>";
                    $output['html'] .= "<span class='wsa-toggle-placeholder'>&nbsp;&nbsp;</span> <strong>" . htmlspecialchars($title) . "</strong> ";
                    if ($status_label) {
                        $output['html'] .= "<span class='wsa-status'>{$status_label}</span>";
                    }
                    $output['html'] .= "<br><span class='wsa-slug'>/{$slug}</span></div>";
                }
            }
        }
        return $output;
    }

    // Build Pages structure
    $structure['html'] .= "<div class='wsa-tree-item' data-id='pages' data-parent='root'><span class='wsa-toggle'>+</span> <strong class='wsa-folder'>PAGES</strong></div>";
    $structure['html'] .= "<div class='wsa-children' style='display: none;'>";
    $built_structure = build_structure('pages', $page_tree);
    $structure['csv'] .= $built_structure['csv'];
    $structure['html'] .= $built_structure['html'];
    $structure['html'] .= "</div>";

    // Build Posts structure
    $structure['html'] .= "<div class='wsa-tree-item' data-id='posts' data-parent='root'><span class='wsa-toggle'>+</span> <strong class='wsa-folder'>POSTS</strong></div>";
    $structure['html'] .= "<div class='wsa-children' style='display: none;'>";
    foreach ($posts as $post) {
        $title = str_replace('"', '""', $post->post_title);
        $slug = get_post_field('post_name', $post->ID);
        list($status_class, $status_label, $full_status) = get_status_info($post);
        $structure['csv'] .= "Post,,\"{$title}\",/{$slug},{$full_status}\n";
        $structure['html'] .= "<div class='wsa-tree-item {$status_class}' data-id='{$post->ID}' data-parent='posts' style='padding-left: 20px;'>";
        $structure['html'] .= "<span class='wsa-toggle-placeholder'>&nbsp;&nbsp;</span> <strong>" . htmlspecialchars($post->post_title) . "</strong> ";
        if ($status_label) {
            $structure['html'] .= "<span class='wsa-status'>{$status_label}</span>";
        }
        $structure['html'] .= "<br><span class='wsa-slug'>/{$slug}</span></div>";
    }
    $structure['html'] .= "</div>";

    // Build Custom Post Types structure
    foreach ($custom_post_types as $cpt => $cpt_object) {
        $structure['html'] .= "<div class='wsa-tree-item' data-id='{$cpt}' data-parent='root'><span class='wsa-toggle'>+</span> <strong class='wsa-folder'>" . strtoupper($cpt_object->labels->name) . "</strong></div>";
        $structure['html'] .= "<div class='wsa-children' style='display: none;'>";
        foreach ($cpt_posts[$cpt] as $cpt_post) {
            $title = str_replace('"', '""', $cpt_post->post_title);
            $slug = get_post_field('post_name', $cpt_post->ID);
            list($status_class, $status_label, $full_status) = get_status_info($cpt_post);
            $structure['csv'] .= "{$cpt_object->labels->singular_name},,\"{$title}\",/{$slug},{$full_status}\n";
            $structure['html'] .= "<div class='wsa-tree-item {$status_class}' data-id='{$cpt_post->ID}' data-parent='{$cpt}' style='padding-left: 20px;'>";
            $structure['html'] .= "<span class='wsa-toggle-placeholder'>&nbsp;&nbsp;</span> <strong>" . htmlspecialchars($cpt_post->post_title) . "</strong> ";
            if ($status_label) {
                $structure['html'] .= "<span class='wsa-status'>{$status_label}</span>";
            }
            $structure['html'] .= "<br><span class='wsa-slug'>/{$slug}</span></div>";
        }
        $structure['html'] .= "</div>";
    }

    return $structure;
}
function wsa_check_broken_internal_links($page_id) {
    $content = get_post_field('post_content', $page_id);
    $broken_links = array();

    preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"[^>]*>(.*?)<\/a>/', $content, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $link = $match[1];
        $link_text = $match[2];
        if (wsa_is_internal_link($link) && strpos($match[0], 'class="wsa-broken-link"') !== false) {
            $broken_links[] = array(
                'url' => wsa_trim_internal_link($link),
                'text' => $link_text
            );
        }
    }

    return $broken_links;
}

function wsa_remove_broken_link_indicators($page_id) {
    $content = get_post_field('post_content', $page_id);
    $links = wsa_get_outgoing_links($page_id);
    $content_updated = false;

    foreach ($links as $link) {
        if (wsa_is_internal_link($link)) {
            $full_link = wsa_get_full_internal_link($link);
            $response = wp_remote_head($full_link);
            if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) != 404) {
                // Remove broken link indicator if the link is now working
                $content = str_replace(
                    '<a href="' . $link . '" class="wsa-broken-link"',
                    '<a href="' . $link . '"',
                    $content
                );
                $content_updated = true;
            }
        }
    }

    if ($content_updated) {
        wp_update_post(array(
            'ID' => $page_id,
            'post_content' => $content
        ));
    }

    // Remove the wsa-broken-link-indicator class from the page
    $page_html = get_post_meta($page_id, '_wsa_page_html', true);
    if ($page_html) {
        $page_html = str_replace('class="wsa-broken-link-indicator"', '', $page_html);
        update_post_meta($page_id, '_wsa_page_html', $page_html);
    }
}

function wsa_ajax_get_site_structure() {    
    $structure = wsa_generate_site_structure();
    wp_send_json_success($structure);
}

add_action('wp_ajax_wsa_get_site_structure', 'wsa_ajax_get_site_structure');

function wsa_ajax_check_broken_links() {
    if (!isset($_POST['page_id'])) {
        wp_send_json_error('Invalid request');
    }

    $page_id = intval($_POST['page_id']);
    $broken_links = wsa_check_broken_internal_links($page_id);

    wp_send_json_success(array(
        'has_broken_links' => !empty($broken_links)
    ));
}

add_action('wp_ajax_wsa_check_broken_links', 'wsa_ajax_check_broken_links');

// Add this new function at the end of the file
function wsa_ajax_scan_404s() {
    if (!isset($_POST['batch'])) {
        wp_send_json_error('Batch parameter missing');
        return;
    }

    $batch_size = 10;
    $current_batch = intval($_POST['batch']);

    // Get total number of pages
    $all_pages = get_pages(array('number' => -1));
    $total_pages = count($all_pages);

    // Calculate the range of pages to process in this batch
    $start = $current_batch * $batch_size;
    $end = min(($current_batch + 1) * $batch_size, $total_pages);

    // Process pages in the current batch
    for ($i = $start; $i < $end; $i++) {
        $page = $all_pages[$i];
        $content = $page->post_content;
        $content_updated = false;

        preg_match_all('/<a\s+(?:[^>]*?\s+)?href="([^"]*)"/', $content, $matches);

        if (!empty($matches[1])) {
            foreach ($matches[1] as $link) {
                if (wsa_is_internal_link($link)) {
                    $full_link = wsa_get_full_internal_link($link);
                    $response = wp_remote_head($full_link, array('timeout' => 5));

                    if (is_wp_error($response) || wp_remote_retrieve_response_code($response) == 404) {
                        // Mark the link as broken if not already marked
                        if (strpos($content, 'href="' . $link . '" class="wsa-broken-link"') === false) {
                            $content = str_replace(
                                'href="' . $link . '"',
                                'href="' . $link . '" class="wsa-broken-link"',
                                $content
                            );
                            $content_updated = true;
                        }
                    } else {
                        // Remove the broken link class if the link is no longer broken
                        if (strpos($content, 'href="' . $link . '" class="wsa-broken-link"') !== false) {
                            $content = str_replace(
                                'href="' . $link . '" class="wsa-broken-link"',
                                'href="' . $link . '"',
                                $content
                            );
                            $content_updated = true;
                        }
                    }
                }
            }
        }

        // Update the page content if changes were made
        if ($content_updated && $content !== $page->post_content) {
            wp_update_post(array(
                'ID' => $page->ID,
                'post_content' => $content
            ));
        }

        // Update the broken link status for the page
        $has_broken_links = strpos($content, 'class="wsa-broken-link"') !== false;
        update_post_meta($page->ID, '_wsa_has_broken_links', $has_broken_links);
    }

    // Calculate progress
    $progress = round(($end / $total_pages) * 100);

    if ($end < $total_pages) {
        // More batches to process
        wp_send_json_success(array(
            'completed' => false,
            'next_batch' => $current_batch + 1,
            'progress' => $progress
        ));
    } else {
        // All batches completed
        wp_send_json_success(array(
            'completed' => true,
            'progress' => 100
        ));
    }
}

add_action('wp_ajax_wsa_scan_404s', 'wsa_ajax_scan_404s');

function wsa_get_full_internal_link($link) {
    if (strpos($link, '/') === 0) {
        return home_url($link);
    }
    return $link;
}
add_action('wp_ajax_wsa_scan_404s', 'wsa_ajax_scan_404s');
