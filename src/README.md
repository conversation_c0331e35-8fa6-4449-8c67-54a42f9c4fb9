# WordPress Content Duplication Detector

This TinyMCE plugin for WordPress detects duplicate content in the post/page editor and provides a visual indicator and interface to manage duplicates.

## Features

### Real-time Duplication Detection
- Automatically scans content in the background for duplicate paragraphs, headings, and list items
- Shows a red indicator dot when duplicates are found
- Displays a count of duplicate content blocks
- Per<PERSON>s checks periodically and on content changes

### Interactive Duplication Management
- Click the "Duplicates" button in the toolbar to open the duplication management interface
- Two view modes: List View and Content View
- List View organizes duplicates by content with expandable sections
- Content View shows the duplicate text with navigation options

### Navigation and Highlighting
- Click "Jump to duplicate" to navigate directly to any instance
- Duplicates are highlighted in the editor for easy identification
- Similar to the Anchors functionality, but specialized for duplicate content

## Installation

1. Include the `contentDuplication.js` file in your plugin directory
2. Enqueue the script in your WordPress admin using:
   ```php
   wp_enqueue_script('slmm-content-duplication', plugins_url('/src/contentDuplication.js', dirname(__FILE__)), array('jquery', 'tinymce'), '1.0.0', true);
   ```
3. Register the plugin with TinyMCE:
   ```php
   // Add to mce_external_plugins filter
   $plugins['content_duplication'] = plugins_url('/src/contentDuplication.js', dirname(__FILE__));
   
   // Add to mce_buttons filter
   array_push($buttons, 'duplication');
   ```

## Usage

1. The plugin runs automatically when the editor loads
2. A red dot appears next to the "Duplicates" button when duplicates are found
3. Click the button to open the duplicate content interface
4. Use the tabs to switch between List and Content views
5. Click on any duplicate instance to jump to it in the editor
6. The duplicate will be highlighted in yellow for easy identification

## Technical Details

The plugin detects duplicates by:
1. Parsing the editor content as HTML
2. Extracting paragraphs, list items, and headings
3. Comparing their text content (ignoring HTML structure)
4. Considering content blocks with 15+ characters (to avoid false positives)

The navigation system works by:
1. Adding a temporary unique ID to the target element
2. Updating the editor content
3. Finding and selecting the element by ID
4. Adding a highlight class
5. Removing the ID and scrolling to the element 