(function($) {
    'use strict';

    var SearchReplace = {
        currentIndex: -1,
        matches: [],
        dialog: null,

        init: function() {
            // Create dialog first so it's ready when shortcut is used
            this.createDialog();
            // Bind shortcuts immediately
            this.bindShortcuts();
            // Initialize TinyMCE integration separately
            this.initTinyMCE();
        },

        bindShortcuts: function() {
            var self = this;
            // Detect OS for correct shortcut display
            var isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
            var shortcutText = isMac ? '⇧⌘F' : 'Shift+Ctrl+F';

            // Bind keyboard shortcut globally
            $(document).on('keydown', function(e) {
                // Check for Cmd+Shift+F (Mac) or Ctrl+Shift+F (Windows)
                if ((isMac ? e.metaKey : e.ctrlKey) && e.shiftKey && e.keyCode === 70) {
                    e.preventDefault();
                    e.stopPropagation();
                    self.showDialog();
                }
            });

            return shortcutText;
        },

        initTinyMCE: function() {
            var self = this;
            var shortcutText = this.bindShortcuts();

            // Add button to editor toolbar if TinyMCE is active
            if (typeof tinymce !== 'undefined') {
                tinymce.PluginManager.add('custom_searchreplace', function(editor) {
                    editor.addButton('searchreplace', {
                        text: 'Find & Replace',
                        tooltip: 'Find and Replace (' + shortcutText + ')',
                        onclick: function() {
                            self.showDialog();
                        }
                    });
                });
            }
        },

        createDialog: function() {
            // Add overlay element
            var overlayHTML = '<div id="search-replace-overlay" style="display:none;"></div>';
            
            var dialogHTML = '\
                <div id="search-replace-dialog" style="display:none;" class="wp-core-ui">\
                    <div class="search-replace-content">\
                        <div class="dialog-header">\
                            <div class="title-group">\
                                <span class="dialog-title">Find & Replace</span>\
                                <span id="match-counter"></span>\
                            </div>\
                            <button type="button" class="close-button" aria-label="Close">&times;</button>\
                        </div>\
                        <input type="text" id="find-text" class="regular-text" placeholder="Find (comma separated for multiple terms)">\
                        <input type="text" id="replace-text" class="regular-text" placeholder="Replace with">\
                        <div class="options-box">\
                            <label><input type="checkbox" id="match-case"> Match case</label>\
                            <label><input type="checkbox" id="whole-word"> Whole words</label>\
                            <label><input type="checkbox" id="multiple-terms"> Multiple terms with one</label>\
                            <label><input type="checkbox" id="swap-mode"> Swap mode</label>\
                        </div>\
                        <div class="button-group">\
                            <button type="button" class="button button-primary" id="find-button">Find</button>\
                            <button type="button" class="button" id="replace-button">Replace</button>\
                            <button type="button" class="button" id="replace-all-button">Replace All</button>\
                            <button type="button" class="button" id="save-favorite-button">Save as Favorite</button>\
                        </div>\
                        <div class="favorites-section">\
                            <div class="favorites-header">\
                                <h3>Favorites</h3>\
                                <button type="button" class="button" id="apply-all-favorites">Apply All Favorites</button>\
                            </div>\
                            <div id="favorites-accordion"></div>\
                        </div>\
                    </div>\
                </div>';

            // Remove any existing dialog and overlay
            $('#search-replace-dialog, #search-replace-overlay').remove();
            
            // Add new elements
            $('body').append(overlayHTML + dialogHTML);

            // Style the dialog and overlay
            $('<style>').text(`
                #search-replace-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.2);
                    z-index: 159998;
                }
                #search-replace-dialog {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: #ffffff;
                    padding: 20px;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 159999;
                    width: 400px;
                    max-width: 90vw;
                }
                .search-replace-content {
                    position: relative;
                }
                .dialog-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                }
                .title-group {
                    display: flex;
                    align-items: flex-end;
                    gap: 8px;
                }
                .dialog-title {
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 1;
                }
                #match-counter {
                    color: #000;
                    font-size: 11px;
                    font-weight: 600;
                    line-height: 1;
                    margin-bottom: 1px;
                }
                .close-button {
                    background: none;
                    border: none;
                    font-size: 24px;
                    line-height: 1;
                    padding: 0;
                    cursor: pointer;
                    color: #666;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                }
                .close-button:hover {
                    background: #f0f0f0;
                    color: #000;
                }
                #search-replace-dialog input[type="text"] {
                    width: 100%;
                    margin-bottom: 10px;
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }
                #search-replace-dialog .options-box {
                    margin: 15px 0;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                }
                #search-replace-dialog .options-box label {
                    margin-right: 15px;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                }
                #search-replace-dialog .button-group {
                    display: flex;
                    gap: 8px;
                }
                #search-replace-dialog .status-group {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                #search-replace-dialog .button {
                    padding: 6px 12px;
                }
                #search-status {
                    color: #666;
                    font-size: 13px;
                }
                .mce-match-marker {
                    background: yellow;
                }
                .mce-match-marker-selected {
                    background: orange;
                }
                .favorites-section {
                    margin-top: 20px;
                    border-top: 1px solid #ddd;
                    padding-top: 15px;
                }
                .favorites-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                }
                .favorites-header h3 {
                    margin: 0;
                }
                #favorites-accordion {
                    max-height: 300px;
                    overflow-y: auto;
                }
                .favorite-item {
                    border: 1px solid #ddd;
                    margin-bottom: 5px;
                    border-radius: 4px;
                }
                .favorite-header {
                    padding: 8px;
                    background: #f5f5f5;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .favorite-content {
                    padding: 8px;
                    border-top: 1px solid #ddd;
                    display: none;
                }
                .favorite-actions {
                    display: flex;
                    gap: 8px;
                }
                .favorite-delete {
                    color: #dc3232;
                    cursor: pointer;
                }
            `).appendTo('head');

            this.bindDialogEvents();
            this.loadFavorites();
        },

        showDialog: function() {
            $('#search-replace-overlay').show();
            $('#search-replace-dialog').show();
            $('#find-text').focus();
        },

        hideDialog: function() {
            $('#search-replace-overlay').hide();
            $('#search-replace-dialog').hide();
        },

        bindDialogEvents: function() {
            var self = this;

            // Close button click
            $('.close-button').on('click', function() {
                self.hideDialog();
            });

            // Escape key press
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('#search-replace-dialog').is(':visible')) {
                    self.hideDialog();
                }
            });

            // Close dialog when clicking overlay
            $('#search-replace-overlay').on('click', function() {
                self.hideDialog();
            });

            $('#find-button').on('click', function() {
                self.find();
            });

            $('#replace-button').on('click', function() {
                self.replace();
            });

            $('#replace-all-button').on('click', function() {
                self.replaceAll();
            });

            $('#save-favorite-button').on('click', function() {
                self.saveFavorite();
            });

            $('#apply-all-favorites').on('click', function() {
                self.applyAllFavorites();
            });

            // Make modes mutually exclusive
            $('#multiple-terms').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#swap-mode').prop('checked', false);
                }
            });

            $('#swap-mode').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#multiple-terms').prop('checked', false);
                }
            });

            // Handle Enter key in find input
            $('#find-text').on('keypress', function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    self.find();
                }
            });

            // Delegate events for dynamic favorite items
            $('#favorites-accordion').on('click', '.favorite-header', function() {
                $(this).next('.favorite-content').slideToggle();
            });

            $('#favorites-accordion').on('click', '.favorite-delete', function(e) {
                e.stopPropagation();
                self.deleteFavorite($(this).closest('.favorite-item').data('id'));
            });

            $('#favorites-accordion').on('click', '.favorite-apply', function(e) {
                e.stopPropagation();
                self.applyFavorite($(this).closest('.favorite-item').data('id'));
            });
        },

        find: function() {
            var editor = tinymce.activeEditor;
            if (!editor) return;

            var searchText = $('#find-text').val();
            if (!searchText) {
                $('#match-counter').text('');
                return;
            }

            var matchCase = $('#match-case').is(':checked');
            var wholeWord = $('#whole-word').is(':checked');
            var multipleTerms = $('#multiple-terms').is(':checked');
            var swapMode = $('#swap-mode').is(':checked');

            // Clear previous matches
            this.clearMatches(editor);

            var terms = (multipleTerms || swapMode) ? searchText.split(',').map(function(term) {
                return term.trim();
            }).filter(Boolean) : [searchText];

            var totalMatches = 0;
            var termCounts = {};

            terms.forEach(function(term, index) {
                // Escape special regex characters
                term = term.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&');
                
                // Add word boundaries if whole word search
                if (wholeWord) {
                    term = '\\b' + term + '\\b';
                }

                var regex = new RegExp(term, matchCase ? 'g' : 'gi');
                var matches = this.markMatches(editor, regex, index);
                totalMatches += matches;
                termCounts[index] = matches;
            }.bind(this));

            if (totalMatches > 0) {
                this.currentIndex = 0;
                this.selectMatch(editor, this.currentIndex);
                this.updateMatchCounter(0, totalMatches);
            } else {
                $('#match-counter').text('No matches');
            }
        },

        markMatches: function(editor, regex, termIndex) {
            var text = editor.getContent({format: 'text'});
            var matches = text.match(regex);
            if (!matches) return 0;

            var content = editor.getContent();
            var newContent = content.replace(regex, function(match) {
                return '<span class="mce-match-marker" data-term-index="' + termIndex + '">' + match + '</span>';
            });

            editor.setContent(newContent);
            return matches.length;
        },

        selectMatch: function(editor, index) {
            var markers = editor.dom.select('.mce-match-marker');
            if (markers.length === 0) return;

            // Remove previous selection
            editor.dom.removeClass(markers, 'mce-match-marker-selected');

            // Add selection to current match
            editor.dom.addClass(markers[index], 'mce-match-marker-selected');

            // Scroll match into view
            markers[index].scrollIntoView(false);
        },

        updateMatchCounter: function(current, total) {
            if (total > 0) {
                $('#match-counter').text((current + 1) + ' of ' + total).show();
            } else {
                $('#match-counter').text('').hide();
            }
        },

        replace: function() {
            var editor = tinymce.activeEditor;
            if (!editor) return;

            var replaceText = $('#replace-text').val();
            var swapMode = $('#swap-mode').is(':checked');
            var multipleTerms = $('#multiple-terms').is(':checked');
            
            if (swapMode) {
                // Swap mode - replace with corresponding term
                var replaceTerms = replaceText.split(',').map(function(term) {
                    return term.trim();
                }).filter(Boolean);

                var markers = editor.dom.select('.mce-match-marker-selected');
                if (markers.length > 0) {
                    var marker = markers[0];
                    var termIndex = parseInt(marker.getAttribute('data-term-index'));
                    var replaceTerm = replaceTerms[termIndex] || replaceTerms[0];
                    
                    editor.dom.setHTML(marker, replaceTerm);
                    editor.dom.removeClass(marker, 'mce-match-marker mce-match-marker-selected');
                }
            } else {
                // Regular replace or multiple terms with one
                var markers = editor.dom.select('.mce-match-marker-selected');
                if (markers.length > 0) {
                    var marker = markers[0];
                    editor.dom.setHTML(marker, replaceText);
                    editor.dom.removeClass(marker, 'mce-match-marker mce-match-marker-selected');
                }
            }

            // Move to next match
            this.currentIndex++;
            var totalMatches = editor.dom.select('.mce-match-marker').length;
            if (this.currentIndex >= totalMatches) {
                this.currentIndex = 0;
            }
            this.selectMatch(editor, this.currentIndex);
            this.updateMatchCounter(this.currentIndex, totalMatches);
        },

        replaceAll: function() {
            var editor = tinymce.activeEditor;
            if (!editor) return;

            var replaceText = $('#replace-text').val();
            var swapMode = $('#swap-mode').is(':checked');
            var multipleTerms = $('#multiple-terms').is(':checked');

            var totalReplaced = 0;
            var termCounts = {};
            var replaceTerms = replaceText.split(',').map(function(term) {
                return term.trim();
            }).filter(Boolean);

            if (swapMode) {
                // Swap mode - replace with corresponding terms
                var markers = editor.dom.select('.mce-match-marker');
                markers.forEach(function(marker) {
                    var termIndex = parseInt(marker.getAttribute('data-term-index'));
                    var replaceTerm = replaceTerms[termIndex] || replaceTerms[0];
                    editor.dom.setHTML(marker, replaceTerm);
                    editor.dom.removeClass(marker, 'mce-match-marker mce-match-marker-selected');
                    totalReplaced++;
                    termCounts[termIndex] = (termCounts[termIndex] || 0) + 1;
                });

                // Show swap summary in counter
                var swapSummary = Object.keys(termCounts).map(function(index) {
                    var originalTerm = $('#find-text').val().split(',')[index].trim();
                    var replaceTerm = replaceTerms[index] || replaceTerms[0];
                    return originalTerm + ' → ' + replaceTerm + ': ' + termCounts[index];
                }).join(', ');
                
                $('#match-counter').text(totalReplaced + ' replaced (' + swapSummary + ')');
            } else {
                // Regular replace or multiple terms with one
                var markers = editor.dom.select('.mce-match-marker');
                markers.forEach(function(marker) {
                    editor.dom.setHTML(marker, replaceText);
                    editor.dom.removeClass(marker, 'mce-match-marker mce-match-marker-selected');
                    totalReplaced++;
                });
                $('#match-counter').text(totalReplaced + ' replaced');
            }

            this.currentIndex = -1;
        },

        clearMatches: function(editor) {
            var markers = editor.dom.select('.mce-match-marker');
            markers.forEach(function(marker) {
                editor.dom.setHTML(marker, marker.innerHTML);
                editor.dom.remove(marker, true);
            });
            this.currentIndex = -1;
        },

        saveFavorite: function() {
            var findText = $('#find-text').val();
            var replaceText = $('#replace-text').val();
            
            if (!findText || !replaceText) return;

            var favorites = this.getFavorites();
            var newFavorite = {
                id: Date.now(),
                find: findText,
                replace: replaceText,
                matchCase: $('#match-case').is(':checked'),
                wholeWord: $('#whole-word').is(':checked'),
                multipleTerms: $('#multiple-terms').is(':checked'),
                swapMode: $('#swap-mode').is(':checked')
            };

            favorites.push(newFavorite);
            this.saveFavoritesToStorage(favorites);
            this.renderFavorites();
        },

        getFavorites: function() {
            var favorites = localStorage.getItem('searchReplaceFavorites');
            return favorites ? JSON.parse(favorites) : [];
        },

        saveFavoritesToStorage: function(favorites) {
            localStorage.setItem('searchReplaceFavorites', JSON.stringify(favorites));
        },

        loadFavorites: function() {
            this.renderFavorites();
        },

        renderFavorites: function() {
            var favorites = this.getFavorites();
            var $accordion = $('#favorites-accordion');
            $accordion.empty();

            favorites.forEach(function(favorite) {
                var itemHtml = '\
                    <div class="favorite-item" data-id="' + favorite.id + '">\
                        <div class="favorite-header">\
                            <span>' + this.escapeHtml(favorite.find) + ' → ' + this.escapeHtml(favorite.replace) + '</span>\
                            <div class="favorite-actions">\
                                <button class="button button-small favorite-apply">Apply</button>\
                                <span class="favorite-delete dashicons dashicons-trash"></span>\
                            </div>\
                        </div>\
                        <div class="favorite-content">\
                            <div>Match case: ' + (favorite.matchCase ? 'Yes' : 'No') + '</div>\
                            <div>Whole words: ' + (favorite.wholeWord ? 'Yes' : 'No') + '</div>\
                            <div>Multiple terms: ' + (favorite.multipleTerms ? 'Yes' : 'No') + '</div>\
                            <div>Swap mode: ' + (favorite.swapMode ? 'Yes' : 'No') + '</div>\
                        </div>\
                    </div>';
                $accordion.append(itemHtml);
            }.bind(this));
        },

        deleteFavorite: function(id) {
            var favorites = this.getFavorites().filter(function(favorite) {
                return favorite.id !== id;
            });
            this.saveFavoritesToStorage(favorites);
            this.renderFavorites();
        },

        applyFavorite: function(id) {
            var favorite = this.getFavorites().find(function(f) {
                return f.id === id;
            });

            if (!favorite) return;

            $('#find-text').val(favorite.find);
            $('#replace-text').val(favorite.replace);
            $('#match-case').prop('checked', favorite.matchCase);
            $('#whole-word').prop('checked', favorite.wholeWord);
            $('#multiple-terms').prop('checked', favorite.multipleTerms);
            $('#swap-mode').prop('checked', favorite.swapMode);

            // First find the matches
            this.find();
            
            // Then perform replace all
            if ($('.mce-match-marker').length > 0) {
                this.replaceAll();
            }
        },

        applyAllFavorites: function() {
            var favorites = this.getFavorites();
            favorites.forEach(function(favorite) {
                this.applyFavorite(favorite.id);
            }.bind(this));
        },

        escapeHtml: function(text) {
            var div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        SearchReplace.init();
    });

})(jQuery); 