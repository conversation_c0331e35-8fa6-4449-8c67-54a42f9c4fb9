/**
 * SEO Overview Meta Box CSS
 */

/* Container styling - more compact */
.slmm-seo-overview-content {
    min-height: 80px;
    padding: 0;
    margin: -6px -12px -12px -12px;
    position: relative;
}

/* Table styling - more compact */
.seo-overview-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.seo-overview-table tr {
    border-bottom: 1px solid #f0f0f0;
}

.seo-overview-table tr:last-child {
    border-bottom: none;
}

/* Reduce font size for headers by 25% */
.seo-overview-table th {
    text-align: left;
    padding: 5px 8px;
    font-weight: 600;
    color: #1d2327;
    font-size: 10px; /* 25% smaller than 13px */
    background-color: #f8f9fa;
}

/* More compact cell padding */
.seo-overview-table td {
    text-align: right;
    padding: 5px 8px;
    vertical-align: middle;
    height: 30px; /* Reduced row height */
}

/* Reduce indicator size by 25% */
.status-indicator {
    display: inline-block;
    font-size: 24px; /* Base size */
    line-height: 0;
    vertical-align: middle;
    margin-right: 2px;
}

/* Make indicators 100% larger than original size */
.status-indicator.large-indicator {
    font-size: 48px; /* Double the original size of 24px */
    line-height: 0;
    display: inline-block;
    vertical-align: middle;
}

/* Status colors */
.status-green {
    color: #00a32a;
}

.status-red {
    color: #d63638;
}

.status-orange {
    color: #f56e28; /* Brighter orange for visibility */
}

/* Reduce count text size by 25% */
.status-count {
    font-weight: 600;
    vertical-align: middle;
    font-size: 10px; /* 25% smaller than 13px */
}

/* Reduce large count size by 25% */
.status-count.large-count {
    font-size: 26px; /* 25% smaller than 26px */
    font-weight: 600;
}

/* Last update text styling */
.last-update {
    display: none;
}

/* Loading state */
.slmm-seo-overview-content .loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* Header style updated without auto-update text */
.seo-overview-header {
    display: none;
}

/* Publish status text styling */
.status-indicator.large-indicator:not(.status-green):not(.status-red):not(.status-orange) {
    font-size: 26px;
    line-height: 1;
    font-weight: 600;
}

/* Value highlighting - for word count */
.value-highlight {
    font-size: 20px;
    font-weight: 600;
    display: inline-block;
    vertical-align: middle;
    color: #333; 
}

/* Status count styling - for duplicates and hidden divs */
.status-count.large-count {
    font-size: 20px !important;
    font-weight: 600 !important;
    display: inline-block;
    vertical-align: middle;
    color: #333;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .seo-overview-table th,
    .seo-overview-table td {
        padding: 5px 6px;
    }
}

/* Refresh button styling */
#refresh-seo-overview {
    display: none;
}

/* Spinning animation for refresh button */
@keyframes spin {
    /* empty keyframes to null out any animation */
    0%, 100% { transform: none; }
}

#refresh-seo-overview.spinning .dashicons {
    animation: none;
}

/* Section styling - more compact */
.seo-overview-section {
    margin-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 5px;
    overflow: hidden; /* Prevent margin collapse */
}

.seo-overview-section:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

/* Section title - smaller */
.section-title {
    margin: 0 0 3px 0;
    padding: 3px 8px;
    background-color: #f8f9fa;
    font-size: 18px; /* 25% smaller than 13px */
    font-weight: 800;
    color: #1d2327;
    border-bottom: 1px solid #f0f0f0;
}

/* Loading spinner styling - more compact */
.seo-overview-loading .loading-spinner {
    text-align: center;
    padding: 20px 0;
}

.seo-overview-loading .spinner {
    float: none;
    margin: 0 auto;
    visibility: visible;
}

/* Publish status styling - larger for Published status */
.publish-status {
    font-size: 20px;
    line-height: 1;
    font-weight: 600;
}

.publish-status.large-text {
    font-size: 24px; /* Normal size */
}

/* Make Published status 25% larger */
.publish-status.status-green {
    font-size: 30px; /* 25% larger than 24px */
}

/* Ensure publish status text is properly styled */
.publish-status.status-orange {
    color: #f56e28;
    font-weight: 600;
}

/* Table heading styling - larger font */
.heading-larger {
    font-size: 12px !important; /* Larger than the default 10px */
    font-weight: 700 !important;
}

/* Status count size - 25% smaller than previous */
.status-count.large-count {
    font-size: 15px !important; /* 25% smaller with !important */
    font-weight: 600 !important;
}

/* Publish status text - 25% smaller than previous */
.publish-status.large-text {
    font-size: 15px !important; /* 25% smaller with !important */
    font-weight: 600 !important;
}

/* Status circle alignment */
.status-green.large-indicator, .status-red.large-indicator, .status-orange.large-indicator {
    position: relative;
    top: -2px;
}

/* Larger count text as requested */
.status-count.large-count {
    font-size: 26px; /* 100% larger */
    font-weight: 600;
}

/* Make Published status 25% larger */
.publish-status.published-larger {
    font-size: 30px !important; /* 25% larger than the 24px base */
    font-weight: 700 !important;
}

/* Consistent number styling across all metrics */
.value-highlight, .status-count.large-count {
    font-size: 20px;
    font-weight: 600;
    display: inline-block;
    vertical-align: middle;
    color: #333;
}

/* Ensure top margin is removed for the first section */
.seo-overview-section:first-child {
    margin-top: 0;
} 