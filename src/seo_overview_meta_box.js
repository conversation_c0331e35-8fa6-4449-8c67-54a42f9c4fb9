/**
 * SEO Overview Meta Box
 * 
 * Provides a comprehensive overview of SEO metrics in a meta box
 * that can be toggled from Screen Options and repositioned.
 */
(function($) {
    'use strict';

    var SEOOverviewMetaBox = {
        init: function() {
            // Initialize listeners after the page has loaded
            $(document).ready(function() {
                SEOOverviewMetaBox.setupEventListeners();
                
                // Wait for the page to be fully loaded, including all scripts
                $(window).on('load', function() {
                    // Allow a short delay for all indicator plugins to initialize
                    setTimeout(function() {
                        console.log('SEO Overview: Performing initial update after full page load');
                        SEOOverviewMetaBox.updateMetaBoxContent();
                    }, 800);
                });
                
                // Also set up a one-time update after a short delay to catch any late-initializing elements
                setTimeout(function() {
                    console.log('SEO Overview: Performing fallback initial update');
                    SEOOverviewMetaBox.updateMetaBoxContent();
                }, 1500);
            });
        },

        setupEventListeners: function() {
            // Update the meta box when WordPress saves content (heartbeat)
            $(document).on('heartbeat-tick', function() {
                SEOOverviewMetaBox.updateMetaBoxContent();
            });

            // Real-time updates when tinymce content changes
            if (typeof tinymce !== 'undefined') {
                tinymce.on('AddEditor', function(e) {
                    e.editor.on('change keyup', SEOOverviewMetaBox.debounce(function() {
                        SEOOverviewMetaBox.updateMetaBoxContent();
                    }, 1000));
                    
                    // Check for schema in editor content changes
                    e.editor.on('change keyup', SEOOverviewMetaBox.debounce(function() {
                        var content = e.editor.getContent();
                        var hasSchema = content.includes('application/ld+json') || 
                                       content.includes('itemtype="http://schema.org') ||
                                       content.includes('itemtype="https://schema.org');
                        
                        // Only update if schema is present in the content
                        if (hasSchema) {
                            var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                            $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
                        }
                    }, 1000));
                    
                    // Also update once when editor is fully initialized
                    e.editor.on('init', function() {
                        setTimeout(function() {
                            console.log('SEO Overview: Update triggered by TinyMCE init');
                            SEOOverviewMetaBox.updateMetaBoxContent();
                        }, 500);
                    });
                });
            }

            // Monitor for when key indicators are ready
            var checkForIndicators = function() {
                var areReady = false;
                
                // Check if links indicator is ready
                var linksButton = $('#broken-links-button, .mce-i-link').first();
                if (linksButton.length && linksButton.find('.broken-links-indicator').length) {
                    areReady = true;
                }
                
                // Check if duplicates indicator is ready
                var dupButton = $('#content-duplication');
                if (dupButton.length && dupButton.find('.duplication-indicator').length) {
                    areReady = true;
                }
                
                // Check if hidden div indicator is ready
                var hiddenDiv = $('.hidden-div-indicator');
                if (hiddenDiv.length) {
                    areReady = true;
                }
                
                // If indicators are ready, update the SEO overview
                if (areReady) {
                    console.log('SEO Overview: Key indicators detected, triggering update');
                    SEOOverviewMetaBox.updateMetaBoxContent();
                    clearInterval(indicatorCheckInterval);
                }
            };
            
            // Check every 200ms for indicators, up to 5 seconds
            var indicatorCheckInterval = setInterval(checkForIndicators, 200);
            setTimeout(function() {
                clearInterval(indicatorCheckInterval);
            }, 5000);

            // Update when publish button is clicked
            $('#publish').on('click', function() {
                setTimeout(function() {
                    SEOOverviewMetaBox.updateMetaBoxContent();
                }, 1000);
            });
            
            // Update on editor content change from text mode
            $('#content').on('input change', SEOOverviewMetaBox.debounce(function() {
                SEOOverviewMetaBox.updateMetaBoxContent();
            }, 1000));
            
            // Update on featured image change
            $('#postimagediv').on('DOMSubtreeModified', SEOOverviewMetaBox.debounce(function() {
                // Update just the featured image section
                var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
            }, 500));
            
            // Monitor the "Remove featured image" link click
            $(document).on('click', '#remove-post-thumbnail', function() {
                setTimeout(function() {
                    var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                    $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
                }, 100);
            });
            
            // Monitor the "Set featured image" link click
            $(document).on('click', '#set-post-thumbnail', function() {
                // Need a longer timeout as the media uploader takes time to close
                var checkInterval = setInterval(function() {
                    if ($('#postimagediv .inside img').length > 0) {
                        var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                        $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
                        clearInterval(checkInterval);
                    }
                }, 500);
                
                // Clear interval after 10 seconds in case user cancels
                setTimeout(function() {
                    clearInterval(checkInterval);
                }, 10000);
            });
            
            // Monitor the schema textarea changes
            $(document).on('input change', '#slmm_insert_schema', function() {
                // Update schema status when the schema textarea changes
                var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
            });
            
            // Monitor schema validation and rescan buttons
            $(document).on('click', '#validate-schema, #rescan-schema', function() {
                // Update after a short delay to allow for validation/rescan
                setTimeout(function() {
                    var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                    $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
                }, 500);
            });
            
            // Directly monitor the WordPress word count element
            $('#wp-word-count .word-count').on('DOMSubtreeModified', function() {
                // Immediately update when word count changes
                var wordCount = $(this).text();
                $('.seo-overview-table .value-highlight').text(wordCount);
            });
            
            // Use MutationObserver to monitor the duplicate content indicator
            if (window.MutationObserver) {
                // Observe the duplication indicator in ContentDuplication.js
                var duplicateButton = document.querySelector('#content-duplication');
                if (duplicateButton) {
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList' || mutation.type === 'attributes') {
                                // Update just the duplicates status without reloading everything
                                var duplicatesStatus = SEOOverviewMetaBox.getDuplicatesStatus();
                                $('.seo-overview-table tr:contains("Duplicates:")').find('td').html(duplicatesStatus);
                            }
                        });
                    });
                    
                    observer.observe(duplicateButton, { 
                        childList: true,
                        attributes: true,
                        subtree: true
                    });
                }
                
                // Observe the featured image indicator in the preflight checklist
                var featuredImageIndicator = document.querySelector('.featured-image-indicator');
                if (featuredImageIndicator) {
                    var featuredObserver = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                                // Update just the featured image status without reloading everything
                                var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
                                $('.seo-overview-table tr:contains("Featured Image:")').find('td').html(featuredImageStatus);
                            }
                        });
                    });
                    
                    featuredObserver.observe(featuredImageIndicator, { 
                        attributes: true,
                        attributeFilter: ['style', 'class']
                    });
                }
                
                // Observe the schema indicator in the preflight checklist
                var schemaIndicator = document.querySelector('.schema-indicator');
                if (schemaIndicator) {
                    var schemaObserver = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                                // Update just the schema status without reloading everything
                                var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();
                                $('.seo-overview-table tr:contains("Schema:")').find('td').html(schemaStatus);
                                console.log("Schema status updated via mutation observer");
                            }
                        });
                    });
                    
                    schemaObserver.observe(schemaIndicator, { 
                        attributes: true,
                        attributeFilter: ['style', 'class']
                    });
                }
            }
            
            // Use MutationObserver to ensure we catch all word count updates
            if (window.MutationObserver) {
                var wordCountSpan = document.querySelector('#wp-word-count .word-count');
                if (wordCountSpan) {
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'characterData' || mutation.type === 'childList') {
                                var wordCount = $(wordCountSpan).text();
                                $('.seo-overview-table .value-highlight').text(wordCount);
                            }
                        });
                    });
                    
                    observer.observe(wordCountSpan, { 
                        characterData: true,
                        childList: true,
                        subtree: true
                    });
                }
            }
            
            // Set up a periodic refresh
            setInterval(function() {
                SEOOverviewMetaBox.updateMetaBoxContent();
            }, 5000); // Update every 5 seconds
        },

        updateMetaBoxContent: function() {
            // Get the values from various sources
            var wordCount = SEOOverviewMetaBox.getWordCount();
            var publishStatus = SEOOverviewMetaBox.getPublishStatus();
            var featuredImageStatus = SEOOverviewMetaBox.getFeaturedImageStatus();
            var duplicatesStatus = SEOOverviewMetaBox.getDuplicatesStatus();
            var linksStatus = SEOOverviewMetaBox.getLinksStatus();
            var hiddenDivStatus = SEOOverviewMetaBox.getHiddenDivStatus();
            var schemaStatus = SEOOverviewMetaBox.getSchemaStatus();

            // Check if checklist is enabled (from localized script)
            var checklistEnabled = typeof slmmSeoOverview !== 'undefined' && slmmSeoOverview.checklistEnabled;

            // Build the SEO elements table rows
            var seoElementsRows =
                '<tr><th class="heading-larger">Featured Image:</th><td>' + featuredImageStatus + '</td></tr>' +
                '<tr><th class="heading-larger">Links Status:</th><td>' + linksStatus + '</td></tr>';
            if (checklistEnabled) {
                seoElementsRows += '<tr><th class="heading-larger">Schema:</th><td>' + schemaStatus + '</td></tr>';
            }
            seoElementsRows += '<tr><th class="heading-larger"></th><td>' + publishStatus + '</td></tr>';

            // Update the meta box content with a more compact layout
            $('#seo-overview-content').html(
                // Content metrics section - more compact
                '<div class="seo-overview-section">' +
                    '<h2 class="section-title"><strong>CONTENT</strong></h2>' +
                    '<table class="seo-overview-table">' +
                        '<tr><th class="heading-larger">Word Count:</th><td><span class="value-highlight">' + wordCount + '</span></td></tr>' +
                        '<tr><th class="heading-larger">Duplicates:</th><td>' + duplicatesStatus + '</td></tr>' +
                        '<tr><th class="heading-larger">Hidden Divs:</th><td>' + hiddenDivStatus + '</td></tr>' +
                    '</table>' +
                '</div>' +
                // SEO elements section - more compact
                '<div class="seo-overview-section">' +
                    '<h2 class="section-title"><strong>SEO ELEMENTS</strong></h2>' +
                    '<table class="seo-overview-table">' +
                        seoElementsRows +
                    '</table>' +
                '</div>'
            );
        },

        getWordCount: function() {
            // Get word count directly from the WordPress word count element's span
            var wordCountSpan = $('#wp-word-count .word-count');
            if (wordCountSpan.length) {
                return wordCountSpan.text();
            }
            return '0';
        },

        getPublishStatus: function() {
            // Get publish status from WordPress post_status
            var status = $('#post_status').val() || $('#original_post_status').val() || 'draft';
            var statusText, statusClass;
            
            // Also check the visible status text which might be more accurate
            var visibleStatus = $('#post-status-display').text() || $('.misc-pub-post-status #post-status-display').text() || $('label[for="post_status"]').text() || $('.misc-pub-section').text() || '';
            
            // Check specifically for "Privately Published" text
            if (visibleStatus.toLowerCase().indexOf('privately published') !== -1 || 
                visibleStatus.toLowerCase().indexOf('private') !== -1) {
                status = 'private';
            }
            
            // Add another check for status in the publish panel
            var publishPanelStatus = $('.edit-post-post-status__row:contains("Status")').text() || 
                                    $('span:contains("Status:")').text() || 
                                    $('.components-panel:contains("Status")').text() || '';
            if (publishPanelStatus.toLowerCase().indexOf('private') !== -1) {
                status = 'private';
            }

            switch (status) {
                case 'publish':
                    statusText = 'Published';
                    statusClass = 'status-green';
                    break;
                case 'private':
                    statusText = 'Private';
                    statusClass = 'status-orange';
                    break;
                default:
                    statusText = 'Draft';
                    statusClass = 'status-red';
                    break;
            }

            // All statuses use the same larger size for consistency
            return '<span class="publish-status ' + statusClass + ' large-text published-larger">' + statusText + '</span>';
        },

        getFeaturedImageStatus: function() {
            // First check for featured image indicator from the preflight checklist
            var preflightIndicator = $('.featured-image-indicator');
            if (preflightIndicator.length) {
                // Get color from the preflight checklist indicator
                var bgColor = preflightIndicator.css('background-color');
                if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                    // Green indicator means featured image exists
                    var isValid = (bgColor === 'green' || bgColor === 'rgb(0, 128, 0)' || 
                                  bgColor.indexOf('0, 128, 0') > -1 || bgColor.indexOf('rgb(0, 163, 42)') > -1 || 
                                  bgColor.indexOf('0, 163, 42') > -1 || bgColor.indexOf('#00a32a') > -1);
                    
                    return SEOOverviewMetaBox.getStatusIndicator(isValid);
                }
            }
            
            // Fallback method: Check if featured image exists directly
            var hasFeaturedImage = $('#postimagediv .inside img').length > 0;
            
            // If using the featured image indicator from Content Checklist, get color from there
            var checklistIndicator = $('.featured-image-indicator');
            if (checklistIndicator.length) {
                var bgColor = checklistIndicator.css('background-color');
                if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                    // Get the status from Content Checklist's indicator
                    var isValid = (bgColor.indexOf('0, 163, 42') > -1 || bgColor.indexOf('0,163,42') > -1 || 
                                  bgColor.indexOf('#00a32a') > -1 || bgColor.indexOf('rgb(0, 163, 42)') > -1);
                    
                    // Use the Content Checklist value
                    return SEOOverviewMetaBox.getStatusIndicator(isValid);
                }
            }
            
            // Use our own check as a fallback
            return SEOOverviewMetaBox.getStatusIndicator(hasFeaturedImage);
        },

        getDuplicatesStatus: function() {
            // Directly check the duplicate content indicator from ContentDuplication.js
            var duplicateButton = $('#content-duplication');
            var duplicateIndicator = duplicateButton.find('.duplication-indicator');
            var duplicateCount = duplicateButton.find('.duplication-count').text();
            
            // Check if the indicator is active (has duplicates)
            var hasDuplicates = false;
            if (duplicateIndicator.length) {
                hasDuplicates = duplicateIndicator.hasClass('active');
                
                // Extract count number from the 'x #' format if present
                if (duplicateCount) {
                    var countMatch = duplicateCount.match(/x\s*(\d+)/);
                    if (countMatch && countMatch[1]) {
                        duplicateCount = countMatch[1];
                    }
                }
                
                // Indicator is inverse - GREEN means NO duplicates
                return SEOOverviewMetaBox.getStatusIndicator(!hasDuplicates, hasDuplicates ? duplicateCount : '');
            }
            
            // Fallback to previous behavior if no indicator found
            var duplicateElements = $('.duplicate-content-indicator, .duplicate-indicator');
            if (duplicateElements.length) {
                // Get the first element with a background color set
                duplicateElements.each(function() {
                    var bgColor = $(this).css('background-color');
                    if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                        // Red indicator means duplicates found
                        if (bgColor.indexOf('214, 54, 56') > -1 || bgColor.indexOf('rgb(214, 54, 56)') > -1 || 
                            bgColor.indexOf('#d63638') > -1 || bgColor.indexOf('red') > -1) {
                            hasDuplicates = true;
                            return false; // Exit the loop after finding a duplicate
                        }
                    }
                });
            } else if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                // Fallback: Check for duplicate paragraphs in content
                var content = tinymce.activeEditor.getContent();
                
                // Extract all paragraphs
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                
                var paragraphs = [];
                var p = tempDiv.getElementsByTagName('p');
                for (var i = 0; i < p.length; i++) {
                    var text = p[i].textContent.trim();
                    if (text.length > 20) { // Only consider substantial paragraphs
                        paragraphs.push(text);
                    }
                }
                
                // Check for duplicate paragraphs
                var duplicates = {};
                paragraphs.forEach(function(para) {
                    if (duplicates[para]) {
                        duplicates[para]++;
                    } else {
                        duplicates[para] = 1;
                    }
                });
                
                var dupCount = 0;
                for (var key in duplicates) {
                    if (duplicates[key] > 1) {
                        dupCount += duplicates[key] - 1; // Count only extra occurrences
                    }
                }
                
                hasDuplicates = dupCount > 0;
                duplicateCount = dupCount > 0 ? dupCount : '';
            }
            
            // GREEN means no duplicates, RED means duplicates
            return SEOOverviewMetaBox.getStatusIndicator(!hasDuplicates, hasDuplicates ? duplicateCount : '');
        },

        getLinksStatus: function() {
            // Get links status from BrokenLinkDetector
            var brokenLinksIndicator = $('.broken-links-indicator');
            var isValid = brokenLinksIndicator.hasClass('valid');
            var count = '';
            
            // Try to get count from the broken-links-count element
            var countEl = $('.broken-links-count');
            if (countEl.length && countEl.text().trim() !== '') {
                // Extract just the number if it already has an 'x' prefix
                var countText = countEl.text().trim();
                var countMatch = countText.match(/x\s*(\d+)/);
                if (countMatch && countMatch[1]) {
                    count = countMatch[1];
                } else {
                    count = countText;
                }
            }
            
            return SEOOverviewMetaBox.getStatusIndicator(isValid, count);
        },

        getHiddenDivStatus: function() {
            // Get hidden div count based on the server-side count_hidden_divs function
            var hiddenDivCount = 0;
            
            // First try to directly read the value from the hidden div button in the toolbar
            // This ensures we mirror exactly what's shown in the editor toolbar
            var hiddenDivIndicator = $('.hidden-div-indicator');
            var hiddenDivCount = $('.hidden-div-count');
            
            if (hiddenDivIndicator.length) {
                // Check the background color style
                var bgColor = hiddenDivIndicator.css('background-color');
                var bgStyle = hiddenDivIndicator.attr('style');
                
                // Default to no hidden divs
                var hasHiddenDivs = false;
                var displayCount = '';
                
                // Check if the indicator has a green background (has hidden divs)
                if (bgColor && (bgColor === 'green' || bgColor === 'rgb(0, 128, 0)' || bgColor.indexOf('0, 128, 0') > -1)) {
                    hasHiddenDivs = true;
                    
                    // Get count from the counter element if it exists and has text
                    if (hiddenDivCount.length && hiddenDivCount.text().trim() !== '') {
                        var countMatch = hiddenDivCount.text().match(/x\s*(\d+)/);
                        if (countMatch && countMatch[1]) {
                            displayCount = countMatch[1];
                        } else {
                            displayCount = hiddenDivCount.text().trim();
                        }
                    }
                }
                
                return SEOOverviewMetaBox.getStatusIndicator(hasHiddenDivs, displayCount);
            }
            
            // If we can't find the indicator element, fall back to content scanning
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                var content = tinymce.activeEditor.getContent();
                
                // Check for hidden divs in the content
                var regex = /style\s*=\s*["'][^"']*display\s*:\s*none[^"']*["']/gi;
                var matches = content.match(regex);
                hiddenDivCount = matches ? matches.length : 0;
                
                // IMPORTANT: In seo_text_helper_2_3.php, hidden div indicator is GREEN when divs exist
                // So we need to invert our logic - hasHiddenDivs should be true when count > 0
                var hasHiddenDivs = hiddenDivCount > 0;
                var displayCount = hiddenDivCount > 1 ? hiddenDivCount : '';
                
                return SEOOverviewMetaBox.getStatusIndicator(hasHiddenDivs, displayCount);
            }
            
            // Default to no hidden divs if we can't determine
            return SEOOverviewMetaBox.getStatusIndicator(false, '');
        },

        getSchemaStatus: function() {
            // Check for schema markup from preflight checklist/Content Checklist
            var schemaIndicator = $('.schema-indicator');
            if (schemaIndicator.length) {
                var bgColor = schemaIndicator.css('background-color');
                if (bgColor && bgColor !== 'transparent' && bgColor !== 'rgba(0, 0, 0, 0)') {
                    // Get the status from schema indicator
                    var isValid = (bgColor.indexOf('0, 163, 42') > -1 || bgColor.indexOf('0,163,42') > -1 || 
                                  bgColor.indexOf('#00a32a') > -1 || bgColor.indexOf('rgb(0, 163, 42)') > -1 ||
                                  bgColor === 'green' || bgColor === 'rgb(0, 128, 0)' || bgColor.indexOf('0, 128, 0') > -1);
                    var isWarning = (bgColor.indexOf('245, 110, 40') > -1 || bgColor.indexOf('245,110,40') > -1 || 
                                    bgColor.indexOf('#f56e28') > -1 || bgColor.indexOf('orange') > -1 ||
                                    bgColor === 'orange' || bgColor === 'rgb(255, 165, 0)' || bgColor.indexOf('255, 165, 0') > -1);
                    
                    // Return status with right color for schema - use large-indicator class
                    if (isValid) {
                        return '<span class="status-indicator status-green large-indicator">•</span>';
                    } else if (isWarning) {
                        return '<span class="status-indicator status-orange large-indicator">•</span>';
                    } else {
                        return '<span class="status-indicator status-red large-indicator">•</span>';
                    }
                }
            }
            
            // Fallback: Check the insert schema metabox
            var schemaTextarea = $('#slmm_insert_schema');
            if (schemaTextarea.length && schemaTextarea.val().trim() !== '') {
                return '<span class="status-indicator status-green large-indicator">•</span>';
            }
            
            // Fallback: Simple detection of schema in content
            var hasSchema = false;
            
            // Look for schema in TinyMCE
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                var content = tinymce.activeEditor.getContent();
                hasSchema = content.includes('application/ld+json') || 
                           content.includes('itemtype="http://schema.org') ||
                           content.includes('itemtype="https://schema.org');
            }
            
            return SEOOverviewMetaBox.getStatusIndicator(hasSchema);
        },

        getStatusIndicator: function(isPositive, count) {
            var statusClass = isPositive ? 'status-green' : 'status-red';
            var statusHtml = '<span class="status-indicator ' + statusClass + ' large-indicator">•</span>';
            
            if (count !== undefined && count !== '') {
                statusHtml += ' <span class="status-count large-count">x ' + count + '</span>';
            }
            
            return statusHtml;
        },

        // Utility function to limit how often a function is called
        debounce: function(func, wait) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        }
    };

    // Initialize the module
    SEOOverviewMetaBox.init();

})(jQuery); 