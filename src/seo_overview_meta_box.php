<?php
/**
 * SEO Overview Meta Box
 * 
 * Registers a meta box in the WordPress editor that provides a comprehensive SEO overview.
 * The meta box is togglable from Screen Options and can be moved around the editor.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_SEO_Overview_Meta_Box {
    
    /**
     * Initialize the class and set up hooks
     */
    public function __construct() {
        // Check if SEO Overview is enabled in the settings
        $options = get_option('chatgpt_generator_options', array());
        $seo_overview_enabled = isset($options['enable_seo_overview']) ? (bool)$options['enable_seo_overview'] : true;
        
        // Only register the meta box and related hooks if enabled
        if ($seo_overview_enabled) {
            add_action('add_meta_boxes', array($this, 'register_meta_boxes'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
            add_action('wp_ajax_get_hidden_div_count', array($this, 'get_hidden_div_count_ajax'));
        }
    }
    
    /**
     * Register the meta box
     */
    public function register_meta_boxes() {
        // Get allowed post types from settings
        $allowed_types = get_option('seo_overview_post_types', array('post', 'page'));
        $post_types = is_array($allowed_types) ? $allowed_types : array('post', 'page');
        // Allow other post types via filter
        $post_types = apply_filters('slmm_seo_overview_post_types', $post_types);
        foreach ($post_types as $post_type) {
            add_meta_box(
                'slmm-seo-overview',
                __('SEO Overview', 'slmm-seo-bundle'),
                array($this, 'render_meta_box'),
                $post_type,
                'side',
                'default', // Use default priority so it can be toggled in screen options
                null // No callback args
            );
            
            // Add to list of screen options that can be toggled
            add_filter('hidden_meta_boxes', array($this, 'ensure_meta_box_visible'), 10, 3);
        }
    }
    
    /**
     * Ensure the meta box is visible by default
     */
    public function ensure_meta_box_visible($hidden, $screen, $use_defaults) {
        // Make sure our meta box is not hidden when starting fresh
        if ($use_defaults) {
            $hidden = array_diff($hidden, array('slmm-seo-overview'));
        }
        return $hidden;
    }
    
    /**
     * Render the meta box content
     */
    public function render_meta_box($post) {
        // Output the meta box container
        echo '<div id="seo-overview-content" class="slmm-seo-overview-content">';
        echo '<div class="seo-overview-loading">';
        echo '<div class="seo-overview-header">';
        echo '<span class="last-update">Loading...</span>';
        echo '<button type="button" id="refresh-seo-overview" class="button button-small" style="float:right;" disabled>';
        echo '<span class="dashicons dashicons-update" style="font-size:14px;line-height:1.4;"></span></button>';
        echo '</div>';
        echo '<div class="loading-spinner"><span class="spinner is-active"></span></div>';
        echo '</div>';
        echo '</div>';
        
        // Add nonce for security
        wp_nonce_field('slmm_seo_overview_nonce', 'slmm_seo_overview_nonce');
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts($hook) {
        // Only load on post editing screens
        if (!in_array($hook, array('post.php', 'post-new.php'))) {
            return;
        }
        
        // Ensure Dashicons are loaded
        wp_enqueue_style('dashicons');
        
        // Register and enqueue the script
        wp_enqueue_script(
            'slmm-seo-overview',
            plugin_dir_url(dirname(__FILE__)) . 'src/seo_overview_meta_box.js',
            array('jquery'),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'src/seo_overview_meta_box.js'),
            true
        );
        
        // Register and enqueue the stylesheet
        wp_enqueue_style(
            'slmm-seo-overview',
            plugin_dir_url(dirname(__FILE__)) . 'src/seo_overview_meta_box.css',
            array(),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'src/seo_overview_meta_box.css')
        );
        
        // Check if checklist is enabled
        $options = get_option('chatgpt_generator_options', array());
        $checklist_enabled = isset($options['enable_checklist']) ? (bool)$options['enable_checklist'] : false;
        
        // Pass AJAX URL and checklist enabled status to script
        wp_localize_script('slmm-seo-overview', 'slmmSeoOverview', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'checklistEnabled' => $checklist_enabled
        ));
    }
    
    /**
     * Ajax handler for getting hidden div count
     */
    public function get_hidden_div_count_ajax() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'slmm_seo_overview_nonce')) {
            wp_send_json_error('Invalid nonce');
        }
        
        if (!isset($_POST['content'])) {
            wp_send_json_error('No content provided');
        }
        
        $content = $_POST['content'];
        $count = $this->count_hidden_divs($content);
        
        wp_send_json_success(array('count' => $count));
    }
    
    /**
     * Count hidden divs in content
     */
    private function count_hidden_divs($content) {
        $pattern = '/<div[^>]*style\s*=\s*["\'][^"\']*display\s*:\s*none[^"\']*["\'][^>]*>/i';
        preg_match_all($pattern, $content, $matches);
        return count($matches[0]);
    }
}

// Initialize the class
new SLMM_SEO_Overview_Meta_Box(); 